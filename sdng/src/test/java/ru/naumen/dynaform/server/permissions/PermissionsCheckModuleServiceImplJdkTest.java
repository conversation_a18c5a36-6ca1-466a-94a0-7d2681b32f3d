package ru.naumen.dynaform.server.permissions;

import static org.mockito.Mockito.mock;

import java.util.function.Consumer;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 *
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
public class PermissionsCheckModuleServiceImplJdkTest
{
    private SuperUser superUser;

    @Before
    public void setUp()
    {
        superUser = new SuperUser();
    }

    @Test
    public void testDisable()
    {
        staticMock(v ->
        {
            superUser.setAlias("employee$111");

            PermissionsCheckModuleUtil.disableModule();

            Assert.assertNull(superUser.getAlias());
        });
    }

    @Test
    public void testEnable()
    {
        staticMock(v ->
        {
            String emplUuid = "employee$111";
            PermissionsCheckModuleUtil.enableModule(emplUuid);

            Assert.assertEquals(emplUuid, superUser.getAlias());
        });
    }

    @Test(expected = FxException.class)
    public void testEnableIfNotSuperuser()
    {
        staticMock(v ->
                PermissionsCheckModuleUtil.enableModule("employee$111"), mock(Employee.class));
    }

    @Test
    public void testIsOffIfCurrentUserIsEmployee()
    {
        staticMock(v ->
                Assert.assertFalse(PermissionsCheckModuleUtil.isOn()), mock(Employee.class));
    }

    @Test
    public void testIsOffIfSUWithoutAlias()
    {
        staticMock(v -> Assert.assertFalse(PermissionsCheckModuleUtil.isOn()));
    }

    @Test
    public void testIsOnIfSUWithAlias()
    {
        staticMock(v ->
        {
            superUser.setAlias("employee$111");
            Assert.assertTrue(PermissionsCheckModuleUtil.isOn());
        });
    }

    private void staticMock(Consumer<Void> consumer)
    {
        staticMock(consumer, superUser);
    }

    private static void staticMock(Consumer<Void> consumer, Object employeeUser)
    {
        try (MockedStatic<CurrentEmployeeContext> currentEmployeeContext = Mockito.mockStatic(
                CurrentEmployeeContext.class))
        {
            currentEmployeeContext.when(CurrentEmployeeContext::getCurrentUserPrincipal).thenReturn(employeeUser);
            consumer.accept(null);
        }
    }
}
