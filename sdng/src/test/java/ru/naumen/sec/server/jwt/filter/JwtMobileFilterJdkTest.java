package ru.naumen.sec.server.jwt.filter;

import static org.apache.http.HttpStatus.SC_OK;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.AdditionalMatchers.not;
import static org.mockito.Mockito.*;
import static org.springframework.security.core.authority.AuthorityUtils.createAuthorityList;
import static ru.naumen.sec.server.jwt.Audience.REST_MOBILE;

import java.io.IOException;
import java.util.Date;
import java.util.HashSet;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import io.jsonwebtoken.Jwts.SIG;
import io.jsonwebtoken.security.Jwks.CRV;
import jakarta.servlet.ServletException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoServiceImpl;
import ru.naumen.sec.server.filters.jwt.JwtMobileFilter;
import ru.naumen.sec.server.jwt.Audience;
import ru.naumen.sec.server.jwt.JwtAuthUtil;
import ru.naumen.sec.server.jwt.JwtConfigProperties;
import ru.naumen.sec.server.jwt.JwtMobileAuthenticationFailureHandler;
import ru.naumen.sec.server.jwt.JwtMobileAuthenticationSuccessHandler;
import ru.naumen.sec.server.jwt.JwtMobileEntryPoint;
import ru.naumen.sec.server.jwt.mobile.JwtAuthentication;
import ru.naumen.sec.server.jwt.mobile.JwtMobileConfigurationProperties;
import ru.naumen.sec.server.jwt.mobile.UserDetailsServiceFacade;
import ru.naumen.sec.server.jwt.mobile.storage.JwtTokenStorageService;
import ru.naumen.sec.server.jwt.mobile.utils.JwtTokenExtractor;
import ru.naumen.sec.server.jwt.mobile.utils.JwtTokenGenerator;
import ru.naumen.sec.server.security.PasswordGuessingProtectionService;
import ru.naumen.sec.server.security.UserBlockedException;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.employee.EmployeeUser;
import ru.naumen.sec.server.utils.CoreSecurityUtils;
import ru.naumen.sec.server.utils.ResponseUtils;

/**
 * Тесты для {@link JwtMobileFilter}
 *
 * <AUTHOR>
 * @since April 9, 2019
 */
@RunWith(MockitoJUnitRunner.class)
public class JwtMobileFilterJdkTest
{
    private static final int ACCESS_TOKEN_MAX_AGE = 10;
    private static final int REFRESH_TOKEN_MAX_AGE = 20;

    private static final String JWT_MOBILE_ENTRY_POINT = "/jwt-mobile";
    private static final String JWT_MOBILE_ENDPOINT = "/services/rest-mobile/v9.1/user/settings";
    private static final String NOT_SECURED_ENDPOINT = "/login";

    private static final String USER_UUID = "employee$123456";
    private static final String LOGIN = "as";
    private static final String PASSWORD = "password";
    private static final String ROLE = "ROLE_1";

    @Mock
    private CurrentEmployeeContext currentEmployeeContext;
    @Mock
    private CoreSecurityUtils securityUtils;
    @Mock
    private JwtConfigProperties jwtConfigProperties;
    @Mock
    private ConfigurationProperties configurationProperties;
    @Mock
    private JwtAuthUtil jwtAuthUtil;
    @Mock
    private ScriptService scriptService;
    @Mock
    private PasswordGuessingProtectionService pgpService;
    @Mock
    private UserDetailsServiceFacade userDetailsServiceFacade;
    @Mock
    private JwtTokenGenerator tokenGenerator;
    @Mock
    private JwtTokenExtractor tokenExtractor;
    @Mock
    private JwtTokenStorageService tokenStorageService;
    @Mock
    private JwtMobileConfigurationProperties configuration;

    @InjectMocks
    private JwtMobileFilter jwtMobileFilter;
    @InjectMocks
    private MockHttpServletResponse response;
    @InjectMocks
    private MockHttpServletRequest request;

    private final MockFilterChain chain = new MockFilterChain();
    private final ResponseUtils responseUtils = new ResponseUtils();

    private Employee employee;
    private String accessToken;
    private String refreshToken;

    /**
     * Проверить, что при запросе к сервлету выдачи токенов без токена фильтр будет пропущен
     *
     * Проверить, что пользователя разлогинило 1 раз
     * Проверить, что статус 200
     */
    @Test
    public void testBasicAuthorization() throws Exception
    {
        request.setRequestURI(JWT_MOBILE_ENDPOINT);
        request.setServletPath(JWT_MOBILE_ENDPOINT);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_OK, response.getStatus());
    }

    /**
     * Проверить, что c валидным JWT Refresh токеном, пользователь аутетифицируется и
     * запрос пойдет дальше по цепочке фильтров
     *
     * Проверить, что пользователя разлогинило 1 раз
     * Проверить, что статус ок
     * Проверить, что пользователь залогинен
     */
    @Test
    public void getJwtWithCorrectRefreshToken() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENTRY_POINT);
        request.setServletPath(JWT_MOBILE_ENTRY_POINT);
        request.addHeader("Authorization", "Bearer " + refreshToken);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_OK, response.getStatus());
        assertEquals(LOGIN, (SecurityContextHolder.getContext().getAuthentication().getName()));
    }

    /**
     * Проверить, что с невалидным JWT Refresh токеном, пользователь не аутентифицируется и его не пустет дальше по
     * цепочки фильтра
     *
     * Проверить, что пользователя разлогинило 1 раз
     * Проверить, что код 401
     * Проверить, что текущий аутентифицированный пользователь - null
     */
    @Test
    public void getJwtWithIncorrectRefreshToken() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENTRY_POINT);
        request.setServletPath(JWT_MOBILE_ENTRY_POINT);
        request.addHeader("Authorization", "Bearer invalidRefreshToken");

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_UNAUTHORIZED, response.getStatus());
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    /**
     * Проверить, что с валидным JWT Access токеном можно достучаться к мобильному АПИ
     *
     * Проверить, что пользователя разлогинило 1 раз
     * Проверить, что статус 200
     * Проверить, что текущий аутентифицрованный пользователь - владелец токена
     */
    @Test
    public void accessMobileEndpointWithCorrectAccessToken() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENDPOINT);
        request.setServletPath(JWT_MOBILE_ENDPOINT);
        request.addHeader("Authorization", "Bearer " + accessToken);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_OK, response.getStatus());
        assertEquals(LOGIN, (SecurityContextHolder.getContext().getAuthentication().getName()));
    }

    /**
     * Проверить, что с невалидным JWT Access токеном невозможно достучаться к мобильному АПИ
     *
     * Проверить, что пользователя разлогинило 1 раз
     * Проверить, что статус 401
     * Проверить, что текущий аутентифицированный пользователь - null
     */
    @Test
    public void accessMobileEndpointWithIncorrectAccessToken() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENDPOINT);
        request.setServletPath(JWT_MOBILE_ENDPOINT);
        request.addHeader("Authorization", "Bearer bad:token");

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_UNAUTHORIZED, response.getStatus());
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    /**
     * Проверить, что с валидным JWT Refresh токеном невозможно достучаться к мобильному АПИ
     *
     * Проверить, что статус 401
     */
    @Test
    public void accessMobileEndpointWithCorrectRefreshToken() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENDPOINT);
        request.setServletPath(JWT_MOBILE_ENDPOINT);
        request.addHeader("Authorization", "Bearer " + refreshToken);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_UNAUTHORIZED, response.getStatus());
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    /**
     * Проверить, что без токена невозможно достучаться к мобильному АПИ
     *
     * Проверить, что статус 401
     */
    @Test
    public void accessMobileEndpointWithoutAnyTokens() throws IOException, ServletException
    {
        request.setRequestURI(JWT_MOBILE_ENDPOINT);
        request.setServletPath(JWT_MOBILE_ENDPOINT);
        request.addHeader("Authorization", "Bearer " + refreshToken);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_UNAUTHORIZED, response.getStatus());
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    /**
     * Проверить, что при запросе к незащищеному ресурсу без токена фильтр будет пропушен
     *
     * Проверить, что пользователя ни разу не разлогинило
     * Проверить, что статус 200
     */
    @Test
    public void accessToNotSecuredEndpoint() throws IOException, ServletException
    {
        request.setRequestURI(NOT_SECURED_ENDPOINT);
        request.setServletPath(NOT_SECURED_ENDPOINT);

        jwtMobileFilter.doFilter(request, response, chain);

        assertEquals(SC_OK, response.getStatus());
    }

    /**
     * Подготовка к тестам
     */
    @Before
    public void setup()
    {
        SecurityContextHolder.clearContext();
        initEmployee();

        lenient().when(currentEmployeeContext.getCurrentEmployee()).thenReturn(employee);
        lenient().when(scriptService.isModuleHasMethod(anyString(), anyString())).thenReturn(false);
        when(userDetailsServiceFacade.loadUserByUsername(eq(LOGIN)))
                .thenReturn(new EmployeeUser(employee, new HashSet<>(createAuthorityList(ROLE))));
        when(securityUtils.getAuthentication())
                .thenReturn(new UsernamePasswordAuthenticationToken(LOGIN, PASSWORD, createAuthorityList(ROLE)));

        lenient().doThrow(UserBlockedException.class)
                .when(pgpService)
                .checkIfBlocked(not(eq(new EmployeeUser(employee))));

        initJwtAuthUtils();
        initJwtMobileFilter();
    }

    private void initEmployee()
    {
        employee = new Employee();
        employee.setLogin(LOGIN);
        employee.setPassword(PASSWORD);
        ReflectionTestUtils.setField(employee, "uuid", USER_UUID);
    }

    private void initJwtAuthUtils()
    {
        ReflectionTestUtils.setField(jwtAuthUtil, "configurationProperties", configurationProperties);
        ReflectionTestUtils.setField(jwtAuthUtil, "currentEmployeeContext", currentEmployeeContext);
        ReflectionTestUtils.setField(jwtAuthUtil, "scriptService", scriptService);
        ReflectionTestUtils.setField(jwtAuthUtil, "jwtConfigProperties", jwtConfigProperties);
        ReflectionTestUtils.setField(jwtAuthUtil, "signingKeyPair", CRV.Ed25519.keyPair().build());
        ReflectionTestUtils.setField(jwtAuthUtil, "encryptionKeyPair", SIG.ES512.keyPair().build());

        when(jwtAuthUtil.isBearerHeader(anyString())).thenCallRealMethod();
        when(jwtAuthUtil.parseToken(anyString())).thenCallRealMethod();
        when(jwtAuthUtil.isAudienceAllowed(any(), anyString())).thenCallRealMethod();
        lenient().when(jwtAuthUtil.getAccessTokenCookie(any())).thenCallRealMethod();
        when(jwtAuthUtil.generateToken(
                anyString(), any(), anyMap(), any(Date.class),
                any(Date.class), anyString(), any(Audience.class)
        )).thenCallRealMethod();
        when(tokenExtractor.extractToken(any())).thenCallRealMethod();
        when(tokenExtractor.parseToken(any())).thenCallRealMethod();
        when(tokenGenerator.generateTokens(any(), any(), any(), anyInt(), anyInt(), any())).thenCallRealMethod();
        when(tokenStorageService.hasToken(anyString())).thenReturn(true);
        when(tokenStorageService.createToken(any())).thenAnswer(
                (InvocationOnMock invocation) -> invocation.getArguments()[0]);
        when(configuration.isEnabled()).thenReturn(true);
        ReflectionTestUtils.setField(tokenExtractor, "jwtAuthUtil", jwtAuthUtil);
        ReflectionTestUtils.setField(tokenGenerator, "jwtAuthUtil", jwtAuthUtil);
        ReflectionTestUtils.setField(tokenGenerator, "jwtTokenStorageService", tokenStorageService);

        generateTokens();
    }

    private void initJwtMobileFilter()
    {
        final JwtMobileEntryPoint jwtMobileEntryPoint = new JwtMobileEntryPoint(responseUtils);
        ReflectionTestUtils.setField(jwtMobileFilter, "securityUtils", securityUtils);
        ReflectionTestUtils.setField(jwtMobileFilter, "userDetailsServiceFacade", userDetailsServiceFacade);
        ReflectionTestUtils.setField(jwtMobileFilter, "tokenExtractor", tokenExtractor);
        ReflectionTestUtils.setField(jwtMobileFilter, "authUtil", jwtAuthUtil);

        jwtMobileFilter.setSuccessHandler(new JwtMobileAuthenticationSuccessHandler());
        jwtMobileFilter.setFailureHandler(new JwtMobileAuthenticationFailureHandler(
                mock(MessageFacade.class), mock(UserInfoServiceImpl.class), jwtMobileEntryPoint));
    }

    private void generateTokens()
    {
        EmployeeUser principal = mock(EmployeeUser.class);
        when(principal.getUUID()).thenReturn(USER_UUID);

        JwtAuthentication auth = tokenGenerator.generateTokens(LOGIN, principal, null, ACCESS_TOKEN_MAX_AGE,
                REFRESH_TOKEN_MAX_AGE, REST_MOBILE);
        accessToken = auth.getAccessToken().getToken();
        refreshToken = auth.getRefreshToken().getToken();
    }
}