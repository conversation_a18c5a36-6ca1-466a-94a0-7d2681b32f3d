package ru.naumen.dynaform.server.permissions;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.dynaform.shared.permissions.GetPermissionsCheckModuleInfoAction;
import ru.naumen.dynaform.shared.permissions.PermissionsCheckModuleInfo;
import ru.naumen.sec.server.autorize.AuthorizationInfo;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.HasAlias;

/**
 *
 * <AUTHOR>
 * @since 14 июня 2016 г.
 */
@Component
public class GetPermissionsCheckModuleInfoActionHandler extends
        TransactionalReadActionHandler<GetPermissionsCheckModuleInfoAction, SimpleResult<PermissionsCheckModuleInfo>>
{
    private final CurrentEmployeeContext currentEmployeeContext;
    private final PermissionsCheckModuleService permissionsCheckModuleService;

    @Inject
    public GetPermissionsCheckModuleInfoActionHandler(
            CurrentEmployeeContext currentEmployeeContext,
            PermissionsCheckModuleService permissionsCheckModuleService)
    {
        this.currentEmployeeContext = currentEmployeeContext;
        this.permissionsCheckModuleService = permissionsCheckModuleService;
    }

    @Override
    public SimpleResult<PermissionsCheckModuleInfo> executeInTransaction(GetPermissionsCheckModuleInfoAction action,
            ExecutionContext context) throws DispatchException
    {
        Employee employee = currentEmployeeContext.getCurrentEmployee();

        if (employee == null || !(CurrentEmployeeContext.getCurrentUserPrincipal() instanceof HasAlias alias
                                  && alias.getAlias() != null))
        {
            return new SimpleResult<>(new PermissionsCheckModuleInfo());
        }

        PermissionsCheckModuleInfo info = new PermissionsCheckModuleInfo()
                .setSelectedUser(permissionsCheckModuleService.getCurrentEmployee(employee))
                .setLicenses(permissionsCheckModuleService.getLicenses(employee))
                .setUserGroups(permissionsCheckModuleService.getGroups(employee));

        if (action.getUuid() == null && action.getFqn() == null)
        {
            return new SimpleResult<>(info);
        }

        AuthorizationInfo authInfo = permissionsCheckModuleService.getAuthInfo(action.getUuid());
        return new SimpleResult<>(info.setProfiles(extractTitles(permissionsCheckModuleService.getProfiles(authInfo)))
                .setRoles(extractTitles(authInfo.getRoles())));
    }

    private static List<String> extractTitles(List<? extends ITitled> items)
    {
        return items.stream()
                .map(ITitled::getTitle)
                .sorted(String.CASE_INSENSITIVE_ORDER)
                .toList();
    }
}
