package ru.naumen.dynaform.server.jsp;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.common.server.config.LoadObjectsConfiguration;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.jsp.JspResourcesBase;
import ru.naumen.core.server.push.PushUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.dynaform.server.permissions.PermissionsCheckModuleUtil;

/**
 * Ресурсы для jsp модуля operator
 *
 * <AUTHOR>
 * @since 10.06.22
 */
@Component
public class OperatorJspResources extends JspResourcesBase
{
    private final LoadObjectsConfiguration loadObjectsConfiguration;

    @Inject
    public OperatorJspResources(LoadObjectsConfiguration loadObjectsConfiguration)
    {
        this.loadObjectsConfiguration = loadObjectsConfiguration;
    }

    @Override
    public String getCode()
    {
        return Constants.OPERATOR_ALIAS;
    }

    @Override
    public String getDefaultJS(HttpServletRequest request)
    {
        String url = (String)request.getSession().getAttribute(Constants.DOWNLOAD);
        request.getSession().removeAttribute(Constants.DOWNLOAD);

        String script =
                "var permissionsCheckModule = {" +
                "isOn:" + PermissionsCheckModuleUtil.isOn() +
                "};" +

                "var loadAllPropForObjects = " + loadObjectsConfiguration.isLoadAllPropForObjects() + ";" +
                "var loadAllPropForResolveClient = " + loadObjectsConfiguration.isLoadAllPropForResolveClient()
                + ";";

        if (StringUtilities.isNotEmpty(url))
        {
            script += "var downloadHref = \"" + url + SEMICOLON;
        }

        // Очистить push_uids в localStorage при входе пользователя в систему
        if (PushUtils.isNeedClearLocalStorage())
        {
            script += "localStorage.removeItem('" + Constants.Push.LOCAL_STORAGE_PUSH_UUIDS_KEY + "');";
        }

        return script;
    }
}
