package ru.naumen.dynaform.server.permissions;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.FxException;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.HasAlias;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 * Утилитарные методы по проверке прав.
 *
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
public class PermissionsCheckModuleUtil
{
    private PermissionsCheckModuleUtil()
    {
    }

    public static void disableModule()
    {
        setAlias(null);
    }

    public static void enableModule(String uuid)
    {
        setAlias(uuid);
    }

    public static boolean isOn()
    {
        if (CurrentEmployeeContext.getCurrentUserPrincipal() instanceof SuperUser)
        {
            return getCurrentAlias() != null;
        }
        return false;
    }

    private static HasAlias checkIsSuperUser(@Nullable Object principal)
    {
        if (!(principal instanceof HasAlias superUser))
        {
            throw new FxException("Must be a superuser");
        }
        return superUser;
    }

    private static String getCurrentAlias()
    {
        HasAlias superUser = checkIsSuperUser(CurrentEmployeeContext.getCurrentUserPrincipal());
        return superUser.getAlias();
    }

    private static void setAlias(@Nullable String uuid)
    {
        HasAlias superUser = checkIsSuperUser(CurrentEmployeeContext.getCurrentUserPrincipal());
        superUser.setAlias(uuid);
    }
}
