package ru.naumen.dynaform.server.permissions;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.dynaform.shared.permissions.EnablePermissionsCheckModuleAction;
import ru.naumen.sec.server.session.SessionRegistryNau;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;

/**
 * Класс активации для модуля проверки прав
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
@Component
public class EnablePermissionsCheckModuleActionHandler
        extends BasePermissionsCheckModuleActionHandler<EnablePermissionsCheckModuleAction>
{

    @Inject
    public EnablePermissionsCheckModuleActionHandler(
            ApplicationEventPublisher eventPublisher,
            SessionRegistryNau sessionRegistryNau,
            CurrentEmployeeContext currentEmployeeContext,
            EmployeeUserDetailsService employeeUserDetailService)
    {
        super(eventPublisher, sessionRegistryNau, currentEmployeeContext, employeeUserDetailService);
    }

    @Override
    public EmptyResult executeInTransaction(EnablePermissionsCheckModuleAction action, ExecutionContext context)
            throws DispatchException
    {
        publishSessionInvalidatedEvent();

        PermissionsCheckModuleUtil.enableModule(action.getEmployeeUuid());
        return EmptyResult.instance;
    }
}
