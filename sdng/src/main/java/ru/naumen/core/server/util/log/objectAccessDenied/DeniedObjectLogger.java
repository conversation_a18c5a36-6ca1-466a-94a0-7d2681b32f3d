package ru.naumen.core.server.util.log.objectAccessDenied;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Логгер неуспешных попыток доступа к объекту
 * Помещен в отдельный пакет, чтобы не нарушать обратную совместимость включенного логирования успешных попыток доступа
 *
 * <AUTHOR>
 * @since 11.07.2025
 */
public class DeniedObjectLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(DeniedObjectLogger.class.getName());

    public static Logger getLOG()
    {
        return LOG;
    }
}
