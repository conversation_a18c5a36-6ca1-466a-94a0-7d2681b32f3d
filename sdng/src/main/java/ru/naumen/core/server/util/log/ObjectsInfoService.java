package ru.naumen.core.server.util.log;

import org.slf4j.Logger;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;

import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Сервис, собирающий информацию об объектах в {@link JsonObject}.
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
public interface ObjectsInfoService
{
    /**
     * Получить информацию о правах {@link Employee текущего пользователя} относительно объекта.
     *
     * @param uuid идентификатор объекта, относительно которого необходимо получить информацию о правах
     */
    ObjectNode getObjectInfo(Logger logger, DtObject obj);

    ObjectNode getObjectInfo(Logger logger, String uuid);

    ObjectNode getTempObjectInfo(Logger logger, String uuid, String metaClassFqn);
}