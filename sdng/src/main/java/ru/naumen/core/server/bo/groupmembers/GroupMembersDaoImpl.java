package ru.naumen.core.server.bo.groupmembers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;

import jakarta.inject.Inject;

import org.hibernate.query.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.GroupMembers;
import ru.naumen.core.server.bo.GroupMembersDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.IEmployee;
import ru.naumen.core.server.bo.team.TeamCountService;
import ru.naumen.core.server.cache.transaction.readonly.CachedInReadOnlyTx;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.GroupMember;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Реализация DAO для доступа к данным о {@link IEmployee сотрудниках} в БД
 * <AUTHOR>
 *
 */
@SuppressWarnings("unchecked")
@Component("groupMembersDao")
@Transactional(propagation = Propagation.REQUIRED)
class GroupMembersDaoImpl implements GroupMembersDao
{
    private static final String REGION_NAME = "groupMembersRegion";

    private static final int IN_BATCH_SIZE = 1000;

    private static final String SELECT_GROUP_CODE = "SELECT DISTINCT gm.grpCode FROM GroupMembers gm "
                                                    + "WHERE gm.memberId IN (:members)";

    private static final String EMPLOYEE_TEAMS_SUBQUERY = "SELECT tm.id "
                                                          + "FROM Employee employee INNER JOIN employee.teams tm "
                                                          + "WHERE employee.id = :employeeId AND tm.removed = :removed";

    private final SpringContext springContext;

    private SessionFactory sessionFactory;

    private final TeamCountService teamCountService;

    /**
     * Суммарное количество команд, при превышении которого должен использоваться подзапрос
     * получения команд сотрудников в запросе получения групп пользователей<br>
     * <b>Подзапрос суммарно выгоднее только тогда, когда происходит проверка прав у списка
     * сотрудников и у каждого сотрудника очень много команд.
     * В остальных случаях лучше обычный метод employee.getTeams() </b><br>
     * Если значение <b>0</b> - всегда используется подзапрос<br>
     * Если значение <b>-1 (отрицательное)</b> - всегда используется {@link Employee#getTeams()}
     */
    private int useSubqueryTeamsLimit;

    @Inject
    public GroupMembersDaoImpl(SpringContext springContext,
            @Value("${ru.naumen.core.server.groupMembers.use_subquery_if_teams_more_then}") int useSubqueryTeamsLimit,
            @Lazy TeamCountService teamCountService)
    {
        this.springContext = springContext;
        this.useSubqueryTeamsLimit = useSubqueryTeamsLimit;
        this.teamCountService = teamCountService;
    }

    @Override
    public List<String> getAllGroupMembers(String groupCode)
    {
        return getGroupMembers(groupCode, "");
    }

    @Override
    public List<String> getEmployeeGroups(String employeeUUID)
    {
        return getMemberGroups(employeeUUID);
    }

    @Override
    @CachedInReadOnlyTx
    public Set<String> getEmployeeOuTeamGroupCodes(Employee employee)
    {
        SortedSet<Long> members = getEmployeeOuIds(employee);

        Set<String> emplGroupsCodes = new HashSet<>();
        if (needUseSubQuery())
        {
            Query query = getCurrentSession()
                    .createQuery(SELECT_GROUP_CODE + " OR gm.memberId IN ("
                                 + EMPLOYEE_TEAMS_SUBQUERY + ')')
                    .setParameterList("members", members)
                    .setParameter("employeeId", employee.getId())
                    .setParameter("removed", false, StandardBasicTypes.BOOLEAN)
                    .setCacheable(true).setCacheRegion(REGION_NAME);
            emplGroupsCodes.addAll(query.list());
        }
        else
        {
            employee.getTeams().forEach(team ->
            {
                if (!team.isRemoved())
                {
                    members.add(team.getId());
                }
            });
            Iterable<List<Long>> membersBatchParts = Iterables.partition(members, IN_BATCH_SIZE);
            for (List<Long> membersBatchPart : membersBatchParts)
            {
                Query query = getCurrentSession()
                        .createQuery(SELECT_GROUP_CODE)
                        .setParameterList("members", membersBatchPart)
                        .setCacheable(true).setCacheRegion(REGION_NAME);
                emplGroupsCodes.addAll(query.list());
            }
        }

        return emplGroupsCodes;
    }

    @Override
    @CachedInReadOnlyTx
    public Set<String> getEmployeeOuTeamGroupCodesByLimitation(Employee employee, Set<String> grpCodes)
    {
        SortedSet<Long> members = getEmployeeOuIds(employee);

        Set<String> emplGroupsCodes = new HashSet<>();

        SortedSet<String> grpCodesList = new TreeSet<>(grpCodes);
        Iterable<List<String>> groupsBatchParts = Iterables.partition(grpCodesList, IN_BATCH_SIZE);
        for (List<String> groupsBatchPart : groupsBatchParts)
        {
            if (needUseSubQuery())
            {
                Query query = getCurrentSession()
                        .createQuery(
                                "SELECT DISTINCT gm.grpCode FROM GroupMembers gm "
                                + "WHERE gm.grpCode IN (:groups) AND (gm.memberId IN (:members) "
                                + "OR gm.memberId IN (" + EMPLOYEE_TEAMS_SUBQUERY + "))")
                        .setParameterList("groups", groupsBatchPart)
                        .setParameterList("members", members)
                        .setParameter("employeeId", employee.getId())
                        .setParameter("removed", false, StandardBasicTypes.BOOLEAN)
                        .setCacheable(true).setFetchSize(IN_BATCH_SIZE)
                        .setCacheRegion(REGION_NAME);
                emplGroupsCodes.addAll(query.list());
            }
            else
            {
                employee.getTeams().forEach(team ->
                {
                    if (!team.isRemoved())
                    {
                        members.add(team.getId());
                    }
                });
                Iterable<List<Long>> membersBatchParts = Iterables.partition(members, IN_BATCH_SIZE);
                for (List<Long> membersBatchPart : membersBatchParts)
                {
                    Query query = getCurrentSession()
                            .createQuery(
                                    "SELECT DISTINCT gm.grpCode FROM GroupMembers gm "
                                    + "WHERE gm.grpCode IN (:groups) AND gm.memberId IN (:members)")
                            .setParameterList("groups", groupsBatchPart)
                            .setParameterList("members", membersBatchPart)
                            .setCacheable(true).setFetchSize(IN_BATCH_SIZE)
                            .setCacheRegion(REGION_NAME);
                    emplGroupsCodes.addAll(query.list());
                }
            }
        }

        return emplGroupsCodes;
    }

    @Override
    public List<String> getGroupEmployees(String groupCode)
    {
        return getGroupMembers(groupCode, Constants.Employee.CLASS_ID);
    }

    @Override
    public List<String> getGroupOUs(String groupCode)
    {
        return getGroupMembers(groupCode, Constants.OU.CLASS_ID);
    }

    @Override
    public List<String> getGroupTeams(String groupCode)
    {
        return getGroupMembers(groupCode, Constants.Team.CLASS_ID);
    }

    @Override
    public List<String> getOUGroups(String ouUUID)
    {
        return getMemberGroups(ouUUID);
    }

    @Override
    public List<String> getTeamGroups(String teamUUID)
    {
        return getMemberGroups(teamUUID);
    }

    public int getUseSubqueryTeamsLimit()
    {
        return useSubqueryTeamsLimit;
    }

    @Override
    public boolean isEmployeeInGroup(String groupCode, long emplID)
    {
        HCriteria criteria = HHelper.create().addSource(GroupMembers.class.getName());
        criteria.setPredicate(HPredicate.COUNT_ALL);
        criteria.add(HRestrictions.eq(criteria.getProperty(GroupMember.MEMBER_ID), emplID)).add(
                HRestrictions.eq(criteria.getProperty(GroupMember.GROUP_CODE), groupCode));
        Number uniqueResult = (Number)criteria.createQuery(getCurrentSession())
                .setCacheable(true)
                .setCacheRegion(REGION_NAME)
                .uniqueResult();
        return uniqueResult != null && uniqueResult.intValue() > 0;
    }

    @Override
    public void removeAllGroupMembers(String grpCode)
    {
        Query query = getCurrentSession().createQuery(
                "delete from " + GroupMembers.class.getName() +
                " where " + GroupMember.GROUP_CODE + "=:gCode");
        query.setParameter("gCode", grpCode, StandardBasicTypes.STRING);
        query.executeUpdate();
    }

    @Override
    public void removeMembersFromGroups(Collection<String> grpCodes, Collection<String> members)
    {
        SortedSet<Long> memberIds = UuidHelper.toIds(members);
        Iterable<List<Long>> membersBatchParts = Iterables.partition(memberIds, IN_BATCH_SIZE);
        SortedSet<String> grpCodesList = new TreeSet<>(grpCodes);
        Iterable<List<String>> groupsBatchParts = Iterables.partition(grpCodesList, IN_BATCH_SIZE);
        for (List<String> groupsBatchPart : groupsBatchParts)
        {
            for (List<Long> membersBatchPart : membersBatchParts)
            {
                Query query = getCurrentSession().createQuery(
                        "delete from " + GroupMembers.class.getName()
                        + " where " + GroupMember.GROUP_CODE + " in (:groups)"
                        + " and " + GroupMember.MEMBER_ID + " in (:members)");
                query.setParameterList("members", membersBatchPart);
                query.setParameterList("groups", groupsBatchPart);
                query.executeUpdate();
            }
        }
    }

    @Override
    public void saveMembersForGroup(Collection<String> grpCodes, Collection<String> membersUUIDs)
    {
        Set<String> notEmptyGrpCodes = Sets.newHashSet(CollectionUtils.filterNotEmpty(grpCodes));
        Set<String> notEmptyMemberUUIDs = Sets.newHashSet(CollectionUtils.filterNotEmpty(membersUUIDs));
        for (String memberUUID : notEmptyMemberUUIDs)
        {
            for (String grpCode : notEmptyGrpCodes)
            {
                GroupMembers groupMember = getGroupMember(memberUUID, grpCode);
                if (null == groupMember)
                {
                    groupMember = new GroupMembers();
                    groupMember.setMemberUUID(memberUUID);
                    groupMember.setGrpCode(grpCode);
                    getCurrentSession().persist(groupMember);
                    getCurrentSession().flush();
                }
            }
        }
    }

    public void setUseSubqueryTeamsLimit(int useSubqueryTeamsLimit)
    {
        this.useSubqueryTeamsLimit = useSubqueryTeamsLimit;

    }

    private Session getCurrentSession()
    {
        if (null == sessionFactory)
        {
            sessionFactory = springContext.getBean("sessionFactory", SessionFactory.class);
        }

        return sessionFactory.getCurrentSession();
    }

    private static SortedSet<Long> getEmployeeOuIds(Employee employee)
    {
        final SortedSet<Long> memberIds = new TreeSet<>();
        if (employee.getId() != 0L)
        {
            memberIds.add(employee.getId());
        }
        if (employee.getParent() != null && employee.getParent().getId() != 0L)
        {
            memberIds.add(employee.getParent().getId());
        }
        return memberIds;
    }

    private GroupMembers getGroupMember(String memberUUID, String grpCode)
    {
        HCriteria criteria = HHelper.create(GroupMembers.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(GroupMember.MEMBER_ID),
                        UuidHelper.toId(memberUUID)))
                .add(HRestrictions.eq(criteria.getProperty(GroupMember.GROUP_CODE), grpCode));
        return (GroupMembers)criteria.createQuery(getCurrentSession()).uniqueResult();
    }

    private List<String> getGroupMembers(String groupCode, String uuidPrefix)
    {
        if (StringUtilities.isEmptyTrim(groupCode))
        {
            return new ArrayList<>();
        }
        HCriteria criteria = HHelper.create().addSource(GroupMembers.class.getName());
        criteria.addColumn(criteria.getProperty(GroupMember.MEMBER_UUID));
        criteria.add(HRestrictions.eq(criteria.getProperty(GroupMember.GROUP_CODE), groupCode));
        if (!StringUtilities.isEmptyTrim(uuidPrefix))
        {
            criteria.add(HRestrictions.like(criteria.getProperty(GroupMember.MEMBER_UUID), uuidPrefix + "%"));
        }
        Query query = criteria.createQuery(getCurrentSession())
                .setCacheable(true)
                .setCacheRegion(REGION_NAME);
        return new ArrayList<String>(query.list());
    }

    private List<String> getMemberGroups(String memberUUID)
    {
        HCriteria criteria = HHelper.create().addSource(GroupMembers.class.getName());
        criteria.addColumn(criteria.getProperty(GroupMember.GROUP_CODE));
        criteria.add(HRestrictions.eq(criteria.getProperty(GroupMember.MEMBER_ID),
                UuidHelper.toId(memberUUID)));
        Query query = criteria.createQuery(getCurrentSession())
                .setCacheable(true)
                .setCacheRegion(REGION_NAME);
        return new ArrayList<String>(query.list());
    }

    private boolean needUseSubQuery()
    {
        return useSubqueryTeamsLimit == 0 ||
               (useSubqueryTeamsLimit > 0 && teamCountService.countAllTeams() > useSubqueryTeamsLimit);
    }
}