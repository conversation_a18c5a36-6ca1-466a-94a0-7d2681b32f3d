package ru.naumen.core.server.util.log;

import static ru.naumen.commons.shared.utils.StringUtilities.isNotEmpty;

import java.util.Iterator;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.dynaform.server.permissions.PermissionsCheckModuleService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Реализация {@link UserInfoService}.
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
@Component
public class UserInfoServiceImpl implements UserInfoService
{
    private static final String TITLE_PROPERTY_NAME = "title";
    private static final String LOGIN_PROPERTY_NAME = "login";
    private static final String UUID_PROPERTY_NAME = "UUID";

    private static final String IP_PROPERTY_NAME = "ip";
    private static final String SESSION_ID_PROPERTY_NAME = "sessionId";
    private static final String GROUPS_PROPERTY_NAME = "groups";
    private static final String LICENSES_PROPERTY_NAME = "licenses";
    private static final String PROFILES_PROPERTY_NAME = "profiles";

    private final CurrentUserProvider currentUserProvider;
    private final PermissionsCheckModuleService permissionsCheckModuleService;
    private final DaoFactory daoFactory;
    private final AuditConfiguration auditConfiguration;

    @Inject
    public UserInfoServiceImpl(CurrentUserProvider currentUserProvider,
            @Lazy PermissionsCheckModuleService permissionsCheckModuleService,
            DaoFactory daoFactory,
            AuditConfiguration auditConfiguration)
    {
        this.currentUserProvider = currentUserProvider;
        this.permissionsCheckModuleService = permissionsCheckModuleService;
        this.daoFactory = daoFactory;
        this.auditConfiguration = auditConfiguration;
    }

    @Override
    public ObjectNode getUserInfo(Logger logger, Employee employee)
    {
        ObjectNode userInfo = JsonNodeFactory.instance.objectNode();
        Employee loadedEmployee = loadEmployee(employee.getLogin());
        addBasicUserInfo(userInfo, loadedEmployee);
        addDetailsUserInfo(logger, userInfo, loadedEmployee);
        return userInfo;
    }

    @Override
    public ObjectNode getUserInfo(Logger logger, @Nullable String login, String ip)
    {
        ObjectNode userInfo = JsonNodeFactory.instance.objectNode();
        Employee employee = loadEmployee(login);
        addBasicUserInfo(userInfo, login, employee == null ? null : employee.getTitle(),
                employee == null ? null : employee.getUUID());
        addDetailsUserInfo(logger, userInfo, employee, ip);
        return userInfo;
    }

    private void addBasicUserInfo(ObjectNode userInfo, Employee employee)
    {
        String login = employee.getLogin();
        String title = employee.getTitle();
        String uuid = employee.getUUID();
        addBasicUserInfo(userInfo, login, title, uuid);
    }

    private void addBasicUserInfo(ObjectNode userInfo, @Nullable String login,
            @Nullable String title, @Nullable String uuid)
    {
        if (isNotEmpty(login))
        {
            userInfo.put(LOGIN_PROPERTY_NAME, login);
        }
        if (isNotEmpty(title))
        {
            userInfo.put(TITLE_PROPERTY_NAME, title);
        }
        if (isNotEmpty(uuid))
        {
            userInfo.put(UUID_PROPERTY_NAME, uuid);
        }
    }

    private void addDetailsUserInfo(Logger logger, ObjectNode userInfo,
            @Nullable Employee employee)
    {
        addDetailsUserInfo(logger, userInfo, employee, null);
    }

    private void addDetailsUserInfo(Logger logger, ObjectNode userInfo,
            @Nullable Employee employee, @Nullable String ip)
    {
        if (!auditConfiguration.isNeedLogUserDetails(logger))
        {
            return;
        }

        String currentIp = ip == null ? CurrentEmployeeContext.getUserIP() : ip;
        if (isNotEmpty(currentIp))
        {
            userInfo.put(IP_PROPERTY_NAME, currentIp);
        }
        String sessionId = CurrentEmployeeContext.getSessionId();
        if (isNotEmpty(sessionId))
        {
            userInfo.put(SESSION_ID_PROPERTY_NAME, sessionId);
        }
        if (employee != null)
        {
            userInfo.put(GROUPS_PROPERTY_NAME, permissionsCheckModuleService.getGroups(employee).toString());
            userInfo.put(LICENSES_PROPERTY_NAME, permissionsCheckModuleService.getLicenses(employee).toString());
        }
    }

    @Nullable
    private Employee loadEmployee(String login)
    {
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
        {
            IDao<Employee> dao = daoFactory.get(Constants.Employee.FQN);
            Iterator<Employee> it = dao.iterate(Filters.and(
                    Filters.eq(Constants.AbstractBO.REMOVED, false),
                    Filters.eq(Constants.Employee.LOGIN, login)
            ));
            Employee employee;
            if (it.hasNext())
            {
                employee =  it.next();
            }
            else
            {
                return null;
            }
            employee = HibernateUtil.unproxy(employee);
            // Инициализируем ленивые поля
            Hibernate.initialize(employee.getTeams());
            Hibernate.initialize(employee.getLicense());
            return employee;
        });
    }
}