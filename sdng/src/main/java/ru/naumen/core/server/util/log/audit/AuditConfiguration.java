package ru.naumen.core.server.util.log.audit;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Конфигурация логирования аудита действий.
 *
 * <AUTHOR>
 * @since 24.07.2025
 */
@Component
public class AuditConfiguration
{
    /**
     * Классы-логгеры, в сообщения которых будет добавлена расширенная информация
     * о пользователе.
     */
    private final Set<String> loggersWithUserDetails;

    /**
     * Классы-логгеры, в сообщения которых будет добавлена расширенная информация
     * о правах на объекты.
     */
    private final Set<String> loggersWithObjectsDetails;

    public AuditConfiguration(@Value("${ru.naumen.logger.details.user.loggers}") String loggersWithUserDetails,
            @Value("${ru.naumen.logger.details.objects.permissions.loggers}")
            String loggersWithObjectsPermissionsDetails)
    {
        this.loggersWithUserDetails = Arrays.stream(loggersWithUserDetails.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
        this.loggersWithObjectsDetails = Arrays.stream(loggersWithObjectsPermissionsDetails.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    /**
     * Необходимо ли логировать расширенную информацию о правах на объекты.
     * @param loggerClass {@link Logger}, для которого проверяется необходимость логирования
     * @return <code>true</code>, если необходимо логировать расширенную информацию
     * о пользователе, иначе - <code>false</code>
     * @param <T> тип {@link Logger логгера}
     */
    public <T extends Logger> boolean isNeedLogUserDetails(T loggerClass)
    {
        return loggersWithUserDetails.contains(loggerClass.getName());
    }

    /**
     * Необходимо ли логировать расширенную информацию о пользователе.
     * @param loggerClass {@link Logger}, для которого проверяется необходимость логирования
     * @return <code>true</code>, если необходимо логировать расширенную информацию
     * о правах на объекты, иначе - <code>false</code>
     * @param <T> тип {@link Logger логгера}
     */
    public <T extends Logger> boolean isNeedLogObjectsPermissionDetails(T loggerClass)
    {
        return loggersWithObjectsDetails.contains(loggerClass.getName());
    }
}