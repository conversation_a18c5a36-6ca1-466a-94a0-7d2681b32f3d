package ru.naumen.core.server.util.log;

import static ru.naumen.commons.shared.utils.StringUtilities.isNotEmpty;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.ObjectNotFoundException;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderServiceImpl;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.objaccess.GrantObjectLogger;
import ru.naumen.core.server.util.log.objectAccessDenied.DeniedObjectLogger;
import ru.naumen.core.shared.Constants.AbstractSystemObject;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.server.permissions.PermissionsCheckModuleService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.autorize.AuthorizationInfo;

/**
 * Реализация {@link ObjectsInfoService}.
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
@Component
public class ObjectsInfoServiceImpl implements ObjectsInfoService
{
    private static final String TITLE_PROPERTY_NAME = "title";
    private static final String UUID_PROPERTY_NAME = "UUID";

    private static final String PROFILES_PROPERTY_NAME = "profiles";
    private static final String ROLES_PROPERTY_NAME = "roles";
    private static final String META_CLASS_FQN_PROPERTY_NAME = "metaClass";
    private static final String META_CLASS_TITLE_PROPERTY_NAME = "metaClassTitle";

    /**
     * Максимальный размер кэша для хранения информации о правах пользователей.
     * Используется для предотвращения бесконечной рекурсии при логировании.
     */
    private static final int MAX_CACHE_SIZE = 1000;

    private final PermissionsCheckModuleService permissionsCheckModuleService;
    private final MetainfoService metainfoService;
    private final AuditConfiguration auditConfiguration;
    private final PrefixObjectLoaderServiceImpl loaderService;

    /**
     * LRU кэш для хранения вычисленной информации о правах пользователей.
     * Ключ: UUID объекта, Значение: AuthorizationInfo
     * Используется для предотвращения повторных вычислений прав при логировании,
     * что может приводить к бесконечной рекурсии.
     */
    private final Map<String, AuthorizationInfo> authorizationInfoCache = new LinkedHashMap<String, AuthorizationInfo>(MAX_CACHE_SIZE + 1, 0.75f, true)
    {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, AuthorizationInfo> eldest)
        {
            return size() > MAX_CACHE_SIZE;
        }
    };

    @Inject
    public ObjectsInfoServiceImpl(@Lazy PermissionsCheckModuleService permissionsCheckModuleService,
            DaoFactory daoFactory,
            MetainfoService metainfoService, AuditConfiguration auditConfiguration,
            PrefixObjectLoaderServiceImpl loaderService)
    {
        this.permissionsCheckModuleService = permissionsCheckModuleService;
        this.metainfoService = metainfoService;
        this.auditConfiguration = auditConfiguration;
        this.loaderService = loaderService;
    }

    @Override
    public ObjectNode getObjectInfo(Logger logger, DtObject obj)
    {
        ClassFqn fqn = obj.getMetaClass();
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        // не логируем доступ к сущностям системных классов
        if (metaClass.isChildForClassFqn(AbstractSystemObject.FQN))
        {
            return null;
        }
        ObjectNode jsonObject = JsonNodeFactory.instance.objectNode();
        jsonObject.put(META_CLASS_FQN_PROPERTY_NAME, fqn.asString());
        jsonObject.put(META_CLASS_TITLE_PROPERTY_NAME, metaClass.getTitle());
        jsonObject.put(TITLE_PROPERTY_NAME, obj.getTitle());
        jsonObject.put(UUID_PROPERTY_NAME, obj.getUUID());
        addDetailsObjectUserPermissions(logger, jsonObject, obj);
        return jsonObject;
    }

    @Override
    public ObjectNode getObjectInfo(Logger logger, String uuid)
    {
        IUUIDIdentifiable object = loadObject(uuid);
        ClassFqn classFqn = metainfoService.getClassFqn(object);
        MetaClass metaClass = metainfoService.getMetaClass(classFqn);

        // не логируем доступ к сущностям системных классов
        if (metaClass.isChildForClassFqn(AbstractSystemObject.FQN)
            && (logger instanceof DeniedObjectLogger || logger instanceof GrantObjectLogger))
        {
            return null;
        }
        ObjectNode objectInfo = JsonNodeFactory.instance.objectNode();
        addBasicObjectInfo(objectInfo, object, metaClass);
        addDetailsObjectUserPermissions(logger, objectInfo, object);
        return objectInfo;
    }

    @Override
    public ObjectNode getTempObjectInfo(Logger logger, String uuid, String metaClassFqn)
    {
        ObjectNode objectInfo = JsonNodeFactory.instance.objectNode();
        MetaClass metaClass = metainfoService.getMetaClass(ClassFqn.parse(metaClassFqn));
        addBasicObjectInfo(objectInfo, uuid, metaClassFqn, metaClass.getTitle());
        return objectInfo;
    }

    private void addBasicObjectInfo(ObjectNode objectInfo,
            @Nullable IUUIDIdentifiable object, MetaClass metaClass)
    {
        String metaClassFqn = metaClass.getFqn().asString();
        String metaClassTitle = metaClass.getTitle();
        addBasicObjectInfo(objectInfo, object, metaClassFqn, metaClassTitle);
    }

    private void addBasicObjectInfo(ObjectNode objectInfo, @Nullable IUUIDIdentifiable object,
            @Nullable String metaClassFqn, @Nullable String metaClassTitle)
    {
        addBasicObjectInfo(objectInfo, object == null ? null : object.getUUID(), metaClassFqn, metaClassTitle);
        if (object instanceof ITitled titledObject)
        {
            String objectTitle = titledObject.getTitle();
            if (isNotEmpty(objectTitle))
            {
                objectInfo.put(TITLE_PROPERTY_NAME, objectTitle);
            }
        }
    }

    private void addBasicObjectInfo(ObjectNode objectInfo, @Nullable String objectUuid,
            @Nullable String metaClassFqn, @Nullable String metaClassTitle)
    {
        if (isNotEmpty(metaClassFqn))
        {
            objectInfo.put(META_CLASS_FQN_PROPERTY_NAME, metaClassFqn);
        }
        if (isNotEmpty(metaClassTitle))
        {
            objectInfo.put(META_CLASS_TITLE_PROPERTY_NAME, metaClassTitle);
        }
        if (isNotEmpty(objectUuid))
        {
            objectInfo.put(UUID_PROPERTY_NAME, objectUuid);
        }
    }

    private void addDetailsObjectUserPermissions(Logger logger, ObjectNode jsonObject, @Nullable IUUIDIdentifiable object)
    {
        if (!auditConfiguration.isNeedLogObjectsPermissionDetails(logger) || object == null)
        {
            return;
        }
        jsonObject.set("permissions", getUserPermissions(object.getUUID()));
    }

    private ObjectNode getUserPermissions(String uuid)
    {
        ObjectNode permission = JsonNodeFactory.instance.objectNode();

        // Проверяем кэш, чтобы избежать повторного вычисления прав и предотвратить рекурсию
        AuthorizationInfo authInfo;
        synchronized (authorizationInfoCache)
        {
            authInfo = authorizationInfoCache.get(uuid);
            if (authInfo == null)
            {
                // Если информации нет в кэше, вычисляем её и сохраняем
                authInfo = permissionsCheckModuleService.getAuthInfo(uuid);
                authorizationInfoCache.put(uuid, authInfo);
            }
        }

        permission.put(PROFILES_PROPERTY_NAME,
                extractTitles(permissionsCheckModuleService.getProfiles(authInfo)).toString());
        permission.put(ROLES_PROPERTY_NAME, extractTitles(authInfo.getRoles()).toString());
        return permission;
    }

    private List<String> extractTitles(List<? extends ITitled> items)
    {
        return items.stream()
                .map(ITitled::getTitle)
                .sorted(String.CASE_INSENSITIVE_ORDER)
                .toList();
    }

    /**
     * Очищает кэш информации о правах пользователей.
     * Должен вызываться при изменении прав пользователей или ролей.
     */
    public void clearAuthorizationInfoCache()
    {
        synchronized (authorizationInfoCache)
        {
            authorizationInfoCache.clear();
        }
    }

    /**
     * Удаляет из кэша информацию о правах для конкретного объекта.
     *
     * @param uuid UUID объекта, для которого нужно удалить информацию из кэша
     */
    public void invalidateAuthorizationInfo(String uuid)
    {
        synchronized (authorizationInfoCache)
        {
            authorizationInfoCache.remove(uuid);
        }
    }

    @Nullable
    private IUUIDIdentifiable loadObject(String uuid)
    {
        IUUIDIdentifiable object = loaderService.load(uuid);
        if (object == null)
        {
            return null;
        }
        try
        {
            return HibernateUtil.unproxy(object);
        }
        catch (ObjectNotFoundException e)
        {
            // Объект может фактически отсутствовать в БД
            return object;
        }
    }
}