package ru.naumen.core.server.util.log;

import org.slf4j.Logger;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.bo.employee.Employee;

/**
 * Сервис, собирающий информацию о {@link Employee текущем пользователе} в виде {@link JsonObject}.
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
public interface UserInfoService
{
    /**
     * Получить информацию о {@link Employee пользователе}.
     *
     * @param employee {@link Employee пользователь}, информацию о котором необходимо получить
     */
    ObjectNode getUserInfo(Logger logger, Employee employee);

    /**
     * Получить информацию о {@link Employee пользователе}.
     *
     */
    ObjectNode getUserInfo(Logger logger, @Nullable String login, String ip);

//    ObjectNode getBasicUserInfo(Employee employee);
}