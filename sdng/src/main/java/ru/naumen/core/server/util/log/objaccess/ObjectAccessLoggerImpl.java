package ru.naumen.core.server.util.log.objaccess;

import java.util.Collection;
import java.util.Collections;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.log.ObjectsInfoService;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.objectAccessDenied.DeniedObjectLogger;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Утилита для логирования доступа пользователя к объектам
 *
 * <AUTHOR>
 * @since 04.12.19
 */
@Component("objectAccessLogger")
public class ObjectAccessLoggerImpl implements ObjectAccessLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(ObjectAccessLogger.class);

    private final CurrentEmployeeContext currentEmployeeContext;
    private final UserInfoService userInfoService;
    private final ObjectsInfoService objectsInfoService;

    @Inject
    public ObjectAccessLoggerImpl(CurrentEmployeeContext currentEmployeeContext,
            UserInfoService userInfoService,
            ObjectsInfoService objectsInfoService)
    {
        this.currentEmployeeContext = currentEmployeeContext;
        this.userInfoService = userInfoService;
        this.objectsInfoService = objectsInfoService;
    }

    @Override
    public void logSingleObjectAccess(@Nullable DtObject accessedObject)
    {
        logSingleObjectAccess(accessedObject, AccessResult.GRANTED);
    }

    @Override
    public void logSingleObjectAccess(@Nullable DtObject accessedObject, AccessResult accessResult)
    {
        if (accessedObject != null)
        {
            logObjectCollectionAccess(Collections.singletonList(accessedObject), accessResult);
        }
    }

    @Override
    public void logObjectCollectionAccess(Collection<DtObject> accessedObjects)
    {
        logObjectCollectionAccess(accessedObjects, AccessResult.GRANTED);
    }

    @Override
    public void logObjectCollectionAccess(Collection<DtObject> accessedObjects, AccessResult accessResult)
    {
        logObjectCollectionAccessInternal(accessedObjects, accessResult);
    }

    private void logObjectCollectionAccessInternal(Collection<DtObject> accessedObjects, AccessResult accessResult)
    {
        if (accessedObjects.isEmpty() || CurrentEmployeeContext.isVendor())
        {
            return;
        }

        Logger targetLogger = getLoggerForAccessResult(accessResult);
        if (!targetLogger.isTraceEnabled())
        {
            return;
        }

        String accessInfo = accessResult.getValue();
        ArrayNode loggedCollection = collectionToJsonArray(targetLogger, accessedObjects, accessInfo);
        if (loggedCollection.isEmpty())
        {
            return;
        }
        ObjectNode root = createRootObjectAccessInfo(targetLogger);
        root.set("objects", loggedCollection);
        targetLogger.trace("Objects access info {}", root);
    }

    /**
     * Получить логгер для указанного результата доступа
     *
     * @param accessResult результат доступа
     * @return соответствующий логгер
     */
    private Logger getLoggerForAccessResult(AccessResult accessResult)
    {
        return accessResult.equals(AccessResult.DENIED) ? DeniedObjectLogger.getLOG() : GrantObjectLogger.getLOG();
    }

    private ObjectNode createRootObjectAccessInfo(Logger logger)
    {
        ObjectNode root = JsonNodeFactory.instance.objectNode();
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        if (employee != null)
        {
            ObjectNode user = userInfoService.getUserInfo(logger, employee);
            root.put("user", user);
        }

        return root;
    }

    private ArrayNode collectionToJsonArray(Logger logger, Collection<DtObject> objects, String accessInfo)
    {
        return objects.stream()
                .filter(Objects::nonNull)
                .map(object ->
                {
                    ObjectNode objectUserPermissions = objectsInfoService.getObjectInfo(logger, object);
                    objectUserPermissions.put("access", accessInfo);
                    return objectUserPermissions;
                })
                .reduce(JsonNodeFactory.instance.arrayNode(),
                        (array, obj) ->
                        {
                            array.add(obj);
                            return array;
                        },
                        (a1, a2) ->
                        {
                            a1.addAll(a2);
                            return a1;
                        });
    }
}
