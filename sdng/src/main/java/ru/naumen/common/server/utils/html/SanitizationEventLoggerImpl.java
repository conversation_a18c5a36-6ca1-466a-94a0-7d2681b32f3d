package ru.naumen.common.server.utils.html;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.inject.Inject;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderServiceImpl;
import ru.naumen.core.server.util.log.ObjectsInfoService;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Реализация {@link SanitizationEventLogger}
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
@Component
public class SanitizationEventLoggerImpl implements SanitizationEventLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(SanitizationEventLoggerImpl.class);
    private final CurrentUserProvider currentUserProvider;
    private final MetainfoService metainfoService;
    private final PrefixObjectLoaderServiceImpl loaderService;
    private final UserInfoService userInfoService;
    private final ObjectsInfoService objectsInfoService;

    public enum XssEvent
    {
        SUBMISSION("Potential XSS removed during form submission"),
        RENDERING("Potential XSS removed during page rendering"),
        UNKNOWN("Potential XSS removed");

        private final String message;

        XssEvent(String message)
        {
            this.message = message;
        }

        public String getMessage()
        {
            return message;
        }

        public static XssEvent fromString(String value)
        {
            try
            {
                return XssEvent.valueOf(value);
            }
            catch (Exception ex)
            {
                return XssEvent.UNKNOWN;
            }
        }
    }

    @Inject
    public SanitizationEventLoggerImpl(CurrentUserProvider currentUserProvider,
            MetainfoService metainfoService, PrefixObjectLoaderServiceImpl loaderService,
            UserInfoService userInfoService, ObjectsInfoService objectsInfoService)
    {
        this.currentUserProvider = currentUserProvider;
        this.metainfoService = metainfoService;
        this.loaderService = loaderService;
        this.userInfoService = userInfoService;
        this.objectsInfoService = objectsInfoService;
    }

    @Override
    public void log(SanitizedContentStorage unsafeContent)
    {
        if (unsafeContent.hasUnsafeElements())
        {
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode logRecord = mapper.createObjectNode();

            addFormInfo(logRecord);
            addUserInfo(logRecord);
            addObjectInfo(logRecord);
            addPayload(logRecord, unsafeContent);

            LOG.warn("{} {}", SanitizationScope.getSanitizeEvent().getMessage(), logRecord);
        }
    }

    private static void addFormInfo(ObjectNode node)
    {
        if (SanitizationScope.getSanitizeForm() != null)
        {
            node.put("form", SanitizationScope.getSanitizeForm());
        }
    }

    private static void addPayload(ObjectNode node, SanitizedContentStorage unsafeContent)
    {
        ObjectMapper mapper = new ObjectMapper();
        node.set("payload", mapper.valueToTree(unsafeContent));
    }

    private void addUserInfo(ObjectNode node)
    {
        if (CurrentEmployeeContext.getCurrentUserPrincipal() != null)
        {
            ObjectNode userInfo = userInfoService.getUserInfo(LOG,
                    ((CurrentEmployeeContext)currentUserProvider).getCurrentEmployee());
            node.set("user", userInfo);
        }
    }

    private void addObjectInfo(ObjectNode node)
    {
        String uuid = SanitizationScope.getSanitizedObject();
        if (uuid != null)
        {
            ObjectNode objectInfo = objectsInfoService.getObjectInfo(LOG, uuid);
            objectInfo.put("attribute", SanitizationScope.getSanitizeAttribute());

            node.set("objects", objectInfo);
        }
    }
}