package ru.naumen.sec.server.handlers;

import static ru.naumen.sec.server.servlets.LoginServlet.USER_NAME;

import java.io.IOException;

import org.slf4j.Logger;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.login.LoginFailureLogger;
import ru.naumen.sec.server.AuthType;

/**
 * Базовая реализация {@link AuthenticationFailureHandler обработчика неудачного входа в систему}.
 *
 * <AUTHOR>
 * @since 18.07.2025
 */
public abstract class BaseAuthenticationFailureHandler implements AuthenticationFailureHandler
{
    private static final String AUTHENTICATION_TYPE_PROPERTY_NAME = "authenticationType";

    private final MessageFacade messages;
    private final UserInfoService userInfoService;

    @Inject
    protected BaseAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService)
    {
        this.messages = messages;
        this.userInfoService = userInfoService;
    }

    @Override
    public final void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception) throws IOException, ServletException
    {
        String authenticationAttemptUsername = getUsernameFromRequest(request);
        String loginFailureMessage = messages.getMessage("eventService." + Categories.LOGIN_FAILURE,
                authenticationAttemptUsername, exception.getMessage());
        Logger logger = LoginFailureLogger.getLogger();
        ObjectNode userInfo = userInfoService.getUserInfo(logger, authenticationAttemptUsername,
                request.getRemoteAddr());
        userInfo.put(AUTHENTICATION_TYPE_PROPERTY_NAME, getAuthenticationType().getName());
        loginFailureMessage += " %s".formatted(userInfo);
        logger.info(loginFailureMessage);
        processAuthenticationFailure(request, response, exception);
    }

    /**
     * Обработать неудачную попытку аутентификации.
     *
     * @param request запрос, во время которого была совершена попытка аутентификации
     * @param response ответ
     * @param exception ошибка, возникшая в процессе аутентификации
     */
    public abstract void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException;

    @Nullable
    protected String getUsernameFromRequest(HttpServletRequest request)
    {
        HttpSession httpSession = request.getSession(false);
        if (httpSession == null)
        {
            return null;
        }

        Object userNameHttpSessionAttribute = httpSession.getAttribute(USER_NAME);
        return userNameHttpSessionAttribute == null
                ? request.getParameter(USER_NAME)
                : userNameHttpSessionAttribute.toString();
    }

    /**
     * @return используемый тип аутентификации
     */
    protected abstract AuthType getAuthenticationType();
}