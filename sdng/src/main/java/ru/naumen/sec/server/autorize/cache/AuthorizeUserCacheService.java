package ru.naumen.sec.server.autorize.cache;

import static java.util.stream.Collectors.partitioningBy;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.concurrent.ThreadSafe;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Sets;
import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.transaction.TransactionManager;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.ObjectsInfoService;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.server.l2cache.security.SecurityL2Cache;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.RoleSnippet;
import ru.naumen.sec.server.autorize.AuthorizationContext;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.autorize.TransactionWithUserPrincipalCache;

/**
 * Кэш пользовательской информации привязанный к текущей транзакции
 * Должен использоваться только в сервисе авторизации
 * т.к. делает мгновенные снимки состояния системы
 *
 * <AUTHOR>
 *
 */
@Component
@ThreadSafe
public class AuthorizeUserCacheService
{
    private static final String PROFILE_ROLES_FOR_DOMAINS_REGION = "profileRolesForDomains";
    private static final String ACCESS_MATRIX_SCRIPTS_REGION = "accessMatrixScripts";
    private static final String ACCESS_MATRIX_VALUES_REGION = "accessMatrixValues";

    /**
     * Максимальный размер кэша для хранения результатов вычисления ролей.
     * Используется для предотвращения бесконечной рекурсии при логировании.
     */
    private static final int MAX_ROLE_COMPUTATION_CACHE_SIZE = 1000;

    private static final Logger LOG = LoggerFactory.getLogger(AuthorizeUserCacheService.class);

    private final TransactionManager txManager;
    private final LicensingService licensingService;
    private final SecurityL2Cache securityCache;
    private final MessageFacade messages;
    private final AccessMatrixExtractorStrategy accessMatrixExtractorStrategies;
    private final AuthorizeServiceUtils authUtils;
    private final ScriptStorageService scriptStorageService;
    private final ScriptService scriptService;
    private final AuthorizationRunnerService authServiceRunner;
    private final UserInfoService userInfoService;
    private final ObjectsInfoService objectsInfoService;

    private TransactionWithUserPrincipalCache<LocalTransactionAuthInfo> cache;
    private TransactionWithUserPrincipalCache<Map<Pair<String, String>, Boolean>> rolesCache;
    private TransactionWithUserPrincipalCache<Map<Pair<String, String>, Boolean>> markerVoterCache;
    private TransactionWithUserPrincipalCache<Map<List<String>, Object>> secDomainCache;

    private Map<String, RoleSnippet> snippets;

    /**
     * LRU кэш для хранения результатов вычисления ролей пользователей.
     * Ключ R(UUID пользователя + UUID объекта + коды ролей)
     * Значениеимеет ли пользователь указанные роли для данного объекта
     */
    private final Map<RoleComputationKey, Boolean> roleComputationCache = new LinkedHashMap<RoleComputationKey, Boolean>(MAX_ROLE_COMPUTATION_CACHE_SIZE + 1, 0.75f, true)
    {
        @Override
        protected boolean removeEldestEntry(Map.Entry<RoleComputationKey, Boolean> eldest)
        {
            return size() > MAX_ROLE_COMPUTATION_CACHE_SIZE;
        }
    };

    /**
     * Ключ для кэша вычисления ролей.
     */
    private static class RoleComputationKey
    {
        private final String employeeUuid;
        private final String objectUuid;
//        private final String rolesCodes;

        public RoleComputationKey(String employeeUuid, String objectUuid)
        {
            this.employeeUuid = employeeUuid;
            this.objectUuid = objectUuid;
//            this.rolesCodes = roles.stream()
//                    .map(Role::getCode)
//                    .sorted()
//                    .collect(Collectors.joining(","));
        }

        @Override
        public boolean equals(Object o)
        {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RoleComputationKey that = (RoleComputationKey) o;
            return Objects.equals(employeeUuid, that.employeeUuid) &&
                   Objects.equals(objectUuid, that.objectUuid);
//                   Objects.equals(rolesCodes, that.rolesCodes);
        }

        @Override
        public int hashCode()
        {
            return Objects.hash(employeeUuid, objectUuid);
        }

        @Override
        public String toString()
        {
            return "RoleComputationKey{" +
                   "employeeUuid='" + employeeUuid + '\'' +
                   ", objectUuid='" + objectUuid + '\'' +
                   '}';
        }
    }

    @Inject
    public AuthorizeUserCacheService(
            MessageFacade messages,
            TransactionManager txManager,
            LicensingService licensingService,
            SecurityL2Cache securityCache,
            AccessMatrixExtractorStrategy accessMatrixExtractorStrategies,
            AuthorizeServiceUtils authUtils,
            ScriptStorageService scriptStorageService,
            ScriptService scriptService,
            AuthorizationRunnerService authServiceRunner,
            UserInfoService userInfoService, ObjectsInfoService objectsInfoService)
    {
        this.messages = messages;
        this.txManager = txManager;
        this.licensingService = licensingService;
        this.securityCache = securityCache;
        this.accessMatrixExtractorStrategies = accessMatrixExtractorStrategies;
        this.authUtils = authUtils;
        this.scriptService = scriptService;
        this.scriptStorageService = scriptStorageService;
        this.authServiceRunner = authServiceRunner;
        this.userInfoService = userInfoService;
        this.objectsInfoService = objectsInfoService;
    }

    /**
     * @return раняя проверка
     */
    @Nullable
    public Boolean earlyCheck()
    {
        return getInfo().earlyCheck();
    }

    /**
     * Получить значение из кэша скриптов матрицы прав
     * @param scriptCode код скрипта
     * @param context контекст
     * @return значение в кэше
     */
    @Nullable
    public Boolean fromMarkerVoterCache(String scriptCode, AuthorizationContext context)
    {
        Map<Pair<String, String>, Boolean> map = markerVoterCache.get();

        if (null == map)
        {
            return null;
        }

        return map.get(getMarkerVoterCacheKey(scriptCode, context));
    }

    public Boolean getAccessMatrix(final SecDomain domain, final Profile profile, final Marker marker)
    {
        // если используется l2 кэширование, то кэшируем через него
        if (securityCache.isCacheEnabled())
        {
            return getDomainAccessMatrix(domain).get(profile, marker);
        }
        return getFromSecDomainCache(input -> getDomainAccessMatrix(domain).get(profile, marker),
                ACCESS_MATRIX_VALUES_REGION, domain, profile, marker);
    }

    private AccessMatrix getDomainAccessMatrix(SecDomain domain)
    {
        return accessMatrixExtractorStrategies.getAccessMatrix(domain);
    }

    public String getAccessMatrixScript(final SecDomain domain, final Profile profile, final Marker marker)
    {
        // если используется l2 кэширование, то кэшируем через него
        if (securityCache.isCacheEnabled())
        {
            return getDomainAccessMatrix(domain).getScript(profile, marker);
        }
        return getFromSecDomainCache(input -> getDomainAccessMatrix(domain).getScript(profile, marker),
                ACCESS_MATRIX_SCRIPTS_REGION, domain, profile, marker);
    }

    public Employee getCurrentEmployee()
    {
        return getInfo().getCurrentEmployee();
    }

    public Collection<? extends Profile> getProfilesForCurrentUser(SecDomain domain, boolean unlicensedRelOfClass)
    {
        return getInfo().getProfilesForCurrentUser(domain, unlicensedRelOfClass);
    }

    public Collection<? extends Role> getRolesForDomain(Profile profile,
            SecDomain domain)
    {
        ClassFqn fqn = ClassFqn.parse(domain.getCode());
        //нет возможности переопределять профили в типах, берем из класса, чтобы лучше работало кэширование
        SecDomain domainClass = fqn.isClass() ? domain : authUtils.getDomain(fqn.fqnOfClass());

        return getFromSecDomainCache((input) -> getRolesForDomainInt(profile, domainClass),
                PROFILE_ROLES_FOR_DOMAINS_REGION, profile, domainClass);
    }

    @PostConstruct
    public void init()
    {
        cache = new TransactionWithUserPrincipalCache<>(txManager);
        rolesCache = new TransactionWithUserPrincipalCache<>(txManager);
        markerVoterCache = new TransactionWithUserPrincipalCache<>(txManager);
        secDomainCache = new TransactionWithUserPrincipalCache<>(txManager);
    }

    public void invalidate()
    {
        cache.put(new LocalTransactionAuthInfo(authUtils, licensingService));
        Map<Pair<String, String>, Boolean> roles = rolesCache.get();
        if (roles != null)
        {
            roles.clear();
        }
        Map<Pair<String, String>, Boolean> markerVoter = markerVoterCache.get();
        if (markerVoter != null)
        {
            markerVoter.clear();
        }
        Map<List<String>, Object> secDomains = secDomainCache.get();
        if (secDomains != null)
        {
            secDomains.clear();
        }

        // Очищаем кэш вычисления ролей для предотвращения устаревших данных
        synchronized (roleComputationCache)
        {
            roleComputationCache.clear();
        }
    }

    public boolean isCurrentUserLicensedOrSuperUser(ClassFqn fqn)
    {
        Employee employee = getInfo().getCurrentEmployee();
        return employee == null || !isUnlicensedCurrentUser(fqn);
    }

    /**
     * Метод определяет, обладает ли указанный сотрудник в указанном контексте
     * хотя бы одной ролью включённой в указанный профиль.
     */
    public boolean isEmployeeHasRoles(Employee employee, Collection<? extends Role> roles, AuthorizationContext context)
    {
        String objectUuid = context.getObject() instanceof IUUIDIdentifiable identifiable
                ? identifiable.getUUID()
                : null;

        if (objectUuid != null)
        {
            RoleComputationKey cacheKey = new RoleComputationKey(employee.getUUID(), objectUuid);
            synchronized (roleComputationCache)
            {
                Boolean cachedResult = roleComputationCache.get(cacheKey);
                if (cachedResult != null)
                {
                    return cachedResult;
                }
            }

            // Вычисляем роли и сохраняем результат в кэш
            boolean result = computeEmployeeRoles(employee, roles, context);
            synchronized (roleComputationCache)
            {
                roleComputationCache.put(cacheKey, result);
            }
            return result;
        }

        // Если объект не имеет UUID, вычисляем без кэширования
        return computeEmployeeRoles(employee, roles, context);
    }

    /**
     * Внутренний метод для вычисления ролей пользователя без кэширования.
     * Выделен в отдельный метод для предотвращения дублирования кода.
     */
    private boolean computeEmployeeRoles(Employee employee, Collection<? extends Role> roles, AuthorizationContext context)
    {
        // Оптимизация. Сначала вычисляем назначенные роли т.к. их вычисление быстрое
        for (Role role : roles)
        {
            if (Role.Type.ASSIGNED.equals(role.getType()))
            {
                String assignedUuid = role.getTransientProperties().getProperty(Constants.Role.ASSIGNED_KEY);
                if (employee.getUUID().equals(assignedUuid))
                {
                    loggingUserRole(employee, role, context);
                    return true;
                }
            }
        }

        // подготавливаем RoleSnippet для системных ролей
        List<Pair<Role, RoleSnippet>> snippets = new ArrayList<>(roles.size());
        for (Role role : roles)
        {
            if (Role.Type.SYSTEM.equals(role.getType()))
            {
                try
                {
                    String snippetName = role.getProperties().getProperty(Constants.Role.SNIPPET_KEY);
                    RoleSnippet snippet = getRoleSnippet(snippetName);
                    snippets.add(Pair.create(role, snippet));
                }
                catch (NoSuchBeanDefinitionException e)
                {
                    LOG.error("Error creating roleSnippet: {}", e.getMessage()); //NOPMD отключение предупреждения об
                    // отсутствующей переменной с исключением. Если нужно увидеть стек, то необходимо включить
                    // уровень логирования DEBUG (см. следующую строку).
                    LOG.debug(e.getMessage(), e);
                }
            }
        }

        final Map<Boolean, List<Pair<Role, RoleSnippet>>> snippetsBySlowness = snippets.stream()
                .collect(partitioningBy(snippetPair -> snippetPair.getRight().isSlow()));

        final Optional<Pair<Role, RoleSnippet>> suitableSnippetOptional = Stream.concat(
                        snippetsBySlowness.get(false).stream(),
                        snippetsBySlowness.get(true).stream())
                .filter(snippetPair -> calculateRole(employee, context, snippetPair))
                .findAny();

        if (suitableSnippetOptional.isPresent())
        {
            return true;
        }

        // Вычисление ролей скриптов c помещением результата в кэш ролей. Вычисляются последними т.к. потенциально
        // самые медленные (в т.ч. и из-за кривых рук разработчиков этих скриптов)
        for (Role role : roles)
        {
            if (Role.Type.SCRIPT.equals(role.getType()))
            {
                Boolean value = fromRolesCache(role, context);
                if (null == value)
                {
                    String scriptCode = role.getProperties().getProperty(Constants.Role.SCRIPT_ACCESS_KEY);
                    if (role.isOnlyForGloballyLicensed() && !licensingService.isGloballyLicensed(employee.getLicense()))
                    {
                        value = toRolesCache(role, context, false);
                    }
                    else
                    {
                        try
                        {
                            value = toRolesCache(role, context,
                                    executeRoleSnippetScript(context, scriptCode, employee));
                        }
                        catch (Exception e)
                        {
                            value = Boolean.FALSE;
                            LOG.error(messages.getMessage("AuthorizationService.roleScriptError", role.getTitle(),
                                    e.getMessage()), e);
                        }
                    }
                }
                if (Boolean.TRUE.equals(value))
                {
                    loggingUserRole(employee, role, context);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Выполняет код {@link RoleSnippet} реализованный как скрипт.
     * Задача скрипта определить, обладает ли указанный сотрудник указанной ролью
     */
    public boolean executeRoleSnippetScript(AuthorizationContext context, @Nullable final String scriptCode,
            Employee employee)
    {
        final Map<String, Object> bindings = authUtils.getBindigs(context, employee);

        final Script script = scriptStorageService.getScript(scriptCode);
        // отключаем проверку прав чтобы не возникало рекурсии
        Object scriptResult = script == null
                ? null
                : authServiceRunner.callWithAllPermission(() -> scriptService.execute(script, bindings));

        return Boolean.TRUE.equals(scriptResult);
    }

    public boolean isUnlicensedCurrentUser(CoreClassFqn fqn)
    {
        return getInfo().isUnLicensedCurrentUser(fqn);
    }

    /**
     * Положить значение в кэш скриптов матрицы прав
     * @param scriptCode код скрипта
     * @param context контекст
     * @param value значение
     */
    public void toMarkerVoterCache(String scriptCode, AuthorizationContext context, boolean value)
    {
        Map<Pair<String, String>, Boolean> map = markerVoterCache.get();
        if (null == map)
        {
            map = new HashMap<>();
            markerVoterCache.put(map);
        }

        map.put(getMarkerVoterCacheKey(scriptCode, context), value);
    }

    protected LocalTransactionAuthInfo getInfo()
    {
        LocalTransactionAuthInfo info = cache.get();
        if (null == info)
        {
            info = new LocalTransactionAuthInfo(authUtils, licensingService);
            LOG.debug("Stored a local transaction authentication data to the cache. Data: {}.", info);
            cache.put(info);
        }

        return info;
    }

    private boolean calculateRole(Employee employee, AuthorizationContext context, Pair<Role, RoleSnippet> snippetPair)
    {
        final Role role = snippetPair.getLeft();
        Boolean value = fromRolesCache(role, context);
        if (null == value)
        {
            final RoleSnippet snippet = snippetPair.getRight();
            value = toRolesCache(role, context, snippet.hasRole(employee, context));
        }
        if (Boolean.TRUE.equals(value))
        {
            loggingUserRole(employee, role, context);
            return true;
        }
        return false;
    }

    @Nullable
    private Boolean fromRolesCache(Role role, AuthorizationContext context)
    {
        Map<Pair<String, String>, Boolean> map = rolesCache.get();

        if (null == map)
        {
            return null;
        }

        return map.get(getRoleCacheKey(role, context));
    }

    @SuppressWarnings("unchecked")
    private <T> T getFromSecDomainCache(Function<Void, T> function, String region, HasCode... objs)
    {
        Map<List<String>, Object> map = secDomainCache.get();
        if (null == map)
        {
            map = new HashMap<>();
            secDomainCache.put(map);
        }

        ArrayList<String> key = getSecDomainCacheKey(region, objs);
        if (map.containsKey(key))
        {
            return (T)map.get(key);
        }

        T value = function.apply(null);
        map.put(key, value);
        return value;
    }

    private static Pair<String, String> getMarkerVoterCacheKey(String scriptCode, AuthorizationContext context)
    {
        Object object = context.getObject();
        if (null == object)
        {
            CoreClassFqn fqn = context.getFqn();
            //в случае добавления объекта, кэшируем скрипты по классу
            String classId = fqn != null ? fqn.getId() : null;
            return Pair.create(classId, scriptCode);
        }
        return Pair.create(((IUUIDIdentifiable)object).getUUID(), scriptCode);
    }

    private static Pair<String, String> getRoleCacheKey(Role role, AuthorizationContext context)
    {
        Object object = context.getObject();
        if (null == object)
        {
            CoreClassFqn fqn = context.getFqn();
            //в случае добавления объекта, кэшируем роли по классу, чтобы не вычислять повторно для всех типов
            String classId = fqn != null ? fqn.getId() : null;
            return Pair.create(classId, role.getCode());
        }
        return Pair.create(((IUUIDIdentifiable)object).getUUID(), role.getCode());
    }

    private static Collection<? extends Role> getRolesForDomainInt(Profile profile, SecDomain domain)
    {
        Set<String> codes = Sets.intersection(profile.getRolesCodes(), domain.getRoleCodes());
        return codes.stream().map(domain::getRole).toList();
    }

    private RoleSnippet getRoleSnippet(String name)
    {
        if (snippets == null)
        {
            snippets = new HashMap<>(SpringContext.getInstance().getBeans(RoleSnippet.class));
        }
        return snippets.get(name);
    }

    private static ArrayList<String> getSecDomainCacheKey(String region, HasCode... objs)
    {
        ArrayList<String> key = new ArrayList<>(objs.length + 1);
        key.add(region);
        for (HasCode obj : objs)
        {
            key.add(obj == null ? null : obj.getCode());
        }
        return key;
    }

    private void loggingUserRole(Employee employee, Role role, AuthorizationContext context)
    {
        Object contextObject = context.getObject();
        if (LOG.isTraceEnabled())
        {
            //            JsonObject objectInfo = new JsonObject();
            //            if (context.getObject() instanceof IUUIDIdentifiable obj)
            //            {
            //                objectInfo = objectAccessLogger.permissionsJson(obj.getUUID());
            //            }
            ObjectNode info = JsonNodeFactory.instance.objectNode();
            ObjectNode user = userInfoService.getUserInfo(LOG, employee);
            info.put("user", user);
            ObjectNode objectInfo = JsonNodeFactory.instance.objectNode();
            if (contextObject!= null
                && contextObject instanceof ITitled
                && contextObject instanceof IUUIDIdentifiable
                && contextObject instanceof IHasMetaInfo)
            {
                DtObject dtObject = new SimpleDtObject();
                dtObject.setMetainfo(((IHasMetaInfo)contextObject).getMetaClass());
                dtObject.setProperty(AbstractBO.TITLE, ((ITitled)contextObject).getTitle());
                dtObject.setProperty(AbstractBO.UUID, ((IUUIDIdentifiable)contextObject).getUUID());
                objectInfo = objectsInfoService.getObjectInfo(LOG, dtObject);
                info.put("object", objectInfo);
            }
            LOG.trace("User has role '{}' for {} [{} {}]", role.getCode(), context.getFqn(),
                    contextObject, info);
        }
        else if (LOG.isDebugEnabled())
        {
            LOG.debug("User has role '{}' for {} [{}]", role.getCode(), context.getFqn(), contextObject);
        }
    }

    private boolean toRolesCache(Role role, AuthorizationContext context, boolean value)
    {
        Map<Pair<String, String>, Boolean> map = rolesCache.get();
        if (null == map)
        {
            map = new HashMap<>();
            rolesCache.put(map);
        }
        map.put(getRoleCacheKey(role, context), value);
        return value;
    }
}
