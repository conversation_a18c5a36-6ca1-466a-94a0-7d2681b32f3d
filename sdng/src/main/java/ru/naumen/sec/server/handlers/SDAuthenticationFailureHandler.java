package ru.naumen.sec.server.handlers;

import java.io.IOException;

import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.mbean.authentication.AuthenticationStatisticsStorage;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.IAuthFailureBehaviour;
import ru.naumen.sec.server.exception.IntegrationEmployeeCredentialsException;
import ru.naumen.sec.server.users.employee.CaseInsensitiveUnigueLoginException;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#CREDENTIALS}.
 *
 * <AUTHOR>
 */
@Component("authFailureHandler")
public class SDAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final EventService eventService;
    private final IAuthFailureBehaviour defaultAuthFailureRedirect;
    private final AuthenticationStatisticsStorage authenticationStatisticsStorage;

    @Inject
    public SDAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            EventService eventService,
            IAuthFailureBehaviour defaultAuthFailureRedirect,
            AuthenticationStatisticsStorage authenticationStatisticsStorage)
    {
        super(messages, userInfoService);
        this.eventService = eventService;
        this.defaultAuthFailureRedirect = defaultAuthFailureRedirect;
        this.authenticationStatisticsStorage = authenticationStatisticsStorage;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException
    {
        eventService.txEvent(Categories.LOGIN_FAILURE, getUsernameFromRequest(request), exception.getMessage());
        // Небольшой костылёк для кастомизации сообщения об ошибке на форме логина :)
        String msg;
        if (exception instanceof CaseInsensitiveUnigueLoginException
            || exception instanceof InternalAuthenticationServiceException) //приходит, если есть ошибки в
        // конфиге ldap
        {
            msg = "systemAuthFailure";
        }
        else if (exception instanceof IntegrationEmployeeCredentialsException)
        {
            msg = "authorization.errorEmployeeForIntegration.loginForm";
        }
        else
        {
            msg = "authenticationFailure";
        }
        defaultAuthFailureRedirect.authFailureRedirect(request, response, exception, msg);
        authenticationStatisticsStorage.incFailed();
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.CREDENTIALS;
    }
}
