package ru.naumen.sec.server.jwt;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.handlers.BaseAuthenticationFailureHandler;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#JWT}.<br>
 * Возвращает {@link HttpServletResponse#SC_UNAUTHORIZED 401 ошибку}.
 *
 * <AUTHOR>
 * @since April 10, 2019 г.
 */
@Component
public class JwtMobileAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final JwtMobileEntryPoint jwtMobileEntryPoint;

    @Inject
    public JwtMobileAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            final JwtMobileEntryPoint jwtMobileEntryPoint)
    {
        super(messages, userInfoService);
        this.jwtMobileEntryPoint = jwtMobileEntryPoint;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException
    {
        jwtMobileEntryPoint.commence(request, response, exception);
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.JWT;
    }
}
