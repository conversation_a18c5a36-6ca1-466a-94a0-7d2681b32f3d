package ru.naumen.sec.server.auth.external;

import java.io.IOException;

import org.pac4j.core.http.adapter.HttpActionAdapter;
import org.pac4j.jee.context.JEEContext;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.IAuthFailureBehaviour;
import ru.naumen.sec.server.handlers.BaseAuthenticationFailureHandler;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#EXTERNAL}.
 *
 * <AUTHOR>
 * @since 30.09.2021
 */
@Component("externalAuthFailureHandler")
public class ExternalAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final IAuthFailureBehaviour defaultAuthFailureRedirect;
    private final MobileExternalAuthUtils mobileUtils;

    @Inject
    public ExternalAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            IAuthFailureBehaviour defaultAuthFailureRedirect,
            MobileExternalAuthUtils mobileUtils)
    {
        super(messages, userInfoService);
        this.defaultAuthFailureRedirect = defaultAuthFailureRedirect;
        this.mobileUtils = mobileUtils;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException
    {
        String errorMessage = (String) request.getAttribute("externalAuthErrorMessage");
        String errorUrl = (String) request.getAttribute("externalAuthErrorUrl");
        Boolean useCustomErrorPage = (Boolean) request.getAttribute("externalAuthUseCustomErrorPage");

        if (errorMessage != null && errorUrl != null && useCustomErrorPage != null)
        {
            handleExternalAuthFailure(request, response, exception, errorMessage, errorUrl, useCustomErrorPage);
        }
        else
        {
            String messageCode = ExternalAuthUtils.getMessageCode(exception);
            if (mobileUtils.isMobileSign(request, false))
            {
                mobileUtils.authFailureRedirect(request, response, exception, messageCode);
            }
            else
            {
                defaultAuthFailureRedirect.authFailureRedirect(request, response, exception, messageCode);
            }
        }
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.EXTERNAL;
    }

    private void handleExternalAuthFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception, String errorMessage, String errorUrl, boolean useCustomErrorPage)
            throws IOException, ServletException
    {
        if (mobileUtils.isMobileSign(request, false))
        {
            String messageCode = ExternalAuthUtils.getMessageCode(exception);
            mobileUtils.authFailureRedirect(request, response, exception, messageCode);
        }
        else if (useCustomErrorPage)
        {
            HttpSession session = request.getSession();
            session.setAttribute(ExternalAuthUtils.EXCEPTION_MESSAGE, errorMessage);

            JEEContext context = new JEEContext(request, response);
            HttpActionAdapter httpActionAdapter = org.pac4j.jee.http.adapter.JEEHttpActionAdapter.INSTANCE;
            httpActionAdapter.adapt(org.pac4j.core.util.HttpActionHelper.buildRedirectUrlAction(context, errorUrl), context);
        }
        else
        {
            String messageCode = ExternalAuthUtils.getMessageCode(exception);
            defaultAuthFailureRedirect.authFailureRedirect(request, response, exception, messageCode);
        }
    }
}
