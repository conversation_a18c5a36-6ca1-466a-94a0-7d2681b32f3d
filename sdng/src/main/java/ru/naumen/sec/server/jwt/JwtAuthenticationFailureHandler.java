package ru.naumen.sec.server.jwt;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.handlers.BaseAuthenticationFailureHandler;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#JWT}.<br>
 * Выполняет редирект в систему откуда пришел запрос на выдачу токена.
 *
 * <AUTHOR>
 * @since 21 апр. 2017 г.
 */
@Component("jwtAuthFailureHandler")
public class JwtAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final JwtAuthEntryPoint jwtAuthEntryPoint;

    @Inject
    public JwtAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            JwtAuthEntryPoint jwtAuthEntryPoint)
    {
        super(messages, userInfoService);
        this.jwtAuthEntryPoint = jwtAuthEntryPoint;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException
    {
        jwtAuthEntryPoint.commence(request, response, exception);
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.JWT;
    }
}
