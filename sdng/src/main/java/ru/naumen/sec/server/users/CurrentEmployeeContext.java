package ru.naumen.sec.server.users;

import static ru.naumen.core.shared.Constants.ServiceUsers.ADVIMPORT_USER;
import static ru.naumen.core.shared.Constants.ServiceUsers.JAR_FROM_WAR_USER;
import static ru.naumen.core.shared.Constants.ServiceUsers.SCRIPT_USER;
import static ru.naumen.core.shared.Constants.ServiceUsers.SERVICE_USERS;
import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.ADMIN;
import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.CRUD_ADMIN;
import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.VENDOR;

import java.util.Collection;
import java.util.Set;

import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpSession;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.sec.server.users.employee.EmployeeUser;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;
import ru.naumen.sec.server.users.superuser.HasAlias;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 * Содержит методы для получения сведений о текущем пользователе.
 * В случае необходимости работы с абстрактной сущностью пользователя нужно использовать
 * {@link ru.naumen.core.server.bo.employee.EmployeeUtils}
 *
 * <AUTHOR>
 * @since 19.01.2023
 */
@Component
public class CurrentEmployeeContext implements CurrentUserProvider
{
    private final IPrefixObjectLoaderService objectLoaderService;
    private final LicensingService licensingService;
    private final EmployeeUserDetailsService employeeUserDetailsService;

    @Inject
    public CurrentEmployeeContext(
            IPrefixObjectLoaderService objectLoaderService,
            @Lazy LicensingService licensingService,
            @Lazy EmployeeUserDetailsService employeeUserDetailsService)
    {
        this.objectLoaderService = objectLoaderService;
        this.licensingService = licensingService;
        this.employeeUserDetailsService = employeeUserDetailsService;
    }

    /**
     * Возвращает действительного текущего сотрудника в обход модуля
     * проверки прав
     *
     * @return текущий сотрудник либо null если суперпользователь
     */
    @Nullable
    public static Employee getActualCurrentEmployee()
    {
        final Object currentUser = getCurrentUserPrincipal();
        if (currentUser instanceof EmployeeUser employeeUser)
        {
            return employeeUser.getEmployee();
        }
        return null;
    }

    /**
     * <p>Возвращает текущего пользователя. Если это сотрудник, то возвращает сотрудника, если суперпользователь, то
     * null</p>
     * <p>Метод возвращает сотрудника с уже "закрытыми" Lazy-полями(например, parent), т.к. сотрудник извлекается в
     * отдельной транзакции (см. {@link EmployeeUser#getEmployee()}) и у всех инициализаторов его Lazy-полей cессия
     * заканчивается на коммите этой транзакции.</p>
     * <p>Попытки получения значений этих полей будут заканчиваться ошибкой:
     * <pre>    org.hibernate.LazyInitializationException: could not initialize proxy - no Session</pre></p>
     */
    @Nullable
    public Employee getCurrentEmployee()
    {
        final Object currentUser = getCurrentUserPrincipal();
        if (currentUser instanceof HasAlias user && user.getAlias() != null)
        {
            return objectLoaderService.get(user.getAlias());
        }
        if (currentUser instanceof EmployeeUser employeeUser)
        {
            return employeeUser.getEmployee();
        }
        return null;
    }

    /**
     * Возвращает логин текущего суперпользователя либо null, если текущий пользователь не суперпользователь
     */
    @Nullable
    public static String getCurrentSuperUserLogin()
    {
        final Object currentUser = getCurrentUserPrincipal();
        return currentUser instanceof SuperUser superUser ? superUser.getLogin() : null;
    }

    /**
     * Возвращает токен аутентификации текущего пользователя
     */
    public static Authentication getCurrentUserAuth()
    {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    @Nullable
    @Override
    public DtObject getCurrentUserDtObject()
    {
        final String uuid = getCurrentUserUUID();
        if (uuid == null)
        {
            return null;
        }
        final Employee employee = objectLoaderService.getSafe(uuid);
        return null == employee ? null : new SimpleDtObject(uuid, employee.getTitle(), employee.getMetaClass());
    }

    /**
     * Возвращает логин текущего пользователя
     */
    @Nullable
    public static String getCurrentUserLogin()
    {
        return getCurrentUserLogin(getCurrentUserPrincipal());
    }

    /**
     * Возвращает логин пользователя currentUser
     */
    @Nullable
    public static String getCurrentUserLogin(@Nullable Object currentUser)
    {
        if (currentUser instanceof Employee user)
        {
            return user.getLogin();
        }
        if (currentUser instanceof EmployeeUser user)
        {
            return user.getUsername();
        }
        if (currentUser instanceof SuperUser superUser)
        {
            return superUser.getLogin();
        }
        return null;
    }

    /**
     * Получим владельца текущей авторизации
     */
    @Nullable
    public static Object getCurrentUserPrincipal()
    {
        final Authentication authentication = getCurrentUserAuth();
        return authentication != null ? authentication.getPrincipal() : null;
    }

    /**
     * Получить UUID текущего пользователя, учитывает модуль проверки прав
     */
    @Nullable
    @Override
    public String getCurrentUserUUID()
    {
        final Object currentUser = getCurrentUserPrincipal();
        if (currentUser instanceof SuperUser superUser)
        {
            final String alias = superUser.getAlias();
            if (alias != null)
            {
                return alias;
            }
        }
        if (currentUser instanceof IUUIDIdentifiable user)
        {
            return user.getUUID();
        }
        return null;
    }

    /**
     * Возвращает {@link UserPrincipal#getUUID() uuid} текущего пользователя.
     * ВНИМАНИЕ! Игнорирует модуль проверки прав. Лучше воздержаться от использования
     * в коде оператора
     * @see EmployeeUser#getUUID(), {@link SuperUser#getUUID()}
     */
    @Nullable
    public static String getCurrentUserUuidWithoutCheckSuper()
    {
        if (getCurrentUserPrincipal() instanceof IUUIDIdentifiable currentUser)
        {
            return currentUser.getUUID();
        }
        return null;
    }

    /**
     * @return UUID текущего авторизованного пользователя (superUser$id или employee$id)
     */
    @Nullable
    public static String getCurrentPersonUuid()
    {
        final Object currentUser = getCurrentUserPrincipal();
        return currentUser instanceof IUUIDIdentifiable user
                ? user.getUUID()
                : null;
    }

    /**
     * Возвращает дополнительные сведения из токена аутентификации текущего пользователя или null, если она отсутствует
     */
    @Nullable
    public static Object getDetails()
    {
        final Authentication authentication = getCurrentUserAuth();
        return authentication != null ? authentication.getDetails() : null;
    }

    /**
     * Возвращает текущего пользователя. Если это сотрудник, то возвращает сотрудника, если суперпользователь, то null
     */
    @Nullable
    public static EmployeeUser getEmployeeUserPrincipal()
    {
        final Object currentUser = getCurrentUserPrincipal();
        return currentUser instanceof EmployeeUser user ? user : null;
    }

    /**
     * Метод для получения IP-адреса текущего пользователя
     */
    public static String getUserIP()
    {
        if (getDetails() instanceof WebAuthenticationDetails details)
        {
            return details.getRemoteAddress();
        }
        return StringUtilities.EMPTY;
    }

    /**
     * Метод для получения IP-сессии текущего пользователя
     */
    public static String getSessionId()
    {
        if (getDetails() instanceof WebAuthenticationDetails details)
        {
            return details.getSessionId();
        }
        return StringUtilities.EMPTY;
    }

    /**
     * ВНИМАНИЕ! Игнорирует модуль проверки прав! Лучше воздержаться от использования в коде оператора
     *
     * @see #isSuperUser()
     *
     * @return true, если текущий пользователь является суперпользователем, иначе - false
     */
    public static boolean isActuallySuperUser()
    {
        final Object currentUser = getCurrentUserPrincipal();
        return currentUser instanceof SuperUser;
    }

    /**
     * @return true, если логин текущего пользователь advimport, иначе - false
     */
    public static boolean isAdvImport()
    {
        return ADVIMPORT_USER.equals(getCurrentSuperUserLogin());
    }

    /**
     * @return true, если логин текущего пользователь jarFromWarUser, иначе - false
     */
    public static boolean isJarFromWar()
    {
        return JAR_FROM_WAR_USER.equals(getCurrentSuperUserLogin());
    }

    /**
     * @return true, если текущий пользователь обладает профилем vendor, иначе - false
     */
    public static boolean isVendor()
    {
        return getSuperUserProfiles().contains(VENDOR);
    }

    /**
     * @return true, если текущий пользователь обладает профилем admin, иначе - false
     */
    public static boolean isAdmin()
    {
        return getSuperUserProfiles().contains(ADMIN);
    }

    /**
     * @return true, если текущий пользователь обладает профилем crudAdmin, иначе - false
     */
    public static boolean isCrudAdmin()
    {
        return getSuperUserProfiles().contains(CRUD_ADMIN);
    }

    /**
     * Получить список профилей суперпользователя
     */
    public static Set<String> getSuperUserProfiles()
    {
        final Object currentUser = getCurrentUserPrincipal();
        if (currentUser instanceof SuperUser superUser)
        {
            return superUser.getProfiles();
        }
        if (currentUser instanceof EmployeeUser employeeUser && employeeUser.isAdmin())
        {
            return employeeUser.getAdminProfiles();
        }
        return Set.of();
    }

    /**
     * @return true, если текущий пользователь обладает профилем vendor или admin, иначе - false
     */
    public static boolean isVendorOrAdmin()
    {
        return isAdmin() || isVendor();
    }

    /**
     * @return true, если логин текущего пользователь script, иначе - false
     */
    public static boolean isScriptUser()
    {
        return SCRIPT_USER.equals(getCurrentSuperUserLogin());
    }

    /**
     * @return true, если логин текущего пользователя является служебным, иначе - false
     */
    public static boolean isServiceUser()
    {
        return SERVICE_USERS.stream().anyMatch(serviceUserLogin -> serviceUserLogin.equals(getCurrentSuperUserLogin()));
    }

    /**
     * @deprecated вместо него нужно использовать {@link #isUserSuper(Collection)} или {@link #isCurrentUserAdmin}
     */
    @Deprecated(forRemoval = true)
    public static boolean isSuperUser()
    {
        final Object principal = getCurrentUserPrincipal();
        if (principal instanceof SuperUser user)
        {
            return user.getAlias() == null;
        }
        return false;
    }

    /**
     * Устанавливает токен аутентификации текущего пользователя
     */
    public static void setCurrentUserAuth(Authentication auth)
    {
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    /**
     * @return true, если текущий пользователь vendor, иначе - false
     */
    public static boolean isCurrentUserSuper()
    {
        return CurrentEmployeeContext.getCurrentUserPrincipal() instanceof UserDetails
               && CurrentEmployeeContext.isVendor();
    }

    /**
     * Есть ли в списке роль супер-пользователя
     *
     * @param authorities - список ролей
     * @return true, если в списке есть роль супер-пользователя, иначе - false
     */
    public static boolean isUserSuper(Collection<? extends GrantedAuthority> authorities)
    {
        return authorities.contains(ru.naumen.sec.server.Constants.GRANTED_AUTHORITY_SUPERUSER);
    }

    /**
     * @return true, если текущий пользователь обладает ролью SUPER_OPERATOR, иначе - false
     */
    public static boolean isCurrentUserSuperOperatorWithoutPermissionsCheckModule()
    {
        return getCurrentUserPrincipal() instanceof UserDetails details
               && isUserSuperOperator(details.getAuthorities());
    }

    /**
     * Проверяет, что текущий пользователь обладает правами суперпользователя в операторе
     * (учитывает модуль проверки прав, связь пользователя с суперпользователем
     * и свойство "Учитывать права сотрудника")
     */
    public boolean isCurrentUserHasSuperUserOperatorRights()
    {
        Object principal = CurrentEmployeeContext.getCurrentUserPrincipal();
        if (principal instanceof SuperUser user)
        {
            if (user.getAlias() == null)
            {
                return true;
            }
            UserPrincipal details = employeeUserDetailsService.loadByUUID(user.getAlias());
            return CurrentEmployeeContext.isUserSuperOperator(details.getAuthorities());
        }
        return isCurrentUserSuperOperatorWithoutPermissionsCheckModule();
    }

    /**
     * @return true, если текущий авторизованный пользователь обладает ролью ADMIN, иначе - false
     */
    public static boolean isCurrentUserAdmin()
    {
        return CurrentEmployeeContext.getCurrentUserPrincipal() instanceof UserDetails details
               && isUserAdmin(details.getAuthorities());
    }

    /**
     * Есть ли в списке роль администратора
     *
     * @param authorities - список ролей
     * @return true, если в списке есть роль ADMIN, иначе - false
     */
    public static boolean isUserAdmin(Collection<? extends GrantedAuthority> authorities)
    {
        return authorities.contains(ru.naumen.sec.server.Constants.GRANTED_AUTHORITY_ADMIN);
    }

    /**
     * @return true, если текущий авторизованный пользователь обладает ролью ADMIN_LITE, иначе - false
     */
    public static boolean isCurrentUserAdminLite()
    {
        return CurrentEmployeeContext.getCurrentUserPrincipal() instanceof UserDetails details
               && isUserAdminLite(details.getAuthorities());
    }

    /**
     * Есть ли в списке роль ADMIN_LITE
     *
     * @param authorities - список ролей
     * @return true, если в списке есть роль ADMIN_LITE, иначе - false
     */
    public static boolean isUserAdminLite(Collection<? extends GrantedAuthority> authorities)
    {
        return authorities.contains(ru.naumen.sec.server.Constants.GRANTED_AUTHORITY_ADMIN_LITE);
    }

    /**
     * Есть ли в списке роль SUPER_OPERATOR
     *
     * @param authorities - список ролей
     * @return true, если в списке есть роль SUPER_OPERATOR, иначе - false
     */
    public static boolean isUserSuperOperator(Collection<? extends GrantedAuthority> authorities)
    {
        return authorities.contains(ru.naumen.sec.server.Constants.GRANTED_AUTHORITY_SUPER_OPERATOR);
    }

    /**
     * Проверяет, является ли текущий пользователь сотрудником
     */
    public static boolean isCurrentUserEmployee()
    {
        return CurrentEmployeeContext.getCurrentUserPrincipal() instanceof EmployeeUser;
    }

    /**
     * Проверяет, является ли текущий пользователь лицензированным сотрудником
     */
    public static boolean isCurrentUserLicensedOrSuperUser()
    {
        final Object principal = CurrentEmployeeContext.getCurrentUserPrincipal();
        return !(principal instanceof EmployeeUser user) || user.isLicensed();
    }

    /**
     * Возвращает текущую сессию пользователя
     */
    @Nullable
    public static HttpSession getCurrentSession()
    {
        final ServletRequestAttributes attr =
                (ServletRequestAttributes)RequestContextHolder.currentRequestAttributes();
        return attr.getRequest().getSession(false);
    }

    /**
     * Возвращает id текущей сессии
     */
    @Nullable
    @CheckForNull
    public static String getCurrentSessionId()
    {
        final HttpSession session = getCurrentSession();
        if (session != null)
        {
            return session.getId();
        }
        return null;
    }

    /**
     * Проверяет, является ли текущий пользователь лицензированным сотрудником с доступом к определенному FQN
     *
     * @param fqn - определенный FQN, лицензионный доступ к которому проверяется
     */
    public boolean isCurrentUserLicensedOrSuperUser(ClassFqn fqn)
    {
        final Object principal = CurrentEmployeeContext.getCurrentUserPrincipal();
        if (!(principal instanceof EmployeeUser))
        {
            return true;
        }
        if (!((EmployeeUser)principal).isLicensed())
        {
            return false;
        }
        return licensingService.isLicensed(fqn);
    }
}
