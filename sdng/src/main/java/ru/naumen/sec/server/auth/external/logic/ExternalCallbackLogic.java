package ru.naumen.sec.server.auth.external.logic;

import static org.pac4j.core.util.CommonHelper.assertNotBlank;
import static org.pac4j.core.util.CommonHelper.assertNotNull;
import static org.pac4j.core.util.CommonHelper.assertTrue;
import static ru.naumen.sec.server.auth.external.ExternalAuthUtils.EXCEPTION_MESSAGE;
import static ru.naumen.sec.server.auth.external.ExternalRedirectException.PERFORMING_REDIRECT_MSG;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.pac4j.core.client.BaseClient;
import org.pac4j.core.client.Client;
import org.pac4j.core.client.Clients;
import org.pac4j.core.config.Config;
import org.pac4j.core.context.CallContext;
import org.pac4j.core.context.FrameworkParameters;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.credentials.Credentials;
import org.pac4j.core.engine.DefaultCallbackLogic;
import org.pac4j.core.exception.http.HttpAction;
import org.pac4j.core.exception.http.WithLocationAction;
import org.pac4j.core.http.adapter.HttpActionAdapter;
import org.pac4j.core.profile.UserProfile;
import org.pac4j.core.util.HttpActionHelper;
import org.pac4j.core.util.Pac4jConstants;
import org.pac4j.jee.context.JEEContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import jakarta.annotation.Nullable;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.net.UrlUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.sec.server.auth.external.ExternalAuthUtils;
import ru.naumen.sec.server.auth.external.ExternalConfigHolder;
import ru.naumen.sec.server.auth.external.ExternalRedirectException;
import ru.naumen.sec.server.auth.external.ExternalUrls;
import ru.naumen.sec.server.auth.external.oidc.NauOidcClient;

/**
 * Логика обработки сообщений от IDP, поступающих на callback URL. Скопипащен родительский метод
 * {@link DefaultCallbackLogic#perform(Config, String, Boolean, String, FrameworkParameters)} только ради того, чтобы
 * попадать в метод {@link NauOidcClient#getUserProfile(CallContext, Credentials)} когда {@code credentials == null}.
 * Это нужно для корректной работы back-channel logout в нашей реализации
 *
 * <AUTHOR>
 * @since 30.09.2021
 * @see ExternalUrls#getCallbackUrl()
 */
public class ExternalCallbackLogic extends DefaultCallbackLogic
{
    private static final Logger LOGGER = LoggerFactory.getLogger(ExternalCallbackLogic.class);
    private static final String ACTION_REQUIRED_MSG = "extra HTTP action required in security: {}";
    private static final String ACTION_WITH_LOCATION_REQUIRED = ACTION_REQUIRED_MSG + ", location: {}";
    private final AuthenticationFailureHandler failureHandler;
    private final ExternalConfigHolder configHolder;
    private final ExternalAuthUtils externalAuthUtils;

    public ExternalCallbackLogic(
            AuthenticationFailureHandler failureHandler,
            ExternalConfigHolder configHolder,
            ExternalAuthUtils externalAuthUtils)
    {
        this.failureHandler = failureHandler;
        this.configHolder = configHolder;
        this.externalAuthUtils = externalAuthUtils;
    }

    @Override
    public Object perform(Config config, @Nullable String inputDefaultUrl,
            @Nullable Boolean inputRenewSession, String defaultClient, FrameworkParameters parameters)
    {
        CallContext callContext = buildContext(config, parameters);
        WebContext webContext = callContext.webContext();
        final String defaultUrl;
        defaultUrl = Objects.requireNonNullElse(inputDefaultUrl, Pac4jConstants.DEFAULT_URL_VALUE);
        LOGGER.debug("=== CALLBACK ===");
        HttpActionAdapter httpActionAdapter = config.getHttpActionAdapter();
        assertNotNull("httpActionAdapter", httpActionAdapter);
        HttpAction action;
        try
        {
            assertNotNull("clientFinder", getClientFinder());
            boolean renewSession =
                    inputRenewSession == null || inputRenewSession; //NOSONAR Не может быть NullPointerException
            assertNotBlank(Pac4jConstants.DEFAULT_URL, defaultUrl);
            Clients clients = config.getClients();
            assertNotNull("clients", clients);
            List<Client> foundClients = getClientFinder().find(clients, webContext, defaultClient);
            assertTrue(foundClients != null && foundClients.size() == 1,
                    """
                            unable to find one indirect client for the callback: check the callback URL for a client name
                            parameter or suffix pathor ensure that your configuration defaults to one indirect client
                            """);
            Client foundClient = foundClients.getFirst(); //NOSONAR Не может быть NullPointerException
            LOGGER.debug("foundClient: {}", foundClient);
            assertNotNull("foundClient", foundClient);
            var credentials = foundClient.getCredentials(callContext).orElse(null);
            LOGGER.debug("extracted credentials: {}", credentials);
            credentials = foundClient.validateCredentials(callContext, credentials).orElse(null);
            LOGGER.debug("validated credentials: {}", credentials);
            if (credentials != null && !credentials.isForAuthentication())
            {
                action = foundClient.processLogout(callContext, credentials);
            }
            else
            {
                // копипаста из родительского метода только ради того, чтобы зайти в метод getUserProfile когда
                // credentials == null
                Optional<UserProfile> optProfile = foundClient.getUserProfile(callContext, credentials);
                LOGGER.debug("optProfile: {}", optProfile);
                if (optProfile.isPresent())
                {
                    UserProfile profile = optProfile.get();
                    Boolean saveProfileInSession = ((BaseClient)foundClient).getSaveProfileInSession(webContext,
                            profile);
                    boolean multiProfile = ((BaseClient)foundClient).isMultiProfile(webContext, profile);
                    LOGGER.debug("saveProfileInSession: {} / multiProfile: {}", saveProfileInSession, multiProfile);
                    saveUserProfile(callContext, config, profile, saveProfileInSession, multiProfile, renewSession);
                }
                action = redirectToOriginallyRequestedUrl(callContext, defaultUrl);
            }
        }
        catch (final RuntimeException e)
        {
            return handleException(e, httpActionAdapter, webContext);
        }
        return httpActionAdapter.adapt(action, webContext);
    }

    /**
     * В отличие от родительского метода в случае получения исключения при аутентификации мы отлавливаем исключения
     * и перенаправляем на страницу авторизации SMP с информационным окном с ошибкой
     */
    @Override
    protected Object handleException(Exception ex, @Nullable HttpActionAdapter httpActionAdapter,
            @Nullable WebContext context)
    {
        if (httpActionAdapter == null || context == null)
        {
            throw runtimeException(ex);
        }
        if (ex instanceof HttpAction httpAction)
        {
            logHttpAction(httpAction);
            return httpActionAdapter.adapt(httpAction, context);
        }
        if (ex instanceof ExternalRedirectException redirectException)
        {
            LOGGER.info(PERFORMING_REDIRECT_MSG, redirectException.getLocation());
            return httpActionAdapter.adapt(
                    HttpActionHelper.buildRedirectUrlAction(context, redirectException.getLocation()), context);
        }
        JEEContext jeeContext = (JEEContext)context;
        HttpSession session = jeeContext.getNativeRequest().getSession();
        if (ex instanceof AuthenticationException authenticationException)
        {
            LOGGER.error("Exception occurred during handle SSO authentication data from IDP", ex);
            try
            {
                jeeContext.getNativeRequest().setAttribute("externalAuthErrorMessage", externalAuthUtils.getMessage(ex));
                jeeContext.getNativeRequest().setAttribute("externalAuthErrorUrl", getErrorUrl(ex));
                jeeContext.getNativeRequest().setAttribute("externalAuthUseCustomErrorPage", configHolder.isUseCustomErrorPage());

                failureHandler.onAuthenticationFailure(jeeContext.getNativeRequest(),
                        jeeContext.getNativeResponse(), authenticationException);
                return false;
            }
            catch (IOException | ServletException re)
            {
                throw runtimeException(re);
            }
        }
        LOGGER.debug("External callback logic perform exception: ", ex);
        session.setAttribute(EXCEPTION_MESSAGE, externalAuthUtils.getMessage(ex));
        final HttpAction action = HttpActionHelper.buildRedirectUrlAction(context, getErrorUrl(ex));
        return httpActionAdapter.adapt(action, context);
    }

    @SuppressWarnings("PMD.InvalidLogMessageFormat")
    private static void logHttpAction(HttpAction httpAction)
    {
        String location = httpAction instanceof WithLocationAction withLocationAction
                ? withLocationAction.getLocation()
                : null;
        if (location == null)
        {
            LOGGER.debug(ACTION_REQUIRED_MSG, httpAction.getCode());
        }
        else
        {
            LOGGER.debug(ACTION_WITH_LOCATION_REQUIRED, httpAction.getCode(), location);
        }
    }

    private String getErrorUrl(Exception ex)
    {
        return ex instanceof AuthenticationException
                ? UrlUtils.buildUrlWithParams(configHolder.getErrorUrl(),
                Map.of(Constants.ANCHOR, configHolder.getDefaultUrl()))
                : configHolder.getDefaultUrl();
    }
}
