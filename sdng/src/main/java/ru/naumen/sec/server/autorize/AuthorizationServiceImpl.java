package ru.naumen.sec.server.autorize;

import static ru.naumen.core.shared.Constants.TEMP_UUID;
import static ru.naumen.core.shared.SecConstants.Employee.BLOCKING_CTRL;
import static ru.naumen.core.shared.SecConstants.Employee.FOR_INTEGRATION_CTRL;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.autorize.GrantedPermission;
import ru.naumen.core.server.bo.GroupMembersDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.settings.AdminLiteSettingsService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.AuthorizeException;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.AttrMarkerImpl;
import ru.naumen.metainfo.server.spi.elements.sec.ISecMarkerVoter;
import ru.naumen.metainfo.server.spi.elements.sec.SecActionImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.AdminLiteSettings;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfo.shared.elements.wf.TransitionLite;
import ru.naumen.sec.server.autorize.cache.AuthorizeServiceUtils;
import ru.naumen.sec.server.autorize.cache.AuthorizeUserCacheService;

/**
 * Реализация сервиса авторизации.
 * <AUTHOR>
 */
@Component
public class AuthorizationServiceImpl implements AuthorizationService
{
    private static final Logger LOG = LoggerFactory.getLogger(AuthorizationServiceImpl.class);

    private final MetainfoServiceBean metainfoService;
    private final SecurityServiceBean securityService;

    private final AuthorizeServiceUtils authUtils;
    private final AuthorizeUserCacheService authUserCacheService;

    private final GroupMembersDao grpMembersDao;
    private final GrantedPermissionsService grantedPermissions;
    private final MessageFacade messages;
    private final ConfigurationProperties properties;
    private final IPrefixObjectLoaderService loader;
    private final AdminLiteSettingsService settingsService;
    private final AuthorizationServiceInterceptors authorizationServiceInterceptors;
    private final ISecMarkerVoter markerVoter;

    @Inject
    public AuthorizationServiceImpl(AuthorizeUserCacheService authUserCacheService,
            MetainfoServiceBean metainfoService,
            SecurityServiceBean securityService,
            AuthorizeServiceUtils authUtils, GroupMembersDao grpMembersDao,
            GrantedPermissionsService grantedPermissions, MessageFacade messages,
            ConfigurationProperties properties,
            IPrefixObjectLoaderService loader,
            AdminLiteSettingsService settingsService,
            AuthorizationServiceInterceptors authorizationServiceInterceptors,
            @Named(MarkerVoterBase.NAME) ISecMarkerVoter markerVoter)
    {
        this.authUserCacheService = authUserCacheService;
        this.metainfoService = metainfoService;
        this.securityService = securityService;
        this.authUtils = authUtils;
        this.grpMembersDao = grpMembersDao;
        this.grantedPermissions = grantedPermissions;
        this.messages = messages;
        this.properties = properties;
        this.loader = loader;
        this.settingsService = settingsService;
        this.authorizationServiceInterceptors = authorizationServiceInterceptors;
        this.markerVoter = markerVoter;
    }

    @Override
    public void checkAttrPermission(AuthorizationContext context, String attrCode, boolean edit)
            throws AuthorizeException
    {
        if (hasAttrPermission(context, attrCode, edit))
        {
            return;
        }
        throw new AuthorizeException(authUtils.getNoPermissionForAttrMessage(context, attrCode, edit), true);
    }

    @Override
    public void checkChangeStatePermission(AuthorizationContext context, TransitionLite transition)
            throws AuthorizeException
    {
        if (hasChangeStatePermission(context, transition))
        {
            return;
        }
        throw new AuthorizeException(authUtils.getNoPermissionForTransition(context.getFqn(), transition), true);
    }

    @Override
    public void checkCommentAttrPermission(IHasMetaInfo object, String attrCode, boolean edit)
            throws AuthorizeException
    {
        AuthorizationContext authContext = new SimpleAuthorizationContext(object, object.getMetaClass());
        if (!hasCommentAttrPermission(authContext, attrCode, edit))
        {
            throw new AuthorizeException(authUtils.getNoPermissionForAttrMessage(
                    new SimpleAuthorizationContext(null, Comment.FQN), attrCode, edit),
                    true);
        }
    }

    @Override
    public void checkEditAttrPermission(IHasMetaInfo object, String attrCode) throws AuthorizeException
    {
        checkAttrPermission(object, attrCode, true);
    }

    @Override
    public void checkEditAttrPermission(IHasMetaInfo object, String attrCode, DtObject oldSubject)
            throws AuthorizeException
    {
        checkAttrPermission(object, attrCode, true, oldSubject);
    }

    @Override
    public void checkEditAttrPermission(IHasMetaInfo object, String attrCode, @Nullable Object value,
            @Nullable DtObject oldSubject) throws AuthorizeException
    {
        checkAttrPermission(object, attrCode, true, oldSubject);
    }

    @Override
    public boolean checkEditAttrPermissionWithoutExeption(Object object, String attrCode)
    {
        return checkAttrPermissionWithoutExeption(object, attrCode, true);
    }

    @Override
    public void checkPermission(AuthorizationContext context, String permission) throws AuthorizeException
    {
        if (!hasPermission(context, permission))
        {
            throw new AuthorizeException(authUtils.getNoPermissionMessage(context, permission), true);
        }
    }

    @Override
    public void checkPermission(IHasMetaInfo object, String permission) throws AuthorizeException
    {
        ClassFqn fqn = metainfoService.getClassFqn(object);
        checkPermission(object, fqn, permission);
    }

    @Override
    public void checkPermission(@Nullable Object object, ClassFqn fqn, String permission) throws AuthorizeException
    {
        checkPermission(new SimpleAuthorizationContext(object, fqn), permission);
    }

    @Override
    public void checkViewAttrPermission(IHasMetaInfo object, String attrCode) throws AuthorizeException
    {
        checkAttrPermission(object, attrCode, false);
    }

    @Override
    public boolean checkViewAttrPermissionWithoutException(Object object, String attrCode) throws AuthorizeException
    {
        return checkAttrPermissionWithoutExeption(object, attrCode, false);
    }

    @Override
    public void clearCache()
    {
        authUserCacheService.invalidate();
    }

    @Override
    public Set<String> getActualLicenses()
    {
        return authUtils.getActualLicenses();
    }

    @Override
    public AuthorizationInfo getAuthorizationInfo(ClassFqn fqn, Object object, IProperties properties)
    {
        Employee currentEmployee = authUserCacheService.getCurrentEmployee();

        AuthorizationContext context = new SimpleAuthorizationContext(object, fqn);
        context.setAll(properties);

        SecDomain domain = authUtils.getDomain(context);

        AuthorizationInfo info = new AuthorizationInfo();

        // Вычисляем все доступные роли сотрудника
        //@formatter:off
        List<? extends Role> roles1 = new ArrayList<>();
        for (Role role: domain.getRoles())
        {
            if (authUserCacheService.isEmployeeHasRoles(currentEmployee, Collections.singleton(role), context))
            {
                info.getRoles().add(role);
            }
        }
        //@formatter:on

        // Вычисляем профили сотрудника
        boolean unlicensedRelOfClass = authUserCacheService.isUnlicensedCurrentUser(context.getFqn());

        Collection<? extends Profile> profiles = authUserCacheService
                .getProfilesForCurrentUser(domain, unlicensedRelOfClass).stream()
                .filter(profile ->
                {
                    Collection<? extends Role> roles = authUserCacheService.getRolesForDomain(profile, domain);
                    return authUserCacheService.isEmployeeHasRoles(currentEmployee, roles, context);
                })
                .toList();
        info.getProfiles().addAll(profiles);

        return info;
    }

    @Override
    public boolean hasAttrPermission(AuthorizationContext context, String attrCode, boolean edit)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        if (hasAttrGrantedPermission(context, attrCode, edit))
        {
            return true;
        }

        long startTime = 0;
        if (LOG.isTraceEnabled())
        {
            startTime = System.currentTimeMillis();
            LOG.trace("Checking permissions in " + context.getFqn() + " for " + attrCode + ". edit = " + edit);
        }
        try
        {
            if (authUserCacheService.isUnlicensedCurrentUser(context.getFqn())
                && !authUtils.hasPermissionForUnlicensedUser(context.getFqn(), attrCode, edit))
            {
                return false;
            }
            if (attrCode.startsWith(SecConstants.ServiceCall.CHANGE_STATE))
            {
                TransitionLite transition =
                        SecConstants.ServiceCall.ChangeStateFunctions.FROM_ATTR_CODE_TO_TRANSITION_LITE_FORMATTER
                                .apply(attrCode);
                return hasPermission(context,
                        securityService.getMarkerCodesForChangeState(context.getFqn(), transition));
            }
            if (edit && ru.naumen.core.shared.Constants.Employee.FQN.isSameClass(context.getFqn())
                && ru.naumen.core.shared.Constants.Employee.IS_LOCKED.equals(attrCode))
            {
                //В случае, если проверяются права на редактирование системного атрибута "Заблокирован"
                //сотрудника, вместо прав на маркер проверяем права на действие "Управление блокировкой учетной записи"
                return hasPermission(context, BLOCKING_CTRL);
            }
            if (edit && ru.naumen.core.shared.Constants.Employee.FQN.isSameClass(context.getFqn())
                && ru.naumen.core.shared.Constants.Employee.IS_FOR_INTEGRATION.equals(attrCode))
            {
                //В случае, если проверяются права на редактирование системного атрибута "Служебный для интеграции"
                //сотрудника, вместо прав на маркер проверяем права на действие "Изменение признака "Служебный для
                // интеграции""
                return hasPermission(context, FOR_INTEGRATION_CTRL);
            }
            return hasPermission(context, securityService.getMarkerCodesForAttr(context.getFqn(), attrCode, edit));
        }
        finally
        {
            if (LOG.isTraceEnabled())
            {
                LOG.trace("Checked(" + (System.currentTimeMillis() - startTime) + ") permissions in " + context.getFqn()
                          + " for " + attrCode + ". edit = " + edit);
            }
        }
    }

    @Override
    public boolean hasEditAttrPermissionForProcessingLicense(AuthorizationContext context, String attrCode)
    {
        if (!properties.isCheckAttrPermissionWithProcessLicense()
            || authUtils.isGlobalLicensedUser()
            || authUtils.isUnLicencesUser()
            || !authUserCacheService.isUnlicensedCurrentUser(context.getFqn()))
        {
            return hasAttrPermission(context, attrCode, true);
        }
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }

        long startTime = 0;
        if (LOG.isTraceEnabled())
        {
            startTime = System.currentTimeMillis();
            LOG.trace("Checking edit permissions in " + context.getFqn() + " for " + attrCode);
        }
        try
        {
            Object object = context.getObject();
            if (object == null || object instanceof File)
            {
                return false;
            }
            Attribute attribute = metainfoService.getMetaClass(object).getAttribute(attrCode);
            if (attribute == null)
            {
                return false;
            }
            AttributeType attributeType = attribute.getType();
            if (attributeType.getCode().equals(BackLinkAttributeType.CODE))
            {
                String relClassFqnString = attributeType.getProperty(ObjectAttributeType.METACLASS_FQN);
                ClassFqn relClassFqn = relClassFqnString == null
                        ? null
                        : ClassFqn.parse(relClassFqnString);

                String relAttrCode = attributeType.getProperty(BackLinkAttributeType.BACK_ATTR_CODE);

                return relClassFqn != null
                       && !authUserCacheService.isUnlicensedCurrentUser(relClassFqn)
                       && hasPermission(context,
                        securityService.getMarkerCodesForAttr(context.getFqn(), attrCode, true))
                       && hasPermission(new SimpleAuthorizationContext(null, relClassFqn),
                        securityService.getMarkerCodesForAttr(relClassFqn, relAttrCode, true));
            }
            if (authUserCacheService.isUnlicensedCurrentUser(context.getFqn())
                && !authUtils.hasPermissionForUnlicensedUser(context.getFqn(), attrCode, true))
            {
                return false;
            }
            return hasPermission(context, securityService.getMarkerCodesForAttr(context.getFqn(), attrCode, true));
        }
        finally
        {
            if (LOG.isTraceEnabled())
            {
                LOG.trace("Checked(" + (System.currentTimeMillis() - startTime) + ") edit permissions in "
                          + context.getFqn() + " for " + attrCode);
            }
        }
    }

    @Override
    public boolean hasEditAttrPermissionForProcessingLicense(IHasMetaInfo object, String attrCode)
    {
        ClassFqn fqn = metainfoService.getClassFqn(object);
        return hasEditAttrPermissionForProcessingLicense(new SimpleAuthorizationContext(object, fqn), attrCode);
    }

    @Override
    public boolean hasAttrPermission(IHasMetaInfo object, String attrCode, boolean edit)
    {
        ClassFqn fqn = metainfoService.getClassFqn(object);
        return hasAttrPermission(new SimpleAuthorizationContext(object, fqn), attrCode, edit);
    }

    @Override
    public boolean hasChangeStatePermission(AuthorizationContext context, TransitionLite transition)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        return hasPermission(context, securityService.getMarkerCodesForChangeState(context.getFqn(), transition));
    }

    @Override
    public boolean hasChangeStatePermission(Object object, ClassFqn fqn, TransitionLite transition)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        SimpleAuthorizationContext simpleAuthorizationContext = new SimpleAuthorizationContext(object, fqn);
        return hasChangeStatePermission(simpleAuthorizationContext, transition);
    }

    @Override
    public boolean hasCommentAttrPermission(AuthorizationContext context, String attrCode, boolean edit)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        Attribute attribute = metainfoService.getAttribute(AttributeFqn.create(Comment.FQN, attrCode));
        if (attribute.isHardcoded())
        {
            // На системные атрибуты комментариев права есть всегда
            return true;
        }
        return hasPermission(context, securityService.getMarkerCodesForCommentAttr(context.getFqn(), attrCode, edit));
    }

    @Override
    public boolean hasFireUserEventPermission(AuthorizationContext context, @Nullable String eventUuid)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        return hasPermission(context, securityService.getMarkerCodesForUserEvent(context.getFqn(), eventUuid));
    }

    @Override
    public void checkFireUserEventPermission(AuthorizationContext context, @Nullable String eventUuid)
    {
        if (!hasFireUserEventPermission(context, eventUuid))
        {
            throw new AuthorizeException(messages.getMessage("AuthorizationService.accessDenied"), true);
        }
    }

    @Override
    public boolean hasPermission(AuthorizationContext context, Collection<String> permissions)
    {
        Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }
        if (hasGrantedPermission(context, permissions))
        {
            return true;
        }

        for (AuthorizationServiceInterceptor interceptor : authorizationServiceInterceptors.getInterceptors())
        {
            if (permissions != SKIP_PERMISSIONS && interceptor.isPossible(context))
            {
                context = interceptor.intercept(context);
                permissions = interceptor.intercept(context, permissions);
            }
        }
        if (permissions == SKIP_PERMISSIONS)
        {
            return true;
        }

        if (permissions.isEmpty())
        {
            return false;
        }

        if (context.isPermissionsCacheEnabled())
        {
            Set<Boolean> values = getPermissionValues(context, permissions);
            if (values.contains(Boolean.TRUE))
            {
                return true;
            }
            else if (!values.contains(null))
            {
                return false; //все false
            }
        }

        return hasPermissionInt(context, permissions);
    }

    @Override
    public boolean hasPermission(AuthorizationContext context, String permission)
    {
        return hasPermission(context, Collections.singleton(permission));
    }

    @Override
    public boolean hasPermission(@Nullable Object object, CoreClassFqn fqn, String permission)
    {
        return hasPermission(new SimpleAuthorizationContext(object, fqn), permission);
    }

    @Override
    public boolean hasPermission(@Nonnull Object object, String permission)
    {
        ClassFqn fqn = metainfoService.getClassFqn(object);
        return hasPermission(object, fqn, permission);
    }

    @Override
    public boolean hasProfile(@Nonnull CoreClassFqn fqn, String profile)
    {
        return hasProfile(fqn, profile, IProperties.EMPTY);
    }

    @Override
    public boolean hasProfile(CoreClassFqn fqn, String profile, IProperties contextProperties)
    {
        return hasProfile(null, fqn, profile, contextProperties);
    }

    @Override
    public boolean hasProfile(@Nonnull Object object, String profile)
    {
        return hasProfile(object, ((IHasMetaInfo)object).getMetaClass(), profile, IProperties.EMPTY);
    }

    @Override
    public boolean hasRole(Object object, String role)
    {
        AuthorizationContext context = new SimpleAuthorizationContext(object, ((IHasMetaInfo)object).getMetaClass());
        Employee employee = authUserCacheService.getCurrentEmployee();
        context = getContextFromInterceptor(context);
        return authUserCacheService.isEmployeeHasRoles(employee, ImmutableSet.of(securityService.getRole(role)),
                context);
    }

    @Override
    public boolean hasRole(String role)
    {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        for (GrantedAuthority authority : authentication.getAuthorities())
        {
            if (role.equals(authority.getAuthority()))
            {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isEditAttrPermissionGranted(IHasMetaInfo object, String attrCode, @Nullable Object value,
            DtObject oldSubject)
    {
        final SimpleAuthorizationContext ctx = createAuthorizationContext(object, attrCode, oldSubject);
        return hasAttrGrantedPermission(ctx, attrCode, true);
    }

    @Override
    public boolean isEmployeeHasGroups(Employee employee, Set<String> groupsCodes)
    {
        if (groupsCodes.isEmpty())
        {
            return true;
        }

        return !grpMembersDao.getEmployeeOuTeamGroupCodesByLimitation(employee, groupsCodes).isEmpty();
    }

    @Override
    public boolean isEmployeeHasProfileGroupsCodes(Employee employee, Profile profile)
    {
        Set<String> groupsCodes = profile.getGroupsCodes();
        return isEmployeeHasGroups(employee, groupsCodes);
    }

    @Override
    public boolean isProfileExists(String profile)
    {
        return securityService.isProfileExists(profile);
    }

    @Override
    public void register(AuthorizationServiceInterceptor interceptor)
    {
        authorizationServiceInterceptors.addInterceptor(interceptor);
    }

    @Override
    public Boolean isAdminLite(String employeeUuid)
    {
        if (!settingsService.isAdminLiteSettingsEnabled())
        {
            return false;
        }

        final AdminLiteSettings settings = settingsService.getSettings();
        return TransactionRunner.call(TransactionType.READ_ONLY, () ->
        {
            Employee employee = loader.get(employeeUuid);
            if (AuthorizeServiceUtils.isUnLicencesUserInGeneral(employee))
            {
                return false;
            }
            return isEmployeeHasGroups(employee, new HashSet<>(settings.getGroups()));
        });
    }

    @Override
    public boolean hasProfile(@Nullable Object object, CoreClassFqn fqn, String profileCode,
            IProperties contextProperties)
    {
        final Boolean res = authUserCacheService.earlyCheck();
        if (res != null)
        {
            return res;
        }

        final Employee employee = authUserCacheService.getCurrentEmployee();
        final boolean unLicensedUser = authUserCacheService.isUnlicensedCurrentUser(fqn);

        final Profile profile = securityService.getProfile(profileCode);

        if (profile == null)
        {
            return false;
        }

        if (unLicensedUser ^ profile.isForLicensedUsers() && isEmployeeHasProfileGroupsCodes(employee, profile))
        {
            AuthorizationContext context = new SimpleAuthorizationContext(object, fqn);

            for (String propertyName : contextProperties.propertyNames())
            {
                context.setProperty(propertyName, contextProperties.getProperty(propertyName));
            }
            context = getContextFromInterceptor(context);
            SecDomain domain = authUtils.getDomain(context);

            Collection<? extends Role> roles = authUserCacheService.getRolesForDomain(profile, domain);
            return authUserCacheService.isEmployeeHasRoles(employee, roles, context);
        }

        return false;
    }

    private void checkAttrPermission(IHasMetaInfo object, String attrCode, boolean edit) throws AuthorizeException
    {
        checkAttrPermission(object, attrCode, edit, null);
    }

    private void checkAttrPermission(IHasMetaInfo object, String attrCode, boolean edit,
            @Nullable DtObject oldSubject) throws AuthorizeException
    {
        final SimpleAuthorizationContext ctx = createAuthorizationContext(object, attrCode, oldSubject);
        checkAttrPermission(ctx, attrCode, edit);
    }

    private boolean checkAttrPermissionWithoutExeption(AuthorizationContext context, String attrCode, boolean edit)
            throws AuthorizeException
    {
        return hasAttrPermission(context, attrCode, edit);
    }

    private boolean checkAttrPermissionWithoutExeption(Object object, String attrCode, boolean edit)
            throws AuthorizeException
    {
        ClassFqn fqn = metainfoService.getClassFqn(object);

        Object objectForCheck = object;
        if (object instanceof IUUIDIdentifiable && ((IUUIDIdentifiable)object).getUUID().startsWith(TEMP_UUID))
        {
            objectForCheck = null;
        }

        return checkAttrPermissionWithoutExeption(new SimpleAuthorizationContext(objectForCheck, fqn), attrCode, edit);
    }

    @Nonnull
    private SimpleAuthorizationContext createAuthorizationContext(IHasMetaInfo object, String attrCode,
            @Nullable DtObject oldSubject)
    {
        final ClassFqn fqn = Constants.METACLASS.equals(attrCode) && oldSubject != null
                ? oldSubject.getMetaClass()
                : metainfoService.getClassFqn(object);

        final SimpleAuthorizationContext ctx = new SimpleAuthorizationContext(object, fqn);
        ctx.setOldSubject(oldSubject);
        return ctx;
    }

    private AuthorizationContext getContextFromInterceptor(AuthorizationContext context)
    {
        for (AuthorizationServiceInterceptor interceptor : authorizationServiceInterceptors.getInterceptors())
        {
            if (interceptor.isPossible(context))
            {
                context = interceptor.intercept(context);
            }
        }
        return context;
    }

    private static Set<Boolean> getPermissionValues(AuthorizationContext context, Collection<String> permissions)
    {
        if (permissions.size() == 1)
        {
            return Collections.singleton(context.getPermission(permissions.iterator().next()));
        }

        Set<Boolean> result = new HashSet<>(permissions.size());
        for (String permission : permissions)
        {
            result.add(context.getPermission(permission));
        }
        return result;
    }

    private boolean hasAttrGrantedPermission(AuthorizationContext context, String attrCode, boolean edit)
    {
        GrantedPermissions ps = grantedPermissions.grantedPermissions();
        if (ps.hasAllPermissions())
        {
            return true;
        }
        Iterable<GrantedPermission> allPermissions = ps.getAll();
        for (GrantedPermission gp : allPermissions)
        {
            if (isAttrGranted(gp, context, attrCode, edit))
            {
                return true;
            }
        }
        SecDomain domain = authUtils.getDomain(context);
        for (GrantedPermission gp : allPermissions)
        {
            for (String markerCode : gp.getPermissions())
            {
                if (SecConstants.ServiceCall.CHANGE_STATE.equals(markerCode) && (HasState.STATE.equals(attrCode)
                                                                                 || HasState.STATE_START_TIME.equals(
                        attrCode)))
                {
                    return true;
                }
                Marker marker = domain.getMarker(markerCode);

                //@formatter:off
                Set<String> attrs = marker instanceof SecActionImpl ?
                        ((SecActionImpl)marker).getAttributeCodes()
                        : marker instanceof AttrMarkerImpl ?
                                    ((AttrMarkerImpl)marker).getElements() :
                                    Collections. emptySet();
                //@formatter:on

                if (attrs.contains(attrCode))
                {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean hasGrantedPermission(AuthorizationContext context, Collection<String> permissions)
    {
        return permissions.stream().anyMatch(permission -> isGrantedPermission(context, permission));
    }

    private ISecMarkerVoter getVoter(Marker marker)
    {
        return StringUtilities.isEmpty(marker.getHandler()) ? this.markerVoter
                : SpringContext.getInstance().getBean(marker.getHandler(), ISecMarkerVoter.class);
    }

    private boolean hasPermissionInt(AuthorizationContext context, Collection<String> permissions)
    {
        Employee currentEmployee = authUserCacheService.getCurrentEmployee();
        boolean unlicensedRelOfClass = authUserCacheService.isUnlicensedCurrentUser(context.getFqn());

        SecDomain domain = authUtils.getDomain(context);
        Multimap<String, Role> rolesForChecking = HashMultimap.create();

        for (String permission : permissions)
        {
            if (context.isPermissionsCacheEnabled())
            {
                Boolean value = context.getPermission(permission);
                if (Boolean.TRUE.equals(value))
                {
                    return true;
                }
                else if (Boolean.FALSE.equals(value))
                {
                    continue;
                }
            }

            Marker marker = domain.getMarker(permission);
            if (marker == null || unlicensedRelOfClass && !marker.isUnlicensedAllowed())
            {
                continue;
            }

            Collection<? extends Profile> profiles = authUserCacheService.getProfilesForCurrentUser(domain,
                    unlicensedRelOfClass);
            if (profiles.isEmpty())
            {
                continue;
            }
            ISecMarkerVoter voter = getVoter(marker);
            for (Profile profile : profiles)
            {
                boolean configValue = Boolean.TRUE.equals(authUserCacheService.getAccessMatrix(domain,
                        profile, marker));

                String scriptCode = configValue ? authUserCacheService.getAccessMatrixScript(domain, profile, marker)
                        : null;

                Map<String, Object> bindings = StringUtilities.isNotEmpty(scriptCode) ? authUtils.getBindigs(context,
                        currentEmployee) : null;
                // Если нужно учитывать профиль, добавляем для проверки
                if (voter.decide(currentEmployee, profile, marker, configValue, scriptCode, context, bindings))
                {
                    rolesForChecking.putAll(permission, authUserCacheService.getRolesForDomain(profile, domain));
                }
            }
        }

        if (!context.isPermissionsCacheEnabled())
        {
            Set<Role> roles = Sets.newHashSet(rolesForChecking.values());
            return !roles.isEmpty() && authUserCacheService.isEmployeeHasRoles(currentEmployee, roles, context);
        }

        //вычислим для всех пермишенов отдельно и закэшируем
        boolean result = false;
        for (Map.Entry<String, Collection<Role>> entry : rolesForChecking.asMap().entrySet())
        {
            String permission = entry.getKey();
            Collection<Role> roles = entry.getValue();
            boolean value = !roles.isEmpty() && authUserCacheService.isEmployeeHasRoles(currentEmployee, roles,
                    context);
            context.setPermission(permission, value);
            result = result || value;
        }
        return result;
    }

    private boolean isAttrGranted(GrantedPermission gp, AuthorizationContext context, String attrCode, boolean edit)
    {
        Map<String, Boolean> grantedAttrs = gp.getAttrPermissions();
        if (grantedAttrs.containsKey(attrCode) && matchMetaClass(gp, context))
        {
            Boolean value = grantedAttrs.get(attrCode);
            return value == null || edit == value;
        }
        return false;
    }

    private boolean isGranted(GrantedPermission gp, AuthorizationContext context, String marker)
    {
        return (gp.getPermissions().contains(marker) || gp.getPermissions()
                .stream()
                .anyMatch(marker::startsWith)) && matchMetaClass(gp, context);
    }

    private boolean isGrantedPermission(AuthorizationContext context, String permission)
    {
        GrantedPermissions ps = grantedPermissions.grantedPermissions();
        if (ps.hasAllPermissions())
        {
            return true;
        }
        for (GrantedPermission gp : ps.getAll())
        {
            if (isGranted(gp, context, permission))
            {
                return true;
            }
        }
        SecDomain domain = authUtils.getDomain(context);
        Marker marker = domain.getMarker(permission);
        return marker != null && marker.isGranted();
    }

    private boolean matchMetaClass(GrantedPermission gp, AuthorizationContext context)
    {
        return gp.isDescendantsInclude() ? metainfoService.getMetaClass(context.getFqn()).isAssignableTo(gp
                .getClassFqn()) : context.getFqn().equals(gp.getClassFqn());
    }
}
