package ru.naumen.sec.server.session;

import org.springframework.security.core.session.SessionInformation;

import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Сервис логирования событий, связанных с сессиями работы пользователей.
 * <AUTHOR>
 * @since May 07, 2019
 */
public interface SessionLogger
{
    /**
     * Логирует событие входа в приложение.
     *
     * @param principal пользователь
     * @param eventSource тип события для логирования сессии пользователя
     */
    void login(Object principal, SessionEventType eventSource);

    /**
     *
     * @param ip
     * @param username
     * @param message
     * @param request
     */
    void loginFailure(String ip, String username, String message, HttpServletRequest request);

    /**
     * Логирует событие выхода из приложения.
     *
     * @param session сессия пользователя
     * @param reason причина разлогина
     */
    void logout(SessionInformation session, @Nullable LogoutReason reason);

    /**
     * Логирует событие выхода из приложения.
     *
     * @param principal пользователь
     * @param eventSource тип события для логирования сессии пользователя
     * @param reason причина разлогина пользователя
     */
    void logout(Object principal, SessionEventType eventSource, @Nullable LogoutReason reason);
}
