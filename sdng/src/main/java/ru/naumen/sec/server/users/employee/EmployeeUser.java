package ru.naumen.sec.server.users.employee;

import static ru.naumen.core.shared.Constants.Employee.NOT_LICENSED_USER;

import java.util.Set;

import org.hibernate.Hibernate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.annotation.Nullable;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.core.sec.CoreEmployeeUser;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.users.superuser.HasAlias;
import ru.naumen.sec.server.users.superuser.SuperUserEmployee;

/**
 * Реализация {@link UserPrincipal} для {@link Employee сотрудников}
 *
 * <AUTHOR>
 */
public class EmployeeUser extends EmployeeUserBase implements HasAlias, CoreEmployeeUser
{
    private static final long serialVersionUID = -5342286866912505741L;
    private transient volatile boolean isInitialized;
    private transient PlatformTransactionManager txManager;
    private transient IPrefixObjectLoaderService prefixObjectLoader;

    private String alias;

    public EmployeeUser(Employee employee)
    {
        this(employee, (SuperUserEmployee)null);
    }

    public EmployeeUser(Employee employee, @Nullable final SuperUserEmployee superUserEmployee)
    {
        super(employee, superUserEmployee);
    }

    public EmployeeUser(Employee employee, Set<GrantedAuthority> roles)
    {
        this(employee, roles, null);
    }

    public EmployeeUser(Employee employee, Set<GrantedAuthority> roles,
            @Nullable final SuperUserEmployee superUserEmployee)
    {
        this(employee, superUserEmployee);
        this.roles = roles;
    }

    @Override
    public Employee getEmployee()
    {
        ensureInitialized();
        final TransactionTemplate tt = new TransactionTemplate(txManager);
        return tt.execute(status ->
        {
            final Employee empl = HibernateUtil.unproxy(prefixObjectLoader.get(getUUID()));
            Hibernate.initialize(empl.getLicense());
            Hibernate.initialize(empl.getTeams());
            return empl;
        });
    }

    /**
     * Позволяет не анпроксируя всего пользователя (как в {@link EmployeeUser#getEmployee()})
     * с его лицензиями достать булево поле для смены пароля
     * {@link ru.naumen.core.server.bo.employee.Password#mustBeChanged}
     * @return true, если пользователю необходимо сменить пароль, false - в противном случае
     */
    public Boolean getPasswordMustBeChanged()
    {
        ensureInitialized();
        final TransactionTemplate tt = new TransactionTemplate(txManager);
        return tt.execute(status ->
        {
            final Employee empl = prefixObjectLoader.get(getUUID());
            if (empl == null || empl.getPasswordInfo() == null)
            {
                return null;
            }
            return empl.getPasswordInfo().isMustBeChanged();
        });
    }

    @Override
    public boolean isAccountLocked()
    {
        return getEmployee().getIsEmployeeLocked();
    }

    @Override
    public String getPassword()
    {
        return getEmployee().getPassword();
    }

    @Override
    public String getTitle()
    {
        return getEmployee().getTitle();
    }

    @Override
    public boolean isEnabled()
    {
        return !getEmployee().isRemoved();
    }

    @Override
    public boolean isLicensed()
    {
        final Set<String> license = getEmployee().getLicense();
        return !(ObjectUtils.isEmpty(license) || license.contains(NOT_LICENSED_USER));
    }

    @Override
    public void setAlias(String alias)
    {
        this.alias = alias;

    }

    @Override
    public String getAlias()
    {
        return alias;
    }

    private void ensureInitialized()
    {
        if (!isInitialized)
        {
            txManager = SpringContext.getInstance().getBean(PlatformTransactionManager.class);
            prefixObjectLoader = SpringContext.getInstance().getBean(IPrefixObjectLoaderService.class);
            isInitialized = true;
        }
    }
}
