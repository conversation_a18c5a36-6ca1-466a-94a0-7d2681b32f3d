package ru.naumen.sec.server.handlers;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.IAuthFailureBehaviour;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#SPNEGO}.
 *
 * <AUTHOR>
 */
@Component("spnegoFailureHandler")
public class SpnegoAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final IAuthFailureBehaviour defaultAuthFailureRedirect;

    @Inject
    public SpnegoAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            IAuthFailureBehaviour defaultAuthFailureRedirect)
    {
        super(messages, userInfoService);
        this.defaultAuthFailureRedirect = defaultAuthFailureRedirect;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException
    {
        defaultAuthFailureRedirect.authFailureRedirect(request, response, exception, "spnegoAuthFailure");
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.SPNEGO;
    }
}
