package ru.naumen.sec.server.handlers;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.IAuthFailureBehaviour;

/**
 * Обработчик неудачного входа в систему с использованием аутентификации
 * типа {@link AuthType#REMOTE_USER}.
 *
 * <AUTHOR>
 * @since 13.02.2018
 */
@Component("preauthFailureHandler")
public class PreauthAuthenticationFailureHandler extends BaseAuthenticationFailureHandler
{
    private final IAuthFailureBehaviour defaultAuthFailureRedirect;

    @Inject
    public PreauthAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            IAuthFailureBehaviour defaultAuthFailureRedirect)
    {
        super(messages, userInfoService);
        this.defaultAuthFailureRedirect = defaultAuthFailureRedirect;
    }

    @Override
    public void processAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception) throws IOException, ServletException
    {
        defaultAuthFailureRedirect.authFailureRedirect(request, response, exception, "preauthFailed");
    }

    @Override
    protected AuthType getAuthenticationType()
    {
        return AuthType.REMOTE_USER;
    }
}
