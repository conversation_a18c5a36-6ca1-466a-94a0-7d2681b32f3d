#ru.naumen.metainfo.server.utils.AbstractCopyOperation
AbstractCopyOperation.finish=Attribut kopiervorgang - Ende.
AbstractCopyOperation.start=Attribut kopiervorgang - Start.
#ru.naumen.metainfo.server.utils.AbstractCopyValidator
AbstractCopyValidator.finish=Attribut validierungs operation - Ende.
AbstractCopyValidator.start=Attribut validierungs operation - Start.
#ru.naumen.metainfo.server.utils.AbstractValueEditor
AbstractValueEditor.success=Attribut validierungs operation erfolgreich abgeschlossen.
AccessKeyDaoImpl.inactive=Die Seite kann nicht angezeigt werden: Der Autorisierungsschlüssel [{0}] ist inaktiv
AccessKeyDaoImpl.lifetimeExpire=Die Seite kann nicht angezeigt werden: Der Autorisierungsschlüssel [{0}] ist abgelaufen
AccessKeyDaoImpl.notFound=Die Seite kann nicht angezeigt werden: Autorisierungsschlüssel [{0}] wurde nicht gefunden oder kann nicht wiederverwendet werden
AdminLogOperation.objectCannotBeCreated=Objekt der Klasse "Record of admin actions log" konnte nicht erstellt werden.
AdminLogOperation.objectCannotBeDeleted=Objekt der Klasse "Record of admin actions log" kann nicht gelöscht werden.
AdminLogOperation.objectCannotBeEdited=Der Aktivitätsprotokolldatensatz des Admin mit UUID "{0}" kann nicht geändert werden.
#ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportListener
Advlist.export.downloadMail.description=Link zum Herunterladen einer Datei mit einer Liste von {0} Objekten der Klasse ''{1}'': <br> <a href="{2}"> {2} </a> <br> <br> Nur Benutzer, die Die exportierte Liste kann eine Datei herunterladen. <br> Die Datei steht zum Download zur Verfügung {3} (bis {4}).
#ru.naumen.core.server.advlist.GetAdvlistSettingsSetActionHandler
AdvlistSettings.authorDeleted=Der Autor der Ansicht wird gelöscht
#ru.naumen.core.server.advlist.DeleteAdvlistSettingsActionHandler
AdvlistSettings.cannotDelete=Sie können keine Einstellungen der Liste des anderen Benutzers löschen.
AllScriptModulesCompilationService.errorUsedOtherModules=Modulressourcen werden in anderen Modulen verwendet: {0}
ApiCriteriaPropertyColumn.localeIsNotAvailable=Zugriff auf <title.{0}> nicht möglich, weil Locale {0} ist nicht in der Liste der verfügbaren.
ApiCriteriaPropertyColumn.localizationDisabled=Zugriff auf <title.{0}> nicht möglich, weil die Lokalisierung des Titelattributs ist deaktiviert.
ApiCriteriaPropertyColumn.baseLocaleIsNotAvailable=Zugriff auf <title.base> nicht möglich, weil ein Verzeichnis-Element hat kein Basis-Locale.
Association.default=Bindung {0}
Association.main=Grundbindung {0}
AttrTemplates.defaultPasswordTitle=Kennwort
#ru.naumen.metainfo.server.utils.AttributeHelper
AttributeHelper.attrNotExists=Attribut ''{1}'' nicht in der Metaklasse ''{0}'' gefunden.
AttributeHelper.attrTypes=Das Attribut ''{0}'' in der Metaklasse ''{1}'' ist vom Typ ''{2}'', aber einer der folgenden Typen wird erwartet: {3}.
AttributeHelper.checkCatalogs=Das Kopieren ist nicht möglich, weil die Attribute zwar Verzeichniselemente (oder Gruppen von Verzeichniselementen) sind, sich aber auf unterschiedliche Verzeichnisse beziehen.
AttributeHelper.checkLinks=Fehler beim Kopieren des Attributs ''{0}'' der Metaklasse ''{1}'' in das Attribut ''{2}'' der Metaklasse ''{3}'', da sich diese Attribute auf die Objekte von beziehen verschiedene Klassen.
AttributeHelper.classNotExists=Klasse ''{0}'' existiert nicht!
AttributeHelper.emptyCopyList=List of objects in which the values of specified attributes are to be copied - empty.
AttributeHelper.equalsTypes=Attributen haven verschiedene Typen: ''{0}'' und ''{1}''.
AttributeHelper.getRelObj=Das Objekt, das dem Objekt ''{0}'' nach Attribut ''{1}'' zugeordnet ist, existiert nicht.
AttributeHelper.typeNotExixts=Typ ''{0}'' in die Klasse ''{1}'' existiert nicht!
#ru.naumen.core.server.AuthorizationServiceImpl.checkPermission
AuthorizationService.accessDenied=Sie haben keine Berechtigung, diesen Vorgang auszuführen
AuthorizationService.accessDeniedInClass=Sie haben keine Berechtigung für {0} in der Klasse {1}
AuthorizationService.accessDeniedInType=Sie haben keine Berechtigung für {0} im Typ {1} der Klasse {2}
AuthorizationService.accessDeniedOn=Sie haben keine Berechtigung für {0}
AuthorizationService.copingCommentToMasterDenied=Sie haben keine Berechtigung, Kommentare vom Slave zum Master-Objekt zu kopieren
AuthorizationService.copingCommentToSlaveDenied=Sie haben keine Berechtigung, Kommentare vom Master auf Slave-Objekte zu kopieren
AuthorizationService.editAttrNoAccess=Sie haben keine Berechtigung zum Bearbeiten des Attributs ''{0}'' in der Klasse / Typ {1}
AuthorizationService.editAttrNotEditable=Das Attribut {0} des Objekts {1} kann nicht bearbeitet werden
AuthorizationService.roleScriptError=Berechnungsfehler der Rolle ''{0}'': \n {1}
AuthorizationService.viewAttrNoAccess=Sie haben keine Berechtigung zum Lesen des Attributs ''{0}'' in der Klasse / Typ {1}
AvdListSemanticFilteringAttrSchemaUpdater.indexCreationException=Beim Aktivieren der Option "Semantische Filterung in erweiterten Listen" ist beim Erstellen des Index ein Fehler aufgetreten. Versuchen Sie erneut, die Option zu aktivieren. Wenn der Fehler weiterhin besteht, wenden Sie sich an den technischen Support.
BeforeAttributeGroupDeleteListener.errorUsedInComplex=Wird im Parameter "Attributgruppe in Liste" für Attribute verwendet: {0}.
BeforeAttributeGroupDeleteListener.errorUsedInComplexParam=Die Gruppe wird in der Einstellung "Attributgruppe in Liste" für den Ereignisaktionsparameter {0} verwendet.
BeforeAttributeGroupDeleteListener.errorUsedInComplexParamPart=''{0}'' (Ereignisaktion ''{1}'')
BeforeAttributeGroupDeleteListener.errorUsedInMobilePropertiesList=Die Gruppe wird in den Inhaltseinstellungen ''{0}'' ({1}) der Objektkarte in der mobilen Anwendung mit dem Code ''{2}'' verwendet.
#ru.naumen.core.server.bo.bop.AbstractCalculateValueMapOperation
CalculateValueMapOperation.attrDoesNotHaveDefValue=Values cannot be calculated using correspondence table and set by default for the following attributes: {1}.
CannotLicensedAndUnlicensed=Der Mitarbeiter {0} kann nicht gleichzeitig lizenziert und nicht lizenziert werden.
ChangesInPermissionSetUnlicUsers.casePrefixFirstCapital=Typ
ChangesInPermissionSetUnlicUsers.casePrefixLowercase=Typ
ChangesInPermissionSetUnlicUsers.classPrefixFirstCapital=Klass
ChangesInPermissionSetUnlicUsers.classPrefixLowercase=Klass
ChangesInPermissionSetUnlicUsers.editAttrGroup=Attribute bearbeiten
ChangesInPermissionSetUnlicUsers.emptyResult=Es gibt keine Änderungen an den Zugriffsrechten für nicht lizenzierte Benutzer. Es ist keine Neukonfiguration der Rechte erforderlich.
ChangesInPermissionSetUnlicUsers.marker=Marker
ChangesInPermissionSetUnlicUsers.viewAttrGroup=Attribute anzeigen
#ru.naumen.metainfo.server.spi.listeners.CheckRoleProfileUsageListener.onApplicationEvent()
CheckRoleProfileUsageListener.roleUsedInProfiles=Role is used in the following profiles: {0}
#ru.naumen.metainfo.server.spi.listeners.CheckUiListener
CheckUiListener.usedInChangeCaseForm=wird beim Festlegen des Formulars zum Ändern des Klassentyps und des Typs verwendet: {0}
CheckUiListener.usedInChangeResponsibleForm=wird in den Einstellungen des Änderungsformulars des verantwortlichen Mitarbeiters der Klassen und Typen verwendet: {0}
CheckUiListener.usedInContent=wird in den Einstellungen des Inhalts {0} verwendet
CheckUiListener.usedInEditForm=wird in den Einstellungen der Bearbeitungsform von Klassen und Typen verwendet: {0}
CheckUiListener.usedInMassEditForm=wird in Einstellungen der Massenbearbeitungsform "{0}" von Klassen und Typen verwendet: {1}
CheckUiListener.usedInNewForm=wird in den Einstellungen der Add-Form von Klassen und Typen verwendet: {0}
CheckUiListener.usedInQuickForm=wird in den Einstellungen des Schnelladditions- und Bearbeitungsformulars "{0}" für Klassen und Typen verwendet: {1}
CheckUiListener.usedInTemplates=wird in den Einstellungen der Vorlagen verwendet: {0}
CheckUiListener.usedInWindow=wird in den Einstellungen der Schnittstelle von Klassen und Typen verwendet: {0}
Comment=Kommentar
Comment.format=Kommentar von {0}. Autor {1}
#ru.naumen.core.server.bo.CommentDtoMapper
CommentDtoMapper.employee=Mitarbeiter
ConcurrentLicenseExceeded=Die maximale Anzahl gleichzeitig arbeitender Benutzer für die Lizenzen {0} wurde überschritten.
CopyOperation.methodFailed=Systemfehler. Parameter an die Methode ungültig übergeben. Kontaktieren Sie Support.
CustomForm.invalidParameterInDateTimeRestrictionScript=Das Wertebeschränkungsskript verweist auf einen nicht vorhandenen Parameter: {0}
CustomForm.invalidParametersInDateTimeRestrictionScript=Das Wertebeschränkungsskript verweist auf nicht vorhandene Parameter: {0}
CustomForms.paramCannotBeEdited=Parameter mit dem Code "{0}" können nicht bearbeitet werden.
DBStorageOperations.unableCompressFileError.fileIsUsed=Datei {0} kann nicht komprimiert werden, da die Datei wird verwendet.
DBStorageOperations.unableDeleteFileError.fileIsUsed=Datei {0} kann nicht gelöscht werden, da Datei verwendet wird.
DBStorageOperations.unableEditFileError.fileIsUsed=Datei {0} kann nicht bearbeitet werden, da Datei verwendet wird.
DBStorageOperations.unableReadFileError.fileIsUsed=Datei {0} kann nicht gelesen werden, da Datei verwendet wird.
#ru.naumen.core.server.bo
DefaultDao.relationNotExists=Eine übergeordnete Verknüpfung zwischen den Objekten ''{0}'' und ''{1}'' besteht nicht.
#ru.naumen.core.server.bo.AbstractBOToSimpleDtObjectMapper
DefaultMetaObjectToDtObjectMapper.superuser=Superuser
#DelAttributeGroupActionHandler
DelAttributeGroupActionHandler.errorMessage=Die Attributgruppe ''{0}'' konnte nicht gelöscht werden. {1}
DelAttributeGroupActionHandler.group=Gruppe
#ru.naumen.metainfo.server.spi.dispatch.sec.DelSecurityRoleActionHandler.execute()
DelSecurityRoleActionHandler.systemRoleCanNotBeDeleted=Can not delete system role "{0}".
#ru.naumen.core.server.filestirage.bcp.DeleteFileOperation
DeleteFileOperation.errorDeleteFileUsed=Die Datei wird vom Objekt ''{0}'' verwendet.
#ru.naumen.metainfo.server.spi.dispatch.wf.DeleteWfProfileActionHandler
EditAttributeForm.invalidAttributeInDateTimeRestrictionScript=Das Wertebeschränkungsskript verweist auf ein nicht vorhandenes Attribut: {0}
EditAttributeForm.invalidAttributesInDateTimeRestrictionScript=Das Wertebeschränkungsskript verweist auf nicht vorhandene Attribute: {0}
#ru.naumen.metainfo.server.spi.dispatch.wf.EditStateEnabledActionHandler
#ru.naumen.core.server.util.ErrorDetails
ErrorDetails.employeeOuRelationInValueMap=Der Mitarbeiter kann nicht aus der Abteilung gelöscht werden. Diese Beziehung wird in der Korrespondenztabelle ''{1}'' verwendet.
ErrorDetails.employeeServiceCallResponsible={0} ist / war verantwortlich für Objekte: {1}.
ErrorDetails.employeeTeamRelatedTo=Sie können die Verknüpfung nicht aufheben. Der Mitarbeiter im Team ist mit den folgenden nicht archivierten Objekten verbunden: {1}
ErrorDetails.employeeTeamRelationInValueMap={0} kann nicht aus dem Team gelöscht werden. Diese Beziehung wird in der Korrespondenztabelle ''{1}'' verwendet.
ErrorDetails.forObject={0} zum Objekt {1}.
ErrorDetails.incompleteList=Die Liste ist unvollständig, die vollständige Liste wird aufgrund von Leistungsbeschränkungen nicht angezeigt.
ErrorDetails.serviceCallClient={0} ist die Gegenpartei offener Anfragen: {1}
ErrorDetails.serviceCallResponsible={0} ist für offene Anfragen verantwortlich: {1}
ErrorDetails.serviceCallResponsible_f={0} oder sein Mitglied ist für offene Anfragen verantwortlich: {1}
ErrorDetails.teamCanNotDeletedCauseResponsible_f=Das Team oder seine Mitglieder sind / waren für die folgenden Objekte verantwortlich: {1}.
ErrorDetails.usedInValueMap={0} wird als Wert in Korrespondenztabellen verwendet: {1}.
EventAction.ErrorCompileGroovyScript=Das Feld "{0}" enthält den Fehler: "{1}"
EventAction.notificationContent=Benachrichtigungstext
EventAction.subject=Thema
EventAction.template=Vorlage
EventAction.warn.disabled=Die Aktion wurde durch die verwendeten Tags deaktiviert. Die Aktion wird bei dem angegebenen Ereignis nicht ausgeführt. Um eine Aktion zu aktivieren, aktivieren Sie alle verwendeten Tags oder deaktivieren Sie die Aktivität.
ExecuteScriptTask=Script
FileAliasValidationOperation.error=Der Datei-Alias ''{0}'' muss mindestens ein Zeichen, jedoch nicht mehr als {1} enthalten, mit dem lateinischen Alphabet beginnen und nur aus lateinischen Buchstaben und Zahlen bestehen.
FillFileAttrOperation.errorFileEmpty=Datei wurde nicht hochgeladen. Leere Datei kann nicht hochgeladen werden.
FillFileAttrOperation.errorLargeFile=Datei wurde nicht hochgeladen. Die Dateigröße überschreitet das von Ihrem Systemadministrator festgelegte Limit ({0} MB).
FillFileAttrOperation.errorLargeFilesGroup=Die Gesamtgröße der hochgeladenen Dateien überschreitet die maximale Größe ({0} MB). Wenn Sie alle ausgewählten Dateien hochladen müssen, wenden Sie sich an Ihren Systemadministrator
FillFileAttrOperation.errorLongTitle=Datei kann nicht hochgeladen werden. Der Dateiname darf nicht mehr als 255 Zeichen enthalten.
#ru.naumen.core.server.filestirage.bcp.FillFileAttrOperation
FillFileAttrOperation.errorNoFileTransferred=Datei wurde nicht hochgeladen. Dies kann aus folgenden Gründen geschehen: Die Größe ist größer als die zulässige Größe, oder das Server-Dateisystem ist nicht beschreibbar oder die Datei ist schädlich.
FillFileAttrOperation.errorOnlyOneFile=In diesem Zusammenhang sollte eine einzelne Datei ausgewählt werden.
FillFileAttrOperation.errorUnacceptableExtension=Datei wurde nicht hochgeladen. Inakzeptable Verlängerung.
#ru.naumen.core.server.license.LicenseChecker
LicenseDisabled=Sie haben keine Berechtigung, sich mit den Lizenzen anzumelden {0}
LicenseExpired=Lizenzen {0} sind abgelaufen
LicenseMissing=Lizenzen {0} nicht festgelegt
#Mail Log
Mail.titleTemplate=Mail № {0} "{1}"
MailAttachmentDescription=Mailanhang; \nSender: {0} \n Thema: {1}
MailAttachmentDescription.emptySubject=unbestimmt
MailAttachmentDescription2=E-Mail-Anhang, \nThema: {1}, \n Absender: {0}
MailLogOperation.objectCannotBeCreated=Objekt der Klasse "Datensatz des Mail-Verarbeitungsprotokolls" kann nicht erstellt werden.
MailLogOperation.objectCannotBeDeleted=Objekt der Klasse "Datensatz des Mail-Verarbeitungsprotokolls" kann nicht gelöscht werden.
MailLogOperation.objectCannotBeEdited=Der Datensatz des E-Mail-Verarbeitungsprotokolls mit der UUID "{0}" kann nicht geändert werden.
MailReaderSchedulerTask.title=Verarbeitung eingehender E-Mails
MassEditFormNotSetupError=Die Massenbearbeitung ist für Objekte vom Typ {0} nicht konfiguriert.
MassOperationTitle.delete=löschen
MassOperationTitle.edit=bearbeiten
MetainfoClusterChange.error=Der Vorgang kann nicht ausgeführt werden, da die Änderung der Metainformationen blockiert ist und eine Aktualisierung durchgeführt wird. Bitte versuchen Sie es nach einer Weile erneut.
MetainfoClusterChange.outOfSyncNode.error=Das Ändern von Metainformationen auf einem nicht synchronen Knoten ist verboten.
MetainfoUtils.clazz=Klasse ''{0}''
MetainfoUtils.of.clazz=von Klasse ''{0}''
MetainfoUtils.type=Typ ''{0}''
MobileAttributeGroupTitle=Karte in der mobilen Anwendung ({0})
MobileNavigation.addFormLinksChapter=Hinzufügen
MobileNavigation.listLinksChapter=Verfügbare Listen
NoMoreEmployeeLicensesForGroup=Das Limit der Mitarbeiterzahl für die Lizenz {0} ist erreicht
NoMoreSuperPersonLicenses=Das Limit der Anzahl der Superuser ist erreicht
Notification.allEmptyEmails=Among the recipients there is no employee with the filled e-mail address.
Notification.brokenFeedbackAddress=Falsche Adresse des technischen Supports ({0})
Notification.brokenSenderAddress=Absenderadresse ({0}) ist ungültig
Notification.currentRecipientTogetherMethodCc=Der Parameter "currentRecipient" kann nicht gleichzeitig mit der Methode "notification.cc" oder "notification.ccEmployee", "notification.bcc", "notification.bccEmployee"." verwendet werden.
Notification.emptyEmails=E-Mail-Adressen sind nicht angegeben
Notification.noOne=zu niemandem
Notification.noRecipients=Benachrichtigungsempfänger nicht angegeben
ObjectActions.action=Aktion
ObjectActions.image=Bild
ObjectActions.position=Position
OperationHelper.LinkAdded=linked to objects:
OperationHelper.LinkSevered=unlinked from objects:
OperationHelper.RestoreObjectSeparately=The object must be restored from the archive separately
OperationHelper.added={0} cannot be added for the following reasons:\n{2}
OperationHelper.added_f={0} cannot be added for the following reasons:\n{2}
OperationHelper.agreementArchived=Agreement ''{0}'' is in archive.
OperationHelper.changed={0} ''{1}'' kann aus folgenden Gründen nicht geändert werden:\n{2}
OperationHelper.changed_f={0} ''{1}'' kann aus folgenden Gründen nicht geändert werden:\n{2}
OperationHelper.clientArchived=Gegenpartei ''{0}'' befindet sich im Archiv.
OperationHelper.commentsLimitReached=Kommentar kann nicht hinzugefügt werden, da die Grenze der dem Objekt hinzugefügten Kommentare erreicht ist.
OperationHelper.copied={0} ''{1}'' kann aus folgenden Gründen nicht kopiert werden:\n{2}
OperationHelper.deleted={0} ''{1}'' kann aus folgenden Gründen nicht gelöscht werden:\n{2}
OperationHelper.deleted_f={0} ''{1}'' kann aus folgenden Gründen nicht gelöscht werden:\n{2}
OperationHelper.error=The operation cannot be performed for the following reasons:\n{0}
#ru.naumen.bcp.server.operations.OperationHelper
OperationHelper.errorWithObject={0} ''{1}'' cannot be {2}.\n{3}
OperationHelper.etc=usw.
OperationHelper.loopError=Loop dependency between the attributes {1} detected
OperationHelper.removed={0} ''{1}'' kann aus folgenden Gründen nicht archiviert werden:\n{2}
OperationHelper.removed_f={0} ''{1}'' kann aus folgenden Gründen nicht archiviert werden:\n{2}
OperationHelper.replaced={0} ''{1}'' kann aus folgenden Gründen nicht ersetzt werden:\n{2}
OperationHelper.replaced_f={0} ''{1}'' kann aus folgenden Gründen nicht ersetzt werden:\n{2}
OperationHelper.restored={0} ''{1}'' kann aus folgenden Gründen nicht aus dem Archiv wiederhergestellt werden:\n{2}
OperationHelper.restored_f={0} ''{1}'' kann aus folgenden Gründen nicht aus dem Archiv wiederhergestellt werden:\n{2}
OperationHelper.serviceArchived=Service ''{0}'' is in archive.
OperationUtils.agreementIsNull=Request cannot be added for the following reasons: 1. Required attributes not filled in: SLA
OperationValidator.canCopyError=Objects of this type can not be copy.
OperationValidator.cantBeEmpty=Must be filled in
#ru.naumen.core.server.bo.bop.OperationValidator.checkCatalogItemCode(String, ClassFqn)
OperationValidator.catalogItemWithCodeAlreadyExist={0} with the code ''{1}'' already exists!
OperationValidator.changeServiceMasterMassProblemRestriction=Service kann nicht geändert werden. Objekt {0} bezieht sich auf die Slave-Objekte
OperationValidator.changeServiceSlaveMassProblemRestriction=Service kann nicht geändert werden. Objekt {0} bezieht sich auf die Massenanforderung
OperationValidator.changeTypeMasterMassProblemRestriction=Typ kann nicht geändert werden. Objekt {0} bezieht sich auf die Slave-Objekte
OperationValidator.changeTypeSlaveMassProblemRestriction=Typ kann nicht geändert werden. Objekt {0} bezieht sich auf die Massenanforderung
#ru.naumen.core.server.bo.bop.OperationValidator.checkUnique(T, String, Attribute, Object)
OperationValidator.checkAttrUnique.error=Attribute {0} must be unique. Object {1} with this value of the attribute already exists: {2}.
OperationValidator.checkChangeStateVisibleInState.error=The action is not set up in state ''{0}'' for the type of this object
#ru.naumen.core.server.bo.bop.OperationValidator.checkParent(P, C)
OperationValidator.checkParent.error.parentIsAChild={1} ''{0}'' kann nicht verschoben werden. Das Objekt kann nicht das übergeordnete Objekt für sich selbst sein.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentIsNotDescendant(P, T)
OperationValidator.checkParentIsNotDescendant.error={1} ''{0}'' kann nicht in das Objekt ''{3}'' der Klasse {2} verschoben werden. Das vermeintliche Elternteil ist das Kind des zu verschiebenden Objekts.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentIsNotRemoved(P, T)
OperationValidator.checkParentIsNotRemoved.error={1} ''{0}'' kann nicht in das Objekt ''{3}'' der Klasse {2} verschoben werden. Das übergeordnete Objekt befindet sich im Archiv. Stellen Sie das übergeordnete Objekt wieder her oder wählen Sie einen neuen Speicherort für das Objekt.
OperationValidator.checkParentIsNotRemoved.folderUnArchiveError=Der Ordner ''{0}'' kann nicht aus dem Archiv wiederhergestellt werden. Das übergeordnete Objekt befindet sich ebenfalls im Archiv. Stellen Sie das übergeordnete Objekt wieder her oder wählen Sie einen neuen Speicherort für das Objekt.
OperationValidator.checkParentIsNotRemoved.unArchiveError={1} ''{0}'' kann nicht aus dem Archiv wiederhergestellt werden. Das übergeordnete Objekt befindet sich ebenfalls im Archiv. Stellen Sie das übergeordnete Objekt wieder her oder wählen Sie einen neuen Speicherort für das Objekt.
OperationValidator.checkParentRelation.error.parentNotAllowed={1} "{0}" kann nicht in das Objekt ''{3}'' der Klasse {2} verschoben werden.
#ru.naumen.core.server.bo.bop.OperationValidator.checkParentRelation(P, T)
OperationValidator.checkParentRelation.error.parentNotNull={1} ''{0}'' kann nicht in den Hierarchiestamm verschoben werden.
#ru.naumen.core.server.bo.bop.OperationValidator.checkPermittedTypesRestriction()
OperationValidator.checkPermittedTypesRestriction.Multy.error=Values ''{0}'' do not meet type restrictions of the attribute ''{1}''
OperationValidator.checkPermittedTypesRestriction.Single.error=Value ''{0}'' do not meet type restrictions of the attribute ''{1}''
OperationValidator.checkToolPrecenseOnCards.error=The action is not set up for the type of this object
OperationValidator.exceedsMaxLength=The value length exceeds maximum size {0}
OperationValidator.illegalContains=Contains illegal characters
OperationValidator.postFillAttrsAreNotFilled={0}: Beim Status ''{1}'' beenden Sie die erforderlichen Attribute, die nicht ausgefüllt wurden - {2}
OperationValidator.preFillAttrsAreNotFilled={0}: Bei Eingabe des Status ''{1}'' sind Attribute nicht ausgefüllt - {2}
OperationValidator.reqCompAttrsIsNotSet=ist erforderlich "{0}" ist erforderlich. Eines der folgenden Attribute sollte ausgefüllt werden: {1}.
#ru.naumen.core.server.bo.bop.OperationValidator.checkAttrsRestrictions(T)
OperationValidator.requiredAttrsIsNotSet=Die folgenden erforderlichen Attribute wurden nicht ausgefüllt: {1}.
#ru.naumen.core.server.bo.team.FinalTeamValidationOperation
OperationValidator.teamShouldContainItsLeader=Der Leiter des Teams muss sein Mitglied sein
OperationValidator.validateBOAfterProcess.error.caseIsEmpty=Objekttyp nicht angegeben
#ru.naumen.core.server.bo.bop.OperationValidator.validateBOAfterProcess()
OperationValidator.validateBOAfterProcess.error.caseIsRemoved=Objekttyp gelöscht
OperationValidator.valueFiltered=''{0}'' cannot be the value of the attribute ''{1}''
PeriodicTrigger.interval.mandatory=Duration must be specified.
ProcessMailEvent.failure={0} was processed by the mail processing rule {1} with an error:
ProcessMailEvent.interrupt=Mail processing has interrupted due server stopped
ProcessMailEvent.issues={0} was processed by the mail processing rule {1} with the peculiarities:
ProcessMailEvent.success={0} was successfully processed by the mail processing rule {1}.
PropertiesFilesContainer=Configuration files (*.properties)
PropertiesXmlContainer=Configuration files (*.xml)
Push.errorInScript=Fehler im Skript
PushMobile.cert.check.error=Beim Überprüfen des Zertifikats ist ein Fehler aufgetreten
PushMobile.error=Auf der GCM-Serviceseite ist ein Fehler aufgetreten
PushMobile.silentModeOn=Der stille Modus ist aktiviert
PushPresentationType.alwaysInTheInterface=In der Schnittstelle immer. Optionale Browser-Benachrichtigung, wenn der Tab inaktiv ist
PushPresentationType.interfaceAndBrowserNotices=In der Schnittstelle und der Browser-Benachrichtigung
PushPresentationType.onlyExternalPush=Nur Browser-Benachrichtigung
PushPresentationType.onlyInTheInterface=Only in the interface
ReceiveMailEvent.folder=folder {0} - {1}
ReceiveMailEvent.head=Scheduler task {0} has been successfully connected to the incoming mail server {1}. The number of detected messages:
ReceiveMailEvent.logErrorMailStorage=Unsuccessful attempt № {0} of sending the mail {1} for processing, there are {2} attempts left
ReceiveMailEvent.received=Number of letters sent to the processing - {0}
ReceiveMailTask=Incoming mail processing
RecipientAddressRejected=Recipient address rejected: mail is not deliverable.
RejectReason.badFormat=ungültiges Nachrichtenformat
RejectReason.bigAttachment=Anhang ist zu groß
RejectReason.bigAttachment.oneFileMessage=Anhang "{0}" hat die zulässige Größe überschritten - {1} MB
RejectReason.bigAttachment.sumFilesMessage=Die Gesamtdateigröße hat die zulässige Größe - {0} MB überschritten
RejectReason.bigAttachments=Anhänge sind zu groß
RejectReason.blacklist=Das Feld ''{0}'' enthält einen Wert, der die im Verzeichnis ''{1}'' festgelegten Filterbedingungen nicht erfüllt
RejectReason.blacklist.field.body=Inhalt
RejectReason.blacklist.field.sender=Absender
RejectReason.blacklist.field.subject=Thema
RejectReason.clientNotFound=konnte den Client nicht bestimmen, um die Anforderung zu binden
RejectReason.contactNotFound=Kontaktperson für verbindliche Anfrage konnte nicht ermittelt werden
RejectReason.impossibleAttachment=ungültiger Anhang
RejectReason.impossibleAttachment.message=Anhang "{0}" ist vom ungültigen Typ
RejectReason.invalidEmail=Falsche E-mail
RejectReason.invalidEmail.message=Ungültiges Absenderadressformat - {0}
RejectReason.respondBodyIntro=Lieber Benutzer!\nIhr Brief wurde abgelehnt.\n\nGrund, warum der Brief abgelehnt wurde:
RejectReason.maliciousAttachment=Die Datei ist bösartig
RejectReason.other=Nachricht konnte nicht verarbeitet werden
ReportContentRename.export=exportieren
ReportContentRename.refresh=Aktualisierung
ReportContentRename.save=Speichern …
RestApi.createLink=Hinzufügen
RestApi.deleteLink=Löschen
RestApi.editLink=Bearbeiten
#ru.naumen.core.server.rest.accesskeys.AccessKeyDaoImpl
RestServiceController.emptyAccessKey=Sie müssen den Autorisierungsschlüssel im Parameter accessKey angeben
RestServiceController.execScriptLegacyPattern=[а-яА-ЯёЁA-Za-z0-9,\\[\\]$''\\"_\\s\\*\\-%@&\\\\]*
SaveUIActionHandlerBase.changeResponsibleForm=Verantwortliche Form ändern
SaveUIActionHandlerBase.objectCard=Objektkarte
#ru.naumen.core.server.bo.bop.SetEmployeeRemovedOperation
SetEmployeeRemovedOperation.lastPerfOfRespTeam=Mitarbeiter ''{2}'' ist der einzige Darsteller im Team ''{0}'', der für die Objekte verantwortlich ist: {1}
SetEmployeeRemovedOperation.respWithinTeamForObj=Innerhalb des Teams ist ''{0}'' Mitarbeiter ''{2}'' für die Objekte verantwortlich: {1}
SetMetaClassOperation.requiredAttrsIsNotSet={0}: Erforderliche Attribute nicht ausgefüllt - {1}
SetStateOperation.changeState=Ändern Sie den Status von ''{0}'' in ''{1}'' in Klasse / Typ ''{2}''
SetStateOperation.objAlreadyHasNewState=Das Objekt befindet sich bereits im neuen Status
SetStateOperation.stateIsNotDeclared=Status mit dem Code ''{0}'' nicht definiert
SilentModeOrMailServerDisabled=ausgehende E-Mails deaktiviert oder SilentMode aktiviert
Timer.Status.ACTIVE=Aktiv
Timer.Status.EXCEED=Abgelaufen
Timer.Status.NOTSTARTED=Warten
Timer.Status.PAUSED=Pause
Timer.Status.STOPED=Gestoppt
ToolTitle.add=Hinzufügen
ToolTitle.addComment=Kommentar hinzufügen
ToolTitle.addDeleteObjButton=Links hinzufügen/entfernen
ToolTitle.addFile=Datei hinzufügen
ToolTitle.addObject=Objekt hinzufügen
ToolTitle.addRelation=Link hinzufügen
ToolTitle.addSc=Ticket hinzufügen
ToolTitle.changeAssociation=Zuordnung ändern
ToolTitle.changePassword=Kennwort ändern
ToolTitle.changeState=Status ändern
ToolTitle.changeType=Typ ändern
ToolTitle.copy=Kopieren
ToolTitle.copyLinkToList=Link in die Liste kopieren
ToolTitle.createNewReportWithCurrentParameters=Bericht mit aktuellen Parametern hinzufügen
ToolTitle.delete=Löschen
ToolTitle.deleteLink=Link löschen
ToolTitle.download=Herunterladen
ToolTitle.edit=Bearbeiten
ToolTitle.editResponsible=Verantwortlich ändern
ToolTitle.exportAdvlist=Liste exportieren
ToolTitle.exportReportButton=Speichern...
ToolTitle.extendedSearchParams=Suchparameter
ToolTitle.filtration=Filtration
ToolTitle.massDelete=löschen
ToolTitle.massEdit=Bulk-Bearbeitung
ToolTitle.more=Mehr
ToolTitle.move=Bewegung
ToolTitle.openMassServiceCallForm=Massenoperationen
ToolTitle.openObjectCard=Objektkarte öffnen
ToolTitle.openPreview=Vorschau öffnen
ToolTitle.parameters=Parameter ...
ToolTitle.print=Drucken
ToolTitle.rebuildReport=Wiederaufbauen
ToolTitle.refresh=Aktualisierung
ToolTitle.refreshList=Liste aktualisieren
ToolTitle.remove=Archiv
ToolTitle.resetGlobalDefaultSettings=Ansicht zurücksetzen
ToolTitle.restore=Aus dem Archiv wiederherstellen
ToolTitle.savePresentation=Ansicht speichern
ToolTitle.share=Teilen
ToolTitle.showRelated=Zeigen den Verlauf verwandter Objekte
ToolTitle.showRemoved=Archiv anzeigen
ToolTitle.showUnRemoved=Archiv verlassen
ToolTitle.sort=Sortieren
ToolTitle.testMetric=Prüfen
UserSessionsMaxCountExceeded=Die maximale Anzahl von Sitzungen dieses Benutzers wurde überschritten.
ValidateDeleteCatalogEvent.message=Verzeichnis ''{0}'' wird im Parameter ''Schnittstellenseiten'' der Lite-Administratorschnittstelle angegeben.
ValidateDeleteSecurityGroupEvent.message=Die Benutzergruppe ''{0}'' wird im Parameter ''Verfügbar für Benutzergruppen'' der Lite-Administratorschnittstelle angegeben.
VoiceCreationHelper.exceptionInScript=Beim Ausführen des Parameterverarbeitungsskripts ist ein Fehler aufgetreten. Melden Sie es der für die Systemoptimierung zuständigen Person, damit sie es beheben kann.
abstractBO.domain.1=Objektattribute anzeigen
abstractBO.domain.10=Objekt löschen
abstractBO.domain.11=Objektkarte anzeigen
abstractBO.domain.12=Objekttyp ändern
abstractBO.domain.13=Objekt archivieren
abstractBO.domain.14=Objekt aus dem Archiv wiederherstellen
abstractBO.domain.15=Objekt verschieben
abstractBO.domain.16=Objekt kopieren
abstractBO.domain.17=Kommentare hinzufügens
abstractBO.domain.18=Private Kommentare hinzufügen
abstractBO.domain.19=Kommentare löschen
abstractBO.domain.2=Objektattribute bearbeiten
abstractBO.domain.20=Kommentare bearbeiten
abstractBO.domain.21=Kommentare anzeigen
abstractBO.domain.22=Private Kommentare anzeigen
abstractBO.domain.23=Objektverlauf anzeigen
abstractBO.domain.24=Datei anzeigen
abstractBO.domain.25=Datei hinzufügen
abstractBO.domain.26=Datei löschen
abstractBO.domain.27=Dateiautor
abstractBO.domain.28=Kommentar Autor
abstractBO.domain.29=In der Suchergebnisliste anzeigen
abstractBO.domain.3=Kommentare
abstractBO.domain.4=Angehängte Dateien
abstractBO.domain.5=Andere Berechtigungen
abstractBO.domain.6=Attribute, immer zum Lesen verfügbar
abstractBO.domain.7=Andere Attribute
abstractBO.domain.9=Objekt hinzufügen
accessDeniedError=Sie haben keine Berechtigungen für die Systemeinstellungen.
actions=Aktionen
activeUsers=Aktive Benutzer
add=Hinzufügen
addCommentInStateError=Kommentar kann dem Objekt im Status "{0}" nicht hinzugefügt werden.
adminLiteInterface=Leichte Adminschnittstelle
administration=Verwaltung
advListProperties=Komplexe Listen
advListRichTextViewMode=Löschen Text in Feldern wie "Text in RTF" aus Stilen, um Browserressourcen zu sparen
advListRichTextViewMode.RemoveStyles=Ja, für alle Darstellungen (empfohlen)
advListRichTextViewMode.RemoveStylesOnlyFromSafe=Ja, außer für Darstellungen mit der Markierung "unsicher"
advListRichTextViewMode.ShowAsIs=Nein (führt zu einer erhöhten Nutzung der Browser-Ressourcen)
agreement.domain.AgreementRecipient=SLA-Verbraucher
agreement.domain.AgreementServiceResponsible=Kurator des Dienstes, bezogen auf die SLA
agreement.domain.AgreementSupplier=SLA-Anbieter
agreementServiceCaption=Vereinbarung/Dienst
agreementServiceEditPrs=Darstellung zu bearbeiten
agreementServiceSetting=Wert des Feldes "Vereinbarung/Dienst"
agreements=SLA''s
#metainfo export
allSettings=Alle Einstellungen
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarn=Die Aktion steht nicht lizenzierten Benutzern zur Verfügung. Jede Änderung macht es nicht verfügbar
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarnIsExpired=Für nicht lizenzierte Benutzer ist keine Aktion verfügbar, da die Zeit in der Lizenzdatei abgelaufen ist
allowedForUnlicensedActionsAttentionMsgService.eventActionCardWarnIsWrongCheckSum=Die Aktion, das zugehörige Skript oder Modul wurde geändert. Aus diesem Grund ist die Ereignisaktion für nicht lizenzierte Benutzer nicht verfügbar
allowedForUnlicensedActionsAttentionMsgService.scriptCardWarn=Durch Ändern des Skripts wird die Aktion(-en) {0} für nicht lizenzierte Benutzer blockiert
allowedForUnlicensedActionsAttentionMsgService.scriptModuleCardWarn=Durch Ändern des Moduls wird die Aktion(-en) {0} für nicht lizenzierte Benutzer blockiert
allowedForUnlicensedMarkerValidator.actionsNotAllowed=Aktion(-en) {0} für nicht lizenzierte Benutzer nicht verfügbar
anotherSessionStarted=Dieses Konto wird auf einem anderen Computer verwendet. Geben Sie Ihren Login und Ihr Kennwort ein, um sich anzumelden.
application.loading=Ladevorgang, bitte warten.
associatedSuperUser=Sie können sich nicht anmelden. Das Superuser-Konto ist dem Benutzerkonto zugeordnet. Trennen Sie Konten oder wenden Sie sich an Ihren Administrator.
attention=Achtung!
attrValue=Attributwert
attribute=Attribut
attribute.creation.fail=Attribut kann nicht hinzugefügt werden.
attribute.edit.fail=Attribut kann nicht bearbeitet werden.
attributesGroups=Attributgruppen
authenticationFailure=Sie können sich nicht anmelden. Falsche Anmeldung und / oder falsches Kennwort. Bitte versuchen Sie es erneut.
authorization.error=Autorisierungsfehler.
authorization.errorEmployeeForIntegration.loginForm=Sie können sich nicht anmelden. Dies ist ein Dienstkonto. Wenden Sie sich an Ihren Administrator.
availableForGroupOfUsers=Verfügbar für Benutzergruppen
blueOrange=Blau-Orange
blueViolet=Blau-Violett
branded=Gebrandmarkt
breakLink=Brechen die Bindung
cannotLoadInputmaskExtensionsFileWrongFormat=Ein Fehler ist aufgetreten. Eingabefeld mit Maskenerweiterungsdatei kann nicht hochgeladen werden. Ungültiges Dateiformat ausgewählt. {0}
cantBeEmpty=Das Feld muss ausgefüllt werden.
catalog=Verzeichnis
catalogAttributes=Handbuch-Attribute
catalogFolder=Kataloge
catalogItems=Verzeichniselemente
catalogs=Verzeichnisse
categories=Kategorien
category=Kategorie
certificateErrorLoading=Fehler beim Hochladen des Zertifikats.
checkStateConditionsOperation.error1={0} kann nicht in den Status ''{1}'' übertragen werden: {2}
checkStateConditionsOperation.error2={0} ''{1}'' kann nicht in den Zustand ''{2}'' übertragen werden: {3}
checkStateConditionsOperation.postConditionError1=Beim Ausführen der Bedingung "{0}" beim Beenden des Zustands "{1}" ist ein Fehler aufgetreten. \nFehlertext: {2}
checkStateConditionsOperation.preConditionError1=Bei der Ausführung der Bedingung durch das Skript ''{0}'' beim Eintritt in den Status "{1}" ist ein Fehler aufgetreten. \nFehlertext: {2}
class=Klasse
classFastSearch=Search
classMetainfoServiceImpl.actionBar.delete=Löschen
classMetainfoServiceImpl.actionBar.edit=Bearbeiten
classMetainfoServiceImpl.actionBar.move=Bewegung
classMetainfoServiceImpl.actionBar.remove=Archiv
classMetainfoServiceImpl.actionBar.unremove=Aus dem Archiv wiederherstellen
classMetainfoServiceImpl.changeCaseFormDefaultCaption=Form des Änderungsfalls
classMetainfoServiceImpl.changeResponsibleFormDefaultCaption=Verantwortliches Änderungsformular
classMetainfoServiceImpl.defaultCaseTitle=Typ
classMetainfoServiceImpl.editFormDefaultCaption=Formular bearbeiten
classMetainfoServiceImpl.massEditFormsCaption=Bulk-Bearbeitungsformulare
classMetainfoServiceImpl.newEntryFormDefaultCaption=Formular hinzufügen
classMetainfoServiceImpl.quickFormsCaption=Formularen für Schnelles Hinzufügen und Bearbeiten
classMetainfoServiceImpl.systemGroupName=Systemattribute
classMetainfoServiceImpl.validationError=Fehler bei der Validierung der Metainformation. Der zu löschende Typ wird wahrscheinlich in den Einstellungen anderer Typen verwendet.
#ru.naumen.core.server.script.spi.ApiUtils
classOfObjectFailure=Object ''{0}'' is not the object of the class ''{1}''.
classSettings=Klasseneinstellungen
classes=Klassen
clientLang=Client-Sprache
code=Code
comment.title=Kommentar ''{2}'' von {1}, Autor ''{0}''.
commentAuthor=Kommentar Autor
commentText=Text des Kommentars
commonSearchSettings.analyzerIsUsed.casePattern=Typ "{0}" (Klasse "{1}")
commonSearchSettings.analyzerIsUsed.classPattern=Klasse "{0}"
commonSearchSettings.analyzers=Im System verfügbare Analysatortypen
commonSearchSettings.handlerDoesNotExists=Handler für Feld ''{0}'' nicht gefunden!
commonSearchSettings.incorrectValue=Falscher Wert für Feld ''{0}''!
commonSearchSettings.maxSearchResults=Maximale Anzahl von Objekten in Suchergebnissen
commonSearchSettings.useAdvancedSearch=erweiterte Suchsprache verwenden
commonSearchSettings.useRightsInLists=Einen Mechanismus zum Verwalten von Benutzerrechten für Objekte verwenden, die in der Liste der Suchergebnisse angezeigt werden
communicationError=Beim Austausch mit dem Server ist ein Fehler aufgetreten. Versuchen Sie, die Seite zu aktualisieren, und wenden Sie sich an Ihren Systemadministrator, um das Problem zu beheben.
complexEmptyAttrGroups=Deaktivieren Sie "Erweiterte Linkbearbeitung" oder wählen Sie eine Attributgruppe für eine oder mehrere Formularlisten aus.
complexTeamEmptyAttrGroup=Wählen Sie eine Attributgruppe für die Klasse {0} aus. Auf dem erweiterten Formular zur Bearbeitung der Verbindung können Sie ein Objekt der Klasse {1} nur innerhalb des Objekts der Klasse {0} auswählen.
compressionRatio=Bildkomprimierungsverhältnis in "RTF-Text"-Attributen
configurations=Konfigurationen
connection.validation.password=Kennwort
contents=Inhalt
copy=Kopieren
coreOUInitializer.ouTitle=Abteilung
coreRootInitializer.rootTitle=Firma
cti=CTI
customObjectActions.noRightsToEditAttribute=Sie haben keine Berechtigung zum Bearbeiten des Attributs "{0}" in der Klasse/Typ {1}
customObjectActions.notEditableAttribute=Die Aktion kann nicht ausgeführt werden, da das Attribut "{0}" ({1}) nicht bearbeitet werden kann
databaseConnectionError=Datenbankverbindungsfehler. Bitte wenden Sie sich an Ihren Administrator.
databaseAcquireLockTimeoutError=Die Operation konnte nicht durchgeführt werden. Fehler beim Abrufen einer Sperre für die Datenbank, um das Schema zu ändern. Bitte versuchen Sie es nach einer Weile erneut.
dateTimeInterval.DAY={0} Tage
dateTimeInterval.DAYForms=Tag,Tage,Tage,Tage
dateTimeInterval.HOUR={0} Stunden
dateTimeInterval.HOURForms=Stunde,Stunden,Stunden,Stunden
dateTimeInterval.MINUTE={0} Minuten
dateTimeInterval.MINUTEForms=Minute,Minuten,Minuten,Minuten
dateTimeInterval.MONTH={0} Monate
dateTimeInterval.MONTHForms=Monat,Monate,Monate,Monate
dateTimeInterval.SECOND={0} Sekunden
dateTimeInterval.SECONDForms=Sekunde,Sekunden,Sekunden,Sekunden
dateTimeInterval.WEEK={0} Wochen
dateTimeInterval.WEEKForms=Woche,Wochen,Wochen,Wochen
dateTimeInterval.short_DAY={0} T
dateTimeInterval.short_HOUR={0} Std
dateTimeInterval.short_MINUTE={0} min
dateYearValidationError=Das Feld muss ein Datum im Bereich von {0} bis {1} enthalten.
delete=Löschen
deleteCaption=Löschen bestätigen
deleteMessage=Möchten Sie wirklich {0} ''{1}'' löschen?
descendantsClassesSettings=Verschachtelte Klasseneinstellungen
descendantsTypesSettings=Verschachtelte Typeinstellungen
description=Beschreibung
design.CNDesign.HelpString=Die Struktur '{'CN'}' wird vom System durch die Nummer des Clusterknotens ersetzt
deutschLang=Deutsche Sprache
domain.RestAttributesNoun=Andere Attribute
download=Herunterladen
edit=Bearbeiten
editCommentInStateError=Der Kommentar kann im Objekt im Status "{0}" nicht bearbeitet werden.
embeddedAppParametersNotShow=Die Konfigurationsdatei von Parameter der integrierten Anwendung "{0}" enthält einen Fehler. Parameter für die integrierte Anwendung werden nicht angezeigt.
embeddedAppParametersParseError=Bei der Berechnung der Inhaltsparameter ist ein Fehler aufgetreten (siehe Anwendungsprotokoll auf WARN-Ebene).
embeddedApplication.NotFound=Anwendung nicht gefunden.
embeddedApplications=Anwendungen
#employee.domain.xml
employee.domain.ChangePasswd=Kennwort ändern
employee.domain.currentUser=Aktueller Mitarbeiter
employee.domain.editRestAttributes=Andere Attribute
employee.domain.userHead=Direkter Leiter des Mitarbeiter
employee.domain.userOuEmployee=Mitarbeiter der Mitarbeiterabteilung
employee.domain.userTeamEmployee=Mitglied des Mitarbeiterteams
employee.domain.userTeamHead=Leiter des Mitarbeiterteams
employee.domain.userUpperOuEmployee=Mitarbeiter der oberen Abteilung
employee.domain.userUpperOuHead=Leiter der oberen Abteilung
#employee.window.xml
employee.window.ChangePassword=Kennwort ändern
employee.window.EmployeeCard=Mitarbeiterkarte
employee.window.Teams=Teams
#ru.naumen.core.server.catalog.valuemap.SetValueMapItemRowSetOperation
emptyValueMapTargetAttrValue=In the correspondence table values of the definable attributes: ''{0}'' must be specified.
englishLang=Englisch
enterPassword=Geben Sie das Kennwort ein, um sich in der Admin-Oberfläche anzumelden
errorInvalidXmlChar=Error. Tag <{0}> enthält den unzulässigen Wert ''{1}'', der nicht in XML gespeichert werden kann.
escalation=Eskalation
escalationActions=Aktionen
escalationSchemes=Eskalationsschemata
escalationValueMaps=Korrespondenztabellen
etc=usw.
eventActionType.addComment=Kommentar zum Objekt hinzufügen
eventActionType.addFile=Datei an Objekt anhängen
eventActionType.changeResponsible=Verantwortlich ändern
eventActionType.changeState=Statusänderung
eventActionType.edit=Objekt bearbeiten
eventActionType.editComment=Kommentar bearbeiten
eventActions=Liste der Ereignisaktionen
eventActionUsagePlace=Ereignisaktion "{0}"
eventActionUsagePlaceType=Typ "{0}"
eventCleanerJob=Parameter der Reinigungsaufgabe für das Ereignisprotokoll
eventCleanerJobSchedulerEnd=Die Reinigungsaufgabe {0} wurde erfolgreich abgeschlossen. {1} der aus tbl_event entfernten Einträge
eventCleanerJobSchedulerError=Bei der Reinigungsaufgabe {0} ist ein Fehler aufgetreten
eventCleanerJobSchedulerStart=Reinigungsaufgabe {0} ist in Bearbeitung
eventCleanerJobSettings=Verwaltung von Ereignisprotokollen
eventCleanerJobSettingsEdit=Bearbeiten von Parametern für Reinigungsaufgaben
eventCleanerJobSettingsToggleRule=Aktivieren/Deaktivieren der Speicherregel für das Ereignisprotokoll
eventCleanerJobSettingsView=Anzeigen der Parameter für Reinigungsaufgaben
eventCleanerJobSettingsViewRule=Anzeigen der Speicherregeln von Ereignisprotokollen
eventService.actionConditionsError=Bedingung ''{0}'' für Ereignisaktion ''{1}'' ist nicht erfüllt: {2}
eventService.actionConditionsFailed=Fehler beim Ausführen des Skripts der Bedingung ''{0}'' für die Ereignisaktion ''{1}'': {2}
eventService.actionEventFailed=Fehler beim Ausführen der Aktion für Ereignis ''{0}'': {1}
eventService.add=Objekt ''{0}'' wurde erstellt
eventService.addMassProblemSlave=Link mit untergeordnetem Objekt hinzugefügt: {0}
eventService.attach_file=Datei ''{0}'' angehängt
eventService.authorSuperuser=Superuser
eventService.changeCase=Geänderter Objekttyp ''{2}'': ''{0}'' -> ''{1}''
eventService.changeLicense=Benutzer ''{0}'' hat eine Lizenz ''{1}'' zugewiesen
eventService.changePasswd=Benutzer ''{0}'' hat das Kennwort geändert.
eventService.changeResponsible=Verantwortlich geändert: {0} -> {1}
eventService.changeStatus=Das Objekt ''{0}'' hat Status in {1} geändert
eventService.commentAdd=Kommentar ''{2}'' von {1} hinzugefügt, Autor ''{0}''.
eventService.commentDel=Kommentar ''{2}'' von {1} entfernt, Autor ''{0}''.
eventService.commentEdit=Kommentar von {1} geänderter, Autor ''{0}'': \n\t{2}
eventService.del={0} ''{1}''. Objekt gelöscht.
eventService.delError=Das Objekt ''{0}'' wurde aus folgenden Gründen nicht gelöscht: \n{1}
eventService.delete_file=Datei ''{0}'' gelöscht
eventService.edit=Objekt ''{0}'' geändert:\n\t{1}
eventService.edit_file=Objektdatei ''{0}'' geändert:\n\t{1}
eventService.escalationChanged=Satz von Eskalationsschemata geändert: {0}
eventService.escalationLevel=Ausgelöste Ebene {0} aus Schema ''{1}'', Aktion ''{2}''
eventService.escalationTime=Eskalationsstufen Zeit geändert ''{0}'': \n{1}
eventService.eventCategory.actionConditionsError=Bedingung für Ereignisaktion nicht erfüllt
eventService.eventCategory.actionConditionsFailed=Fehler beim Ausführen des Bedingungsskripts für die Ereignisaktion
eventService.eventCategory.actionEventFailed=Ereignisaktionsfehler
eventService.eventCategory.add=Objekt hinzufügen
eventService.eventCategory.addMassProblemSlave=Hinzufügen eines Links zu einem untergeordneten Objekt
eventService.eventCategory.attach_file=Datei hinzufügen
eventService.eventCategory.attach_file_to_attr=Datei hinzufügen
eventService.eventCategory.changeCase=Änderung des Objekttyps
eventService.eventCategory.changeLicense=Benutzerlizenz ändern
eventService.eventCategory.changePasswd=Ändern Sie das Benutzerkennwort
eventService.eventCategory.changeResponsible=Verantwortlich ändern
eventService.eventCategory.changeStatus=Archivobjekt
eventService.eventCategory.commentAdd=Kommentar hinzufügen
eventService.eventCategory.commentDel=Kommentar löschen
eventService.eventCategory.commentEdit=Kommentar bearbeiten
eventService.eventCategory.del=Objekt löschen
eventService.eventCategory.delError=Objektlöschfehler
eventService.eventCategory.delete_file=Datei löschen
eventService.eventCategory.edit=Objekt ändern
eventService.eventCategory.edit_file=Datei bearbeiten
eventService.eventCategory.embeddedApplicationEventInitiated=Aufrufen einer Benutzeraktion aus einer eingebetteten Anwendung
eventService.eventCategory.escalationChanged=Ändern des Eskalationsschemas
eventService.eventCategory.escalationLevel=Eskalation
eventService.eventCategory.escalationTime=Eskalationsstufen ändern
eventService.eventCategory.fastEdit=Schnelles Ändern des Objekts
eventService.eventCategory.getLocationFailed=Fehler beim Abrufen der Geolokalisierung
eventService.eventCategory.loginFailure=Anzumelden sich versuchen
eventService.eventCategory.loginSuccessful=Anmeldung
eventService.eventCategory.logout=Ausloggen
eventService.eventCategory.moveIn=Objekt verschieben (nach)
eventService.eventCategory.moveOut=Objekt verschieben (von)
eventService.eventCategory.ndapAlertActivated=Alarm aktiviert
eventService.eventCategory.ndapAlertChanged=Alarm geändert
eventService.eventCategory.ndapAlertDeactivated=Alarm deaktiviert
eventService.eventCategory.notificationAttemptFailed=Fehler beim Senden der ersten Warnung
eventService.eventCategory.notificationInvalidEmails=Benachrichtigungsfehler (ungültige E-Mail)
eventService.eventCategory.notificationQueuedSuccessful=Zum Senden in die Warteschlange gestellt
eventService.eventCategory.notificationSendFailed=Benachrichtigungfehler
eventService.eventCategory.notificationSendFailedEmailNotExists=Benachrichtigungsfehler (keine E-Mail)
eventService.eventCategory.notificationSendFailedPartially=Benachrichtigungsfehler (teilweises Senden)
eventService.eventCategory.notificationSendFailedSystemEmail=Benachrichtigungsfehler (System-E-Mail)
eventService.eventCategory.notificationSendSuccessful=Benachrichtigung senden
eventService.eventCategory.pushMobileSendFailed=Benachrichtigung in mobiler Anwendung senden: Fehler
eventService.eventCategory.pushMobileSendFailedPartially=Benachrichtigung in mobiler Anwendung senden: Fehler (FCM-Servicefehler)
eventService.eventCategory.pushMobileSendSuccessful=Benachrichtigung in mobiler Anwendung senden
eventService.eventCategory.pushSendFailed=Senden einer Benachrichtigung in der Schnittstelle: Fehler
eventService.eventCategory.pushPortalSendFailed=Fehler bei Push-Benachrichtigung an Portal
eventService.eventCategory.pushSendFailedPartially=Benachrichtigung in mobiler Anwendung senden: Fehler (Benutzer nicht angemeldet)
eventService.eventCategory.pushSendSuccessful=Senden von Push-Benachrichtigungen in der Benutzeroberfläche
eventService.eventCategory.pushPortalSendSuccessful=Push-Benachrichtigung an das Portal senden
eventService.eventCategory.removeError=Objektarchivierungsfehler
eventService.eventCategory.removeMassProblemMaster=Entfernen eines Links zu einem Massenobjekt
eventService.eventCategory.removeMassProblemSlave=Entfernen Link zu einem untergeordneten Objekt
eventService.eventCategory.removeMassProblemStatus=Das Massenattribut ist nicht festgelegt
eventService.eventCategory.removeTransitionsFromMarker=Übergang von der Markierung der Gruppe ''Status ändern'' gelöscht
eventService.eventCategory.responsibleError=Verantwortlichen Fehler ändern
eventService.eventCategory.setMassProblemMaster=Hinzufügen eines Links zu einem Massenobjekt
eventService.eventCategory.setMassProblemStatus=Das Massenattribut ist festgelegt
eventService.eventCategory.stateActionFailed=Fehler beim Anmelden/Abmelden von Status
eventService.eventCategory.userEvent=Protokollierung über ein Skript
eventService.eventCategory.userEventInitiated=Benutzersteuerung drücken
eventService.eventCategory.wfChangeStatus=Änderung des Objektstatus mit Workflow
eventService.fastEdit=Objekt ''{0}'' geändert:\n\t{1}
eventService.getLocationFailed=Mobiler Client: Gerätestandortdaten konnten nicht abgerufen werden. {0}
eventService.groovySyntaxError=Syntaxfehler im Warnfeld ''{0}'' gefunden
eventService.groovyTemplateSyntaxError=Syntaxfehler in Vorlage im Zusammenhang mit Warnung gefunden
eventService.levelTime=Ebene {0}: {1} {2};
eventService.loginFailure=Benutzer ''{0}'' konnte sich nicht anmelden: ''{1}''
eventService.loginSuccessful=Benutzer ''{0}'' ist angemeldet ''{1}''
eventService.logout=Benutzer ''{0}'' ist abgemeldet
eventService.moveIn=Objekt ''{0}'' wurde von ''{1}'' nach ''{2}'' verschoben.
eventService.moveOut=Objekt ''{0}'' wurde von ''{1}'' nach ''{2}'' verschoben.
eventService.notificationAttemptFailed=Beim Senden der Benachrichtigung ({1} - {2} {3}) von {0} ist ein Fehler aufgetreten, weil {4}. Der nächste Versuch zum Senden einer Benachrichtigung wird nach {5} Sekunden ausgeführt ({6} Versuche verfügbar).{7}
eventService.notificationInvalidEmails=Die Warnung ({0} - {1}) wurde nicht an die folgenden ungültigen Adressen gesendet: {2}.{3}
eventService.notificationQueuedSuccessful=Eine Warnung ({0} - {1} - [{2}]) wurde in die Warteschlange gestellt, um von {3} gesendet zu werden. Wird informiert: {4}.{5}
eventService.notificationSendFailed=Warnung ({1} - {2}) wurde nicht von ''{0}'' gesendet, weil {3}.{4}
eventService.notificationSendFailedEmailNotExists=Die Warnung ({0} - {1}) wurde nicht an die folgenden Mitarbeiter gesendet: {2} weil {3}.{4}
eventService.notificationSendFailedPartially=Die Warnung ({0} - {1}) wurde nicht an die folgenden Adressen gesendet: {2} aufgrund von: {3}{4}
eventService.notificationSendFailedSystemEmail=Failed to send notification ({0} - {1}) to the following e-mail addresses: {2}, because these addresses are specified in connection parameters to incoming mail servers.{3}
eventService.notificationSendSuccessful=Benachrichtigung ({1} - {2}{4})\n gesendet von {0} Informiert: {3}.{5}
eventService.pushGroovySyntaxError=Syntaxfehler im Feld ''{0}'' der Benachrichtigung festgestellt
eventService.pushGroovyTemplateSyntaxError=Syntaxfehler in der Vorlage im Zusammenhang mit der Benachrichtigung gefunden
eventService.pushMobileSendFailed=Die Benachrichtigung in der mobilen Anwendung ({0} - {1}{3}) wurde wegen {2} nicht gesendet.{4}
eventService.pushMobileSendFailedPartially=Die Benachrichtigung in der mobilen Anwendung ({0} - {1}) wurde nicht an die folgenden Benutzer gesendet: {2}, weil {3}.{4}
eventService.pushMobileSendSuccessful=In der mobilen Anwendung wurde eine Benachrichtigung gesendet ({0} - {1}{3})\nInformiert: {2}.{4}
eventService.pushSendFailed=Schnittstellenbenachrichtigung nicht gesendet ({0} - {1}{3}), weil {2}.{4}
eventService.pushSendFailedPartially=Die Schnittstellenbenachrichtigung ({0} - {1}) wurde nicht an die folgenden Mitarbeiter gesendet: {2}, da Benutzer derzeit nicht angemeldet sind.{3}
eventService.pushSendSuccessful=In der Schnittstelle wurde eine Benachrichtigung gesendet ({0} - {1}{3}).\n Informiert: {2}.{4}
eventService.removeError=Das Objekt ''{0}'' wurde aus folgenden Gründen nicht zum Archiv hinzugefügt: \n{1}
eventService.removeMassProblemMaster=Link zum Massenobjekt entfernt: {0}
eventService.removeMassProblemSlave=Link zum untergeordneten Objekt entfernt: {0}
eventService.removeMassProblemStatus=Massenzeichen aus Objekt {0} entfernt
eventService.removeTransitionsFromMarker=Der Übergang ''{0}'' - ''{1}'' wurde aus der Markierung ''{2}'' der Rechte der Gruppe ''Statusänderung '' Klasse/Typ {3} entfernt.
eventService.responsibleError=Verantwortliche Änderung fehlgeschlagen: {0}
eventService.setMassProblemMaster=Link zum Massenobjekt hinzugefügt: {0}
eventService.setMassProblemStatus=Massenattribut wurde dem Objekt {0} zugewiesen
eventService.stateActionFailed=Fehleraktion zum Eingeben/Beenden des Status ''{0}'': {1}
#ru.naumen.core.server.events.impl.EventServiceBean
eventService.userEvent=Skript: {0}
eventService.userEventInitiated=Das Benutzerereignis ''{0}'' ({1}) wurde durch Drücken des Steuerelements ''{2}'' gestartet. Ort: {3}.
eventService.wfChangeStatus=Geänderter Objektstatus ''{2}'': ''{0}'' -> ''{1}''
eventStorageRule=Speicherregeln von Ereignisprotokollen
eventStorageRule.eventGroup.popular=Populäre Ereignisse
eventStorageRule.eventGroup.users=Benutzer
eventStorageRule.eventGroup.escalation=Eskalationen
eventStorageRule.eventGroup.objects=Aktionen mit einem Objekt
eventStorageRule.eventGroup.comments=Kommentare
eventStorageRule.eventGroup.files=Dateien
eventStorageRule.eventGroup.notification=Benachrichtigungen
eventStorageRule.eventGroup.pushNotification=Push-Nachrichten
eventStorageRule.eventGroup.pushNotificationError=Fehler bei Push-Nachrichten
eventStorageRule.eventGroup.eventAction=Ereignisaktionen
eventStorageRule.eventGroup.mass=Massivität
eventStorageRule.eventGroup.workflow=Workflow
eventStorageRule.eventGroup.alert=Alarmierung
eventStorageRule.eventGroup.plannedVersion=Geplante Versionierung
eventStorageRule.unableToAddNonUniqueCode=Eine Ereignisprotokoll-Speicherregel mit dem Code "{0}" kann nicht hinzugefügt werden. Der Code der Ereignisprotokoll-Speicherregel muss eindeutig sein.
executeStateActionsOperation.postActionError1=Beim Ausführen der Aktion "{0}" beim Verlassen des Status "{1}" ist ein Fehler aufgetreten. \nFehlertext: {2}
executeStateActionsOperation.preActionError1=Bei der Ausführung der Aktion "{0}" beim Eintritt in den Status "{1}" ist ein Fehler aufgetreten. \nFehlertext: {2}
export=Export
failedToDeleteObjects[few]=Das Löschen von {0} Objekten ist fehlgeschlagen:
failedToDeleteObjects[many]={0} Objekte konnten nicht gelöscht werden:
failedToDeleteObjects[one]=Das Objekt {0} konnte nicht gelöscht werden:
file=Datei
file.preview.unavailable=Datei kann nicht angezeigt werden, da sie entfernt wurde
fileAuthor=Dateiautor
fileContent=Dateiinhalt
fileDescription=Dateibeschreibung
fileDoesNotExistInStorage=Datei oder Katalog "{0}" nicht gefunden
fileStorage.fileAttachmentError=Beim Hinzufügen der Datei "{0}" ist ein Fehler aufgetreten.
fileStorage.fileNotFound=Datei oder Katalog nicht gefunden: {0}.
fileStorage.maliciousFilesAttach=Beim Anhängen der Datei ist ein Fehler aufgetreten: ''{0}''. Diese Datei wurde als bösartig eingestuft.
fileStorage.maliciousFilesUpload=Beim Hochladen der folgenden Dateien ist ein Fehler aufgetreten: ''{0}''. Diese Dateien wurden als bösartig eingestuft.
fileStorageDoesNotExists=Dateispeicher "{0}" nicht gefunden
fileTitle=Dateiname
filterAgreements=Filtern von Vereinbarungen beim Bearbeiten
filterCases=Filter von Typen während Bearbeitung
filterServices=Filter von Diensten während Bearbeitung
folder=Ordner
folders=Ordner
formProperties=Formulareigenschaften
ftsChzechAnalyzer=Tschechisch
ftsDefaultAnalyzer=Standardanalysator
ftsDeutschAnalyzer=Deutsch
ftsEnglishAnalyzer=Englisch
ftsFrenchAnalyzer=Französisch
ftsNoAnalyzer=exakt
ftsNoMorphNoStrictAnalyzer=Unexakt, ohne Morphologie
ftsPolishAnalyzer=Polnisch
ftsRussianAnalyzer=Russisch
ftsUkrainianAnalyzer=Ukrainisch
goToWorkInSystem=Fahren Sie mit der Arbeit im System fort
hasState.editState=Andere Übergänge
importMetainfo.done=Upload der Metadaten in '{}' Sekunden abgeschlossen\nWarnings: '{}'
importMetainfo.entityExistsError=Systementität mit dem Code ''{0}'' existiert bereits.
importMetainfo.error=Metainformationen konnten nicht geladen werden. {0}
importMetainfo.initFrom=Initialisierung von '{}'
importMetainfo.initializingCatalog=Initialisierung des Verzeichnises'''{}'''
importMetainfo.loading=Initialisierung der Metaklasse '{}'
importMetainfo.loadingAdminLiteSettingsDone=Leichte Schnittstellenparameter geladen: '{}'
importMetainfo.loadingAdminLiteSettingsStart=Laden von Schnittstellenparametern für Lightweight-Einstellungen
importMetainfo.loadingAdvListSettingsDone=AdvList-Einstellungen wurden geladen
importMetainfo.loadingAdvListSettingsStart=Beginn des Ladens der AdvList-Einstellungen
importMetainfo.loadingAttrDefaultValue=['{}'/'{}'] Standardwert für Attribut laden: '''{}'''
importMetainfo.loadingAttrDefaultValuesDone=Die Einstellungen der Standardattributwerte wurden geladen: '{}'
importMetainfo.loadingAttrDefaultValuesStart=Beginn des Ladens von Attributen Standardwerte
importMetainfo.loadingCatalogs=['{}'/'{}'] Laden des Katalogs '''{}'''
importMetainfo.loadingCatalogsDone=Verzeichnis-Einstellungen geladen: '{}'
importMetainfo.loadingCatalogsStart=Beginn des Ladens von Verzeichnissen
importMetainfo.loadingCustomForms=['{}'/'{}'] Laden des benutzerdefinierten Formulars: '{}'
importMetainfo.loadingCustomFormsDone=Benutzerdefinierte Formulareinstellungen wurden geladen: '{}'
importMetainfo.loadingCustomFormsStart=Beginn des Ladens von benutzerdefinierten Formularen
importMetainfo.loadingCustomJsElements=['{}'/'{}'] Laden des JS-Elements: '''{}'''
importMetainfo.loadingCustomJsElementsDone=Benutzerdefinierte Einstellungen für JS-Elemente wurden geladen: '{}'
importMetainfo.loadingCustomJsElementsStart=Beginn des Ladens benutzerdefinierter JS-Elemente
importMetainfo.loadingDropDownSettingsDone=Dropdown-Einstellungen wurden geladen: '{}'
importMetainfo.loadingDropDownSettingsStart=Beginn des Ladens Dropdown-Einstellungen
importMetainfo.loadingEmbeddedAppContentDone=Der Inhalt integrierter Anwendungen wurde geladen: '{}'
importMetainfo.loadingEmbeddedAppContentStart=Beginn des Ladens von Inhalten integrierter Anwendungen
importMetainfo.loadingEmbeddedAppDone=Einstellungen der integrierten Anwendungen geladen: '{}'
importMetainfo.loadingEmbeddedAppStart=Beginn des Ladens integrierter Anwendungen
importMetainfo.loadingEscalationDone=Eskalationseinstellungen wurden geladen: '{}'
importMetainfo.loadingEscalationStart=Beginn des Ladens der Eskalationseinstellungen
importMetainfo.loadingEventActionsDone=Einstellungen für Ereignisaktionen wurden geladen: '{}'
importMetainfo.loadingEventActionsStart=Beginn des Ladens von Ereignisaktionen
importMetainfo.loadingFilePreviewSettingsDone=Die Einstellungen für die Dateivorschau wurden geladen
importMetainfo.loadingFilePreviewSettingsStart=Beginn des Ladens der Dateivorschaueinstellungen
importMetainfo.loadingInputmaskExtensionsDone=Die Einstellungen für die Eingabemaskenerweiterungen wurden geladen: '{}'
importMetainfo.loadingInputmaskExtensionsStart=Beginn des Ladens von Eingabemaskenerweiterungen
importMetainfo.loadingListTemplate=['{}'/'{}'] Laden der Einstellungen für Listenvorlagen: '''{}'''
importMetainfo.loadingListTemplatesDone=Einstellungen für Listenvorlagen wurden geladen: '{}'
importMetainfo.loadingListTemplatesStart=Beginn des Ladens von Listenvorlagen
importMetainfo.loadingMailProcessRulesDone=Einstellungen für Mailprozessregeln wurden geladen: '{}'
importMetainfo.loadingMailProcessRulesStart=Beginn des Ladens der Mailprozessregeln
importMetainfo.loadingMobileSettingsDone=Mobile Einstellungen wurden geladen: '{}'
importMetainfo.loadingMobileSettingsStart=Beginn des Ladens der mobilen Einstellungen
importMetainfo.loadingNavSettingsDone=Navigationseinstellungen wurden geladen
importMetainfo.loadingNavSettingsStart=Beginn des Ladens der Navigationseinstellungen
importMetainfo.loadingObjectGroupsDone=Objektgruppeneinstellungen wurden geladen: '{}'
importMetainfo.loadingObjectGroupsStart=Beginn des Ladens von Objektgruppen
importMetainfo.loadingPermittedTypesDone=Zulässige Typeneinstellungen wurden geladen: '{}'
importMetainfo.loadingPermittedTypesStart=Beginn des Ladens zulässiger Typen
importMetainfo.loadingReportTemplatesDone=Die Einstellungen für Berichtsvorlagen wurden geladen: '{}'
importMetainfo.loadingReportTemplatesStart=Beginn des Ladens von Berichtsvorlagen
importMetainfo.loadingResponsibleTransfers=['{}'/'{}'] Laden von Informationen über die Möglichkeit der Übertragung von Verantwortung zwischen Teams: '{}'
importMetainfo.loadingResponsibleTransfersStart=Beginn des Hochladens von Informationen zur Übertragung der Verantwortung zwischen Teams
importMetainfo.loadingScript=['{}'/'{}'] Ladeskript: '{}'
importMetainfo.loadingScriptModulesDone=Regeleinstellungen für Skriptmodule wurden geladen: '{}'
importMetainfo.loadingScriptModulesStart=Beginn des Ladens der Regeln für Skriptmodule
importMetainfo.loadingScriptsDone=Die Skripteinstellungen wurden geladen: '{}'
importMetainfo.loadingScriptsStart=Beginn des Ladens von Skripten
importMetainfo.loadingSecDomainsDone=Die Einstellungen für Sicherheitsdomänen wurden geladen: '{}'
importMetainfo.loadingSecDomainsStart=Beginn des Ladens von Sicherheitsdomänen
importMetainfo.loadingSecGroupsDone=Sicherheitsgruppeneinstellungen wurden geladen: '{}'
importMetainfo.loadingSecGroupsStart=Beginn des Ladens von Sicherheitsgruppen
importMetainfo.loadingSecPolicySettingsDone=Sicherheitsrichtlinieneinstellungen wurden geladen
importMetainfo.loadingSecPolicySettingsStart=Beginn des Ladens der Sicherheitsrichtlinieneinstellungen
importMetainfo.loadingSecProfilesDone=Die Einstellungen für Sicherheitsprofile wurden geladen: '{}'
importMetainfo.loadingSecProfilesStart=Beginn des Ladens von Sicherheitsprofilen
importMetainfo.loadingSettingsDone=Abfrageparameter und andere Einstellungen wurden geladen
importMetainfo.loadingSettingsStart=Beginn des Ladens von Abfrageparametern und anderen Einstellungen
importMetainfo.loadingStyleTemplate=['{}'/'{}'] Laden von Einstellungen für Stilvorlagen: '{}'
importMetainfo.loadingStyleTemplatesDone=Einstellungen für Stilvorlagen wurden geladen: '{}'
importMetainfo.loadingStyleTemplatesStart=Beginn des Ladens von Stilvorlagen
importMetainfo.loadingTag=['{}'/'{}'] Laden der Tag-Einstellungen: '{}'
importMetainfo.loadingTagsDone=Tags-Einstellungen wurden geladen: '{}'
importMetainfo.loadingTagsStart=Beginn des Ladens von Tags
importMetainfo.loadingThemesDone=Themeneinstellungen wurden geladen: '{}'
importMetainfo.loadingThemesStart=Beginn des Ladens von Themen
importMetainfo.loadingTimerDefinition=['{}'/'{}'] Loading timer definition '''{}':'{}'''
importMetainfo.loadingTimerDefinitionsDone=Timer-Definitionseinstellungen wurden geladen: '{}'
importMetainfo.loadingTimerDefinitionsStart=Beginn des Ladens der Timer-Definitionen
importMetainfo.loadingUiDone=UI-Einstellungen wurden geladen: '{}'
importMetainfo.loadingUiStart=Start des Ladens der Benutzeroberfläche
importMetainfo.loadingUserEvent=['{}'/'{}'] Laden von Benutzerereigniseinstellungen: '''{}'''
importMetainfo.loadingUserEventsDone=Benutzerereigniseinstellungen wurden geladen: '{}'
importMetainfo.loadingUserEventsStart=Beginn des Ladens von Benutzerereignissen
importMetainfo.loadingWpProfileFoldersDone=Einstellungen für Workflows-Profilordner wurden geladen: '{}'
importMetainfo.loadingWpProfileFoldersStart=Beginn des Ladens von Profilen verwandter Lebenszyklen
importMetainfo.start=Beginn des Imports von Metainformationen\nTarget application version: '{}'\nEntladungsmethode: '{}'\nAuthor: '{}'\nExport date: '{}'
importMetainfo.tableExistsError=Systementität mit Tabelle ''{0}'' existiert bereits
importMetainfo.type.full=Voll
importMetainfo.type.partial=Teilweise
inputmaskExtensionsLoaded=Das Eingabefeld mit der Maskenerweiterungsdatei wird geladen
interface=Schnittstelle
interfaceAndNavigation=Schnittstelle und Navigation
internalServerError=Interner Anwendungsfehler. Bitte wenden Sie sich an den Systemadministrator
isAsynchronousCountObjectsInTab=Asynchrone Objektzählung auf Tabs
isCompleteSetOfLicensesNotRequired=Anmeldung mit einem unvollständigen Satz von Lizenzen
isUiCssTransitionsEnabled=Animation von Oberflächenelementen
language=Sprache
languageHistory=Sprachänderungsverlaufsobjekt
learningProcess=Lernprozess
lilac=Lila
listTemplate.applyFailMessage=Beim Kopieren von Einstellungsgruppen in die folgenden Listen ist ein Fehler aufgetreten: <br>{0}.<br>Verknüpfungen zwischen der Vorlage und diesen Einstellungsgruppen wurden gelöscht.
listTemplate.applyMessage=Einstellungen aus der Vorlage "{0}" werden angewendet.<br/>Kopiert: {1}.
listTemplate.defaultPrs=Standarddarstellung
listTemplate.filtration=Filterbeschränkungen
listTemplate.hasReferenced=Auf {1} wird durch die Listenvorlagen verwiesen: {0}.
listTemplate.hasReferencedOnAttributeGroup=Die Attributgruppe {1} wird von den Listenvorlagen referenziert: {0}.
listTemplate.massToolPanel=Bulk Aktionsleiste
listTemplate.objectFilter=Einschränkungen des Listeninhalts
listTemplate.objectsActions=Aktionen in der Liste
listTemplate.othersTemplates=Andere Vorlagen
listTemplate.parametersList=Listenoptionen
listTemplate.settingsDefaultPrs=Standardansicht Einstellungen
listTemplate.settingsFiltration=Einstellungen für Filterbeschränkungen
listTemplate.settingsMassToolPanel=Einstellungen für das Bulk Operations Panel
listTemplate.settingsObjectFilter=Einstellungen für Inhaltsbeschränkungen auflisten
listTemplate.settingsObjectsActions=Objekte Aktionen Einstellungen
listTemplate.settingsParametersList=Einstellungen für Listenoptionen
listTemplate.settingsToolPanel=Aktionsleisteneinstellungen
listTemplate.suitableTemplates=Geeignete Vorlagen
listTemplate.toolPanel=Aktionsleiste
listTemplate.unableToAddNonUniqueCode=Die Listenvorlage mit dem Code "{0}" kann nicht hinzugefügt werden. Der Code muss eindeutig sein.
listTemplates=Vorlage
location=Standort
logo.fileIsNotImage=Der Inhalt der Datei "{0}" ist kein Bild.
logos=Systemlogos
mail=E-Mail
mail.server.message=Der Mailserver antwortete:
mailConnectionParameters=Mail-Verbindungsparameter
mailLog=Protokoll für eingehende E-Mails
main=Haupt
massSCOperations=Massenoperationen
memory.gb.title=Gb
memory.kb.title=Kb
memory.mb.title=Mb
metainfo.ChildMetaClass=Untergeordneter Typ ''{0}''
metainfo.MetainfoServiceBean.clazz=Klasse
metainfo.MetainfoServiceBean.type=typ
metainfo.catalog=Verzeichnis ''{0}''
metainfo.catalog.deleteMesage=Verzeichnis ''{0}'' kann nicht gelöscht werden.
metainfo.clazz=Klass ''{0}''
metainfo.clazzLower=Klass ''{0}''
metainfo.security.employeeForIntegration.error=Der Mitarbeiter {0} kann aus folgenden Gründen nicht geändert werden: \n\t 1. Das Attribut "Mitarbeiter für die Integration" kann nicht geändert werden, da der Mitarbeiter mit dem Administrator verbunden ist.
metainfo.security.login.employee.error=Anmeldung als Mitarbeiter nicht möglich. Das Konto des Mitarbeiters dient dem Service.
metainfo.type=Typ ''{0}''
metainfo.typeLower=typ ''{0}''
metainfoValidation.scriptsTitleCantBeEmpty=Skriptname ''{0}'' muss ausgefüllt werden.
metainfoValidation.scriptsTitleExceedsMaxLength=Skriptname ''{0}'' ist länger als max. Größe {1}.
metainfoValidation.scriptModuleCodeExists=Modul ''{0}'' konnte nicht hinzugefügt werden. Ein Modul mit diesem Code existiert bereits.
mobile.error.attributeFqnIncorrectFormat=Das Attribut "{0}" hat nicht das richtige Format.
mobile.rest.action.not_found_error=Die Aktion ist nicht aufgeführt. Wahrscheinlich wurde es gelöscht.
mobile.rest.action.object_deleted_error=Diese Aktion kann nicht ausgeführt werden, da das Objekt gelöscht wurde.
mobile.rest.delete_files_permission_error=Sie haben keine Berechtigung zum Löschen dieser Datei
mobile.rest.events.locationPermissionRequired=Eine Standortgenehmigung ist erforderlich.
mobile.rest.message=Nachricht
mobile.rest.mobile_object_not_exist_error=Das Objekt existiert nicht. Es wurde möglicherweise gelöscht.
mobile.rest.object_is_not_file=Objekt ist keine Datei: uuid = {0}
dateTimeRestrictions.after=Der eingegebene Wert kann nicht in der Vergangenheit liegen.
dateTimeRestrictions.afterAttribute=Feld "{0}" darf nicht kleiner sein als Feld "{1}".
dateTimeRestrictions.before=Der eingegebene Wert kann nicht in der Zukunft liegen.
dateTimeRestrictions.beforeAttribute=Das Feld "{0}" darf nicht größer sein als das Feld "{1}".
mobile.rest.title=Titel
mobileSettings=Mobile Anwendung
mobileSettingsObjectCards=Objektkarten
mobileSettingsObjectLists=Objektlisten
monitoring=Überwachungssystem
needCompressImage=Bildkomprimierung in Attributen "RTF-Text"
no=nein
notSpecified=[unbestimmt]
notification.body=Text
notification.object=Objekt: ''{0}''
notification.script=Skript
notification.subj=Themat
number=Nummer
object=Objekt
objectAddForm=Objekt hinzufügen Formular für Metaklasse
objectEditForm=Objektbearbeitungsformular
objectsExistsListener.childClassCanNotBeDeleted=Untergeordnetes {0} ''{1}'': wird zum Einrichten von Ereignisaktionen verwendet: {2}
objectsExistsListener.childClassCanNotBeDeletedBecauseOfRules=Untergeordnetes {0} ''{1}'': wird in der Speicherregel von Ereignisprotokoll verwendet: {2}
objectsExistsListener.classCanNotBeDeleted={0} wird in Einstellungen von Ereignisaktionen verwendet: {1}
objectsExistsListener.classCanNotBeDeletedBecauseOfRules={0} wird in der Speicherregel von Ereignisprotokoll verwendet: {1}
objectsExistsListener.etc=usw.
objectsExistsListener.objectsExists=Vorhandene Objekte dieses Typs: {0}.
objectsExistsListener.objectsOfChildTypeExists=Es gibt Objekte vom untergeordneten Typ ''{0}'': {1}
on=aktiviert
operation.OK=Operation erfolgreich abgeschlossen
crudOperationForbiddenMessage=Das Bearbeiten/Erstellen/Löschen von Objekten der Admin-Klasse mithilfe von Skripten ist verboten
otherAdminOptions=Andere Einstellungen
#ou.domain.xml
ou.domain.ouHead=Abteilungsleiter
ou.domain.ouMember=Mitarbeiter der Abteilung
ou.domain.upperOUHead=Leiter der oberen Abteilung
ou.domain.upperOUMember=Mitarbeiter der oberen Abteilung
#ou.window.xml
ou.window.AddComment=Kommentar hinzufügen
ou.window.AddFile=Datei hinzufügen
ou.window.Comments=Kommentare
ou.window.Department=Abteilung
ou.window.Files=Dateien
ou.window.History=Historie
ownerEverybody=All
parameters=Parameter
password=Kennwort
pc.module.unlicensed=Nicht lizenziert
permissionSettings=Berechtigungseinstellungen
plannedVersion.mainBranch=Hauptzweig
plannedVersion.rest.LinkToVersion.restricted=Das Arbeiten mit Objekten der Klasse "Objektversionsinformationen" (sys_objVerInfo) ist nicht verfügbar.
polishLang=Polnische Sprache
preauthFailed=Externe Vorautorisierung fehlgeschlagen
#PrefixObjectLoaderServiceImpl
prefixObjectLoaderService.objectNotFound=Objekt nicht gefunden: uuid = {0}
print=Drucken
processSettings=Geschäftsprozesse einrichten
processingRules=Verarbeitungsregeln
properties=Eigenschaften
#ru.naumen.core.server.script.spi.ScriptDtOHelper
proxyService.authProblem=Proxy-Authentifizierung fehlgeschlagen
proxyService.connectProblem=Die Verbindung zum Proxyserver ist fehlgeschlagen oder der Fehler ist auf der FCM-Serviceseite aufgetreten
proxyService.incorrectHostPort=Die Einstellungen für die Verbindung zum Proxyserver sind falsch
push.body=Text
push.object=Objekt: ''{0}''
push.script=Skript
pushApi.employeeBlockedOrArchived=Die Benachrichtigung kann nicht an den Mitarbeiter gesendet werden. Mögliche Gründe: Mitarbeiter ist gesperrt oder archiviert
pushApi.employeeNotFound=Der Mitarbeiter wird nicht gefunden
pushApi.nullRecipient=Der Empfänger darf nicht null sein
pushApi.templateNotFound=Stilvorlage nicht gefunden
pushHeaderFormatType.subject=Thema
pushHeaderFormatType.systemName=Systemname
rebuildReport=Wiederaufbauen
refresh=Aktualisieren
removed.false=aktiv
removed.true=Archiv
removedShort=(arch.) {0}
reportTemplates=Berichtsvorlagen und Druckformulare
#reports
reports.generationReportError=Systemfehler. Berichterstellung fehlgeschlagen. Bitte wenden Sie sich an Ihren Support.
reports.maxMemoryExceededError=Überschreitet die maximal für den Bericht verfügbare Speichergröße. Berichterstellung fehlgeschlagen. Bitte wenden Sie sich an Ihren Support.
reports.maxShowTableSizeExceededError=Der Bericht wird erstellt und steht zum Download zur Verfügung. Um den Bericht zu erhalten, verwenden Sie bitte den Export in verfügbare Formate (Schaltfläche ""Exportieren"" in der Kopfzeile des Berichts). Der erstellte Bericht kann nicht in der Systemschnittstelle angezeigt werden, da er die vom Administrator festgelegte Größenbeschränkung überschreitet.
reports.sentEmail.fileDownloadLink=Link zum Herunterladen der Datei mit dem Berichts-/Druckformular "{0}": <br><a href="{1}">{1}</a>.<br>
reports.sentEmail.fileLifetime=<br> Die Datei steht zum Download zur Verfügung {0} (bis {1}).
reports.sentEmail.fileUserAccess=<br> Nur Benutzer, die einen Bericht / ein Druckformular erstellt haben, können Dateien herunterladen.
reports.sentEmail.reportCardLink=<br><br>Link zur Berichts-/Druckformularkarte: <a href="{0}">{1}</a>
reports.sentEmail.send.with.file=Bericht "{0}" generiert: {1}
reports.sentEmail.send.with.file.description=Melden Sie "{0}" im generierten Format {1}: {2}. Die Berichtsdatei befindet sich im Anhang.
reports.sentEmail.sent=E-Mail mit Link zum Herunterladen der Datei mit Bericht / Druck "{0} {1}" wurde an den Adressaten {2} gesendet.
reports.sentEmail.sent.attachment=Mail mit der Datei des Berichts / Drucks "{0} {1}" wurde an die Adresse {2} gesendet.
resolver.notIntegerValue=Der Wert ''{0}'' ist keine Ganzzahl
responsibilityTransfer=Übertragung der Verantwortung
responsibleStrategy.authorResponsibleStrategy2=Objektautor
responsibleStrategy.prevResponsibleStrategy.serviceError=Das Feld "Service" in der Anfrage ist nicht ausgefüllt
responsibleStrategy.prevResponsibleStrategy.serviceResponsibleError=Das Feld "Kurator" im Anforderungsdienst ist nicht ausgefüllt
responsibleStrategy.solvedResponsibleStrategy.notSolvedError=Im Ticket "Gelöst von" nicht ausgefüllt
root.window.Agreements=Vereinbarungen
root.window.AgreementsAndServices=Vereinbarungen und Dienstleistungen
root.window.ArchiveNoun=Archiv
#root.window.xml
root.window.Attributes=Firmenattribute
root.window.Departments=Abteilungen
root.window.ExportList=Liste exportieren
root.window.ObjectsList=Objektliste
root.window.Orgstructure=Firmenstruktur
root.window.RefreshList=Liste aktualisieren
root.window.SaveListView=Ansicht speichern
root.window.Services=Dienstleistungen
root.window.Sort=Sortieren
ru.naumen.soap.server.SoapServiceHelper.file_added=Datei hinzugefügt
ru.naumen.soap.server.SoapServiceHelper.object_deleted=Objekt gelöscht
ru.naumen.soap.server.SoapServiceHelper.object_edited=Objekt bearbeitet
ru.naumen.soap.server.SoapServiceHelper.wrong_sign=Falsches Nachrichtenzeichen
scRegistration=Registrierung von Tickets
scheduler=Aufgaben-Scheduler
scparams=Tiketparameter
script=Skript
search=Suche nach Kommentaren und Dateien
searchSettings=Suche
secGroups=Benutzergruppen
secRoles=Rollen
security=Benutzergruppen, Rollen und Profile
security.errorWrongShowNewPermissionsParameters=Fehler! Ungültige Parameter:
securityPolicy=Sicherheitsrichtlinie
securityPolicy.cannotChangePasswordYet=In Übereinstimmung mit den vom Systemadministrator festgelegten Einstellungen der Sicherheitsrichtlinie können Sie das Kennwort nicht vor {0} ändern.
securityPolicy.letters=abcdefghijklmnopqrstuvwxyzабвгдеёжзийклмнопрстуфхцчшщъыьэюя
securityPolicy.password.invalidPassword=Das Kennwort ist mit der Sicherheitsrichtlinie nicht zufrieden:
securityPolicy.password.mustBeNew=Das Kennwort darf nicht mit dem aktuellen Kennwort übereinstimmen.
securityPolicy.password.mustContain=Das Kennwort muss {0} enthalten.
securityPolicy.password.mustNotBeLogin=Das Kennwort darf nicht mit dem Login identisch sein.
securityPolicy.password.shouldBeLongerThan=Die Anzahl der Zeichen im Kennwort muss mindestens {0} betragen.
securityPolicy.props.mandatorySymbols=Erforderlich, um die folgenden Zeichentypen im Kennwort zu verwenden
securityPolicy.props.maxPasswordLifespan=Maximales Kennwortalter (Tage)
securityPolicy.props.minPasswordLength=Mindestanzahl von Zeichen im Kennwort
securityPolicy.props.minPasswordLifespan=Mindestalter für das Kennwort (Tage)
securityPolicy.props.restrictCurrentPasswordAsNew=Verbieten Sie die Verwendung des aktuellen Kennworts als neues Passwort
securityPolicy.props.restrictLoginAsPassword=Verbieten Sie die Verwendung des Logins als neues Kennwort
securityPolicy.symbols.digits=Ziffern
securityPolicy.symbols.lowercaseLetters=Englische Großbuchstaben
securityPolicy.symbols.specialSymbols=Spezielle Charaktere
securityPolicy.symbols.uppercaseLetters=Englische Kleinbuchstaben
securityPolicy.wrongCurrentPassword=UngültigesKennwort. Um dasKennwort zu ändern, müssen Sie das gültige aktuelle Kennwort eingeben.
service.domain.AddIssueForClientDepartament=Ticket für Kundenabteilung hinzufügen
service.domain.AddIssueForClientTeam=Ticket für Client-Team hinzufügen
service.domain.AddIsueForClientEmployee=Ticket für Kunden-Mitarbeiter hinzufügen
service.domain.AddObectNoun=Objekt hinzufügen
service.domain.ChangeIssueBinding=Ticketsbindung ändern
service.domain.ChangeMassMode=Massenattribut ändern
service.domain.IssueAuthor=Ticket Autor
service.domain.IssueContact=Gegenpartei der Anfrage
service.domain.IssueContactDepartmentEmployee=Mitarbeiter der Gegenparteiabteilung der Anfrage
service.domain.IssueResolvedEmpoyee=Der Mitarbeiter, der das Ticket gelöst hat
service.domain.IssueResolvedTeamMember=Mitglied des Teams, das das Ticket gelöst hat
service.domain.OtherAttributes=Andere Attribute
#serviceCall.newentryform.xml
serviceCall.newentryform.AddForm=Formular hinzufügen
serviceCall.newentryform.IssueTypeSelect=Auswahl des Tickettyps
serviceCall.newentryform.ObjectTypeSelect=Auswahl des Objekttyps
serviceCall.newentryform.SystemAttributes=Systemattribute
#serviceCall.wf.xml
serviceCall.wf.ClientRequestRecorded=Client-Ticket im System registriert
serviceCall.wf.Closed=Geschlossen
serviceCall.wf.ClosedBy=Geschlossen von
serviceCall.wf.ClosedByEmployee=Geschlossen von (Mitarbeiter)
serviceCall.wf.ClosedByTeam=Geschlossen von (Team)
serviceCall.wf.CodeOfClosure=Code der Schließung
serviceCall.wf.DateOfClosure=Datum der Schließung
serviceCall.wf.IssueProcessingResumed=Ticketsverarbeitung erneut geöffnet
serviceCall.wf.IssueWasResolved=Ticket gelöst
serviceCall.wf.ReceivedConfirmationOfIssueResolutionFromClient=Bestätigung der Ticketsausführung vom Client erhalten
serviceCall.wf.Registered=Eingetragen
serviceCall.wf.Reopened=Wiedereröffnet
serviceCall.wf.Resolved=Gelöst
serviceCall.wf.SolvedBy=Gelöst von
serviceCall.window.ChangeClientAndService=Bindung ändern
serviceCall.window.ChangeState=Status ändern
serviceCall.window.ChangeType=Typ ändern
serviceCall.window.ChnageAssignee=Verantwortlich ändern
serviceCall.window.General=Allgemeine Information
serviceCall.window.HistoryResponsibleAndState=Historie der verantwortlichen und Yustand Veränderungen
serviceCall.window.MassMode=Massenoperationen
#serviceCall.window.xml
serviceCall.window.ObjectParameters=Objektparameter
serviceCall.window.TimeMetrics=Zeitmetriken
serviceCallValidationOperation.scAssociationError=Die Anforderung kann aus folgenden Gründen nicht geändert werden: 1. Erforderliche Attribute nicht ausgefüllt: Geplante Zeit zum Schließen der Anforderung, Standardverarbeitungszeit / Priorität.
serviceCallValidationOperation.scAssociationPriorityError=Die Anforderung kann aus folgenden Gründen nicht geändert werden: 1. Erforderliche Attribute nicht ausgefüllt: Priorität.
serviceCallValidationOperation.scAssociationResolutionTimeError=Die Anforderung kann aus folgenden Gründen nicht geändert werden: 1. Erforderliche Attribute nicht ausgefüllt: Standardverarbeitungszeit.
serviceExistsListener.etc=usw.
serviceExistsListener.hasRelatedAttrs=Auf die Klasse wird durch die Attribute {0} verwiesen.
serviceExistsListener.serviceExists=Der Typ bezieht sich auf die folgenden Objekte: {1}.
sessionTerminated=Konto gelöscht. Bitte geben Sie den Login und das Kennwort eines vorhandenen Kontos ein, um sich anzumelden.
sessionTimedOut=Die Sitzung läuft ab oder dieses Konto wird auf einem anderen Computer verwendet. Bitte geben Sie den Login und das Kennwort ein, um sich anzumelden.
shareViewAvailableFor.any=Alle lizenzierten Benutzer
shareViewAvailableFor.superusers=Nur Superuser
shareViews=Freigegebene Ansichten können erstellt werden
showLeftMenu=Linkes Menü anzeigen
showTopMenu=Oberes Menü anzeigen
signIn=Anmelden
simpleSearchSettings.maxLength=Die Anforderungslänge überschreitet die maximale Größe von {0} Zeichen
#slmService.domain.xml
slmService.domain.ServiceCurator=Dienstkurator
slmService.domain.ServiceCustomer=Dienst-Verbraucher
slmService.domain.ServiceProvider=Dienstleister
smpInterfaceIsForbidden=Systemschnittstelle kann nicht geöffnet werden. Sie haben keine Lizenz mit Zugriff auf die Systemschnittstelle.
#smpSync
smpSync.eaUploadError=Beim Laden der integrierten Anwendung "{0}" ist ein Fehler aufgetreten: {1}
smpSync.eaUploadOk=App erfolgreich geladen
smpSync.licenseUploadOk=Lizenzdatei erfolgreich hochgeladen
smpSync.superUserWarning=Nur Superuser können diese Methoden verwenden
smpSync.wrongContentType=Anfrage hat falschen Inhaltstyp. Es soll sein "multipart/form-data"
# SMS
spnegoAuthFailure=Kerberos/SPNEGO-Autorisierung fehlgeschlagen
spnegoUnsupported=Kerberos/SPNEGO-Authentifizierung wird nicht unterstützt
structuredObjectsViewItems.unableToAddNonUniqueCode=Das Strukturelement mit dem Code ''{0}'' konnte nicht hinzugefügt werden. Der Code muss innerhalb der Struktur eindeutig sein.
structuredObjectsViews=Strukturen
structuredObjectsViews.hasReferenced=Wird in den Einstellungen der Struktur ''{0}'', in den Elementen ''{1}'' verwendet.
structuredObjectsViews.unableToAddNonUniqueCode=Die Struktur mit dem Code ''{0}'' konnte nicht hinzugefügt werden. Der Code muss über alle Strukturen hinweg eindeutig sein.
structuredObjectsViews.useInAttribute=Die Struktur wird in folgenden Attributen verwendet: {0}.
structuredObjectsViews.useInAttributeInCustomForm=Die Struktur wird in folgenden Parametern verwendet: {0}.
structuredObjectsViews.useInContent=Die Struktur wird im Inhalt verwendet: {0}.
structuredObjectsViews.useInViewForEditingAttribute=Struktur wird in der Ansicht zum Bearbeiten von Attributen verwendet {0}.
structuredObjectsViews.useInViewForEditingAttributeInCustomForm=Struktur, die in der Ansicht zur Bearbeitung der Parameter einer benutzerdefinierten Ereignisaktion verwendet wird {0}.
systemAuthFailure=Beim Versuch, sich zu authentifizieren, ist ein Fehler aufgetreten. Wenden Sie sich an Ihren Administrator.
systemCatalogs=System-Verzeichnisse
systemIcons=System-Symbole
systemSettings=Systemeinstellungen
tabBar=Tab-Leiste
tabName=Tab-Name
tabOfBrowser=Tab des Browsers
tabProperties=Tabeigenschaften
tabs=Tabs
#team.domain.xml
team.domain.CuratorDepartmentEmployee=Mitarbeiter der Kuratierungsabteilung
team.domain.HeadOfCuratorDepartment=Leiter der Kuratierungsabteilung
team.domain.TeamLeader=Teamleiter
team.domain.TeamMember=Teammitglied
teams=Teams
templates=Vorlagen
text=Text
themeInterfaceDisplay=Das Thema für die Anzeigeoberfläche
themeInterfaceSettings=Das Thema für die Schnittstelleneinstellungen
timer.active=Aktiv
timer.exceed=Abgelaufen
timer.expired=abgelaufen
timer.notExpired=nicht abgelaufen
timer.notStarted=Warten
timer.paused=Pause
timer.stoped=Gestoppt
timers=Zeitzähler
title=Titel
titleAndCode=''{0}'' ({1})
toggleOff=Ausschalten
toggleOn=Aktivieren
toolPanel=Aktionsleiste
topMenu=Oberes Menü
transaction.timeout=Die Operation hat die maximale Ausführungszeit überschritten
typeSettings=Typeinstellungen
ukrainianLang=Ukrainische Sprache
unlicensedUser=Nicht lizenzierter Benutzer
uploadLicence=Lizenz herunterladen
uploadMetainfo=Laden von Metainformationen
userBlocked=Sie können sich nicht anmelden. Ihr Konto wurde im System gesperrt. Wenden Sie sich an Ihren Administrator.
userCatalogs=Benutzerverzeichnisse
userEvent.error=Bei der Ausführung der Aktion "{0}" ist ein Fehler aufgetreten: {1}
userEvent.eventIsNotApplicable=Diese Aktion kann nicht für ausgewählte Objekte ausgeführt werden.
userEvent.executionDenied={0} kann für die folgenden Objekte nicht ausgeführt werden:   {1}
userEvent.executionErrors.head=Aktion wurde nicht ausgeführt:
userEvent.nothingSelected=Es muss mindestens ein Objekt ausgewählt sein.
userIcons=Benutzerdefinierte Symbole
userJMSQueues=User queues
userSuspended=Sie können sich nicht anmelden. Zu viele falsche Anmeldeversuche. Bitte versuchen Sie es nach {0} s erneut.
validation.image.fileIsNotImage=Der Inhalt der Datei "{0}" ist kein Bild.  Unterstützte Bilddateiformate:  jpg, jpeg, gif, png, bmp, svg, svgz.
value=Bedeutung
valueMapImport.configurationAlreadyExists=Die Konfiguration mit dem Code ''{0}'' ist bereits vorhanden. Sie sollten die neue Korrespondenztabelle mit einem anderen Code speichern.
valueMapImport.existingConfigurationIsNotValueMap=Die Konfiguration mit dem Code ''{0}'' ist bereits vorhanden. Um die Korrespondenztabelle mit dem Code ''{0}'' zu exportieren oder zu importieren, sollten Sie die neue Konfigurationsdatei mit einem anderen Code speichern.
valueMapImport.invalidArchiveContentMulti=Die Korrespondenztabelle kann nicht geladen werden. Das Archiv muss genau ein Dateipaar enthalten: XML mit Importkonfiguration und CSV mit Daten der Korrespondenztabellenzeilen. Dateinamen müssen mit dem Code der importierten Tabelle übereinstimmen.
valueMapImport.invalidArchiveContentSingle=Die Korrespondenztabelle kann nicht geladen werden. Das Archiv muss genau ein Dateipaar enthalten: XML mit Importkonfiguration und CSV mit Korrespondenztabellenzeilendaten. Dateinamen müssen mit dem Code der importierten Tabelle übereinstimmen.
valueMapImport.invalidFileFormat=Die Korrespondenztabelle kann nicht geladen werden. Das Dateiformat ist ungültig.
valueMapImport.rowError=Beim Import der Zeile {0} ist ein Fehler aufgetreten: {1}.
valueMapImport.uploadedConfigurationIsNotValueMap=Die hochgeladene Konfiguration ist keine Importkonfiguration der Korrespondenztabelle mit dem Code ''{0}''.
valueMapImport.valueMapNotFound=Die Importkonfigurationsdatei für die Nachschlagetabelle mit dem Code ''{0}'' wurde nicht gefunden.
valueMapImport.valueMapRemoved=Die Korrespondenztabelle ''{0}'' ist im Archiv und kann daher nicht importiert werden.
valueMapRowIncorrectAttributeValue=Das Feld ''{0}'' enthält den falschen Wert ''{1}''.
visibility=Sichtweite
wfProfiles=Verwandte Workflow-Profile
window.Add=Hinzufügen
window.AddIssue=Anfrage hinzufügen
window.Archive=Archiv
window.ChangeType=Typ ändern
window.Copy=Kopieren
window.Edit=Bearbeiten
window.Move=Bewegung
window.ObjectCard=Objektkarte
window.Remove=Löschen
window.Restore=Aus dem Archiv wiederherstellen
window.SystemAttributes=Systemattribute
yes=ja
you.wrote=Sie schrieben:
adminArea=Unveränderlicher Bereich der Symbolleiste für den Schnellzugriff
AbstractImportMetaInfoStrategy.validationTemplateListAttributeGroup=Parameter "Шаблон списков" ({0}) bezieht sich auf die im System nicht vorhandene Attributgruppe: uuid = "{1}".
AbstractImportMetaInfoStrategy.validationTemplateListUsagePointInContent=Der Parameter "Listenvorlage" ({0}) in der Liste "Verwendungsorte" bezieht sich auf die Liste, die im System nicht vorhanden ist: uuid = "{1}".
AbstractImportMetaInfoStrategy.validationTemplateListUsagePointInLeftMenu=Parameter "Шаблон списков" ({0}) in der Liste "Verwendungsorte" bezieht sich auf den Menüpunkt, der im System nicht vorhanden ist: uuid = "{1}".
addCommentInlineForm=Inline Hinzufügen eines Kommentars
beforeHierarchy=Objekthierarchie (abwärts) ausgehend vom Objekt aufbauen
afterHierarchy=Hierarchieobjekte werden über ein Attribut mit Listenobjekten verknüpft
connection=Verbindung
AbstractImportMetaInfoStrategy.validationUITemplateMissingDefault=Der Parameter "Standardvorlage" in der Metaklasse "{0} ({1})" verweist auf Vorlage, das im System nicht existiert: uuid = {2}.
workflow=Workflow
contentType=Inhaltstyp
hierarchyClass=Klasse der Hierarchieobjekte
nestedObjects=Verschachtelte Objekte
ActionType.integration=Senden an eine externe Warteschlange
version=Version
objectClass=Klasse der Listenobjekte
objectFiles=Objekt-Dateien
roles=Rollen
ADImageDecoder.incorrectDecoder="Falscher Decoder: ''{0}''. Mögliche Werte: {1}"
#ru.naumen.metainfo.server.utils.AbstractCopyOperation
AbstractCopyOperation.emptyCopyList=Die Liste der Objekte, in die Sie die Werte der angegebenen Attribute kopieren möchten, ist leer.
AbstractCopyOperation.impossibleCopyForFqn=Kopiervorgänge von Attributen für Objekte der Metaklasse "{0}" können nicht durchgeführt werden, weil {1}
metaClassSearchSetting=Einstellung von Klassen für die Schnellsuche
AccessKeyDaoImpl.notPortalRest=Die Portallizenz ist abgelaufen oder wurde nicht installiert
ActionType.notification=Alert
ActionType.push=Benachrichtigung in der Schnittstelle
ActionType.pushMobile=Benachrichtigung in der mobilen App
ActionType.pushPortal=Benachrichtigung an das Portal
ActionType.script=Skript
AbstractCopyOperation.impossibleCopyOperation=Der Kopiervorgang von Attributen kann nicht durchgeführt werden, weil der folgende Fehler aufgetreten ist: {0}
AbstractLeftMenuItemActionHandler.itemNotAvailableForProfiles=Die Berechtigungsmarkierung "Liste auf separater Seite anzeigen" für Profile muss aktiviert sein: {0}.
contentTemplates=Inhaltsvorlagen
leftMenu=Linkes Menü
AdvImportSchedulerTask=Synchronisierung
Advlist.export.downloadMail.subject=Die Liste der Objekte der Klasse ''{0}'' wird erzeugt {1}.
linkObjectUUID=Objekt UUID
#ru.naumen.bcp.server.validation.AggregateValueValidator
AggregateValueValidator.parameterError=Die aggregierten Werte ''{0}'' und ''{1}'' von ''{2}'' sind unkorreliert
AggregateValueValidator.valueError=Aggregierte Werte ''{0}'' und ''{1}'' des Attributs ''{2}'' sind nicht verwandt
listPageTitle=Titel der Inhaltsseite
AttachmentDownloadServlet.ErrorInvalidIndex=Invalid attachment index
selectCase=Auswählen eines Objekttyps
showNestedInNested=In verschachtelten Objekten in der Liste verschachtelte Objekte anzeigen
selectContacts=Auswahl einer Kontaktperson
AttrTemplates.attrCannotBeComposite=Ein Attribut kann nicht komposit sein. Das Attribut "{0}" ({1}) ist in dem kompositen Attributvorlage vorhanden: {2}
AttrTemplates.attrRequirements=Attributcodes als $'{'attr'}', wobei attr der Attributcode ist. Es ist erlaubt, die Codes von nicht berechenbaren und nicht zusammengesetzten Attributen der Metaklasse, in der die Einstellung durchgeführt wird, der folgenden Arten von Werten anzugeben: Reelle Zahl; Zeitintervall; Das Datum; Terminzeit; Verweis auf Business-Objekt; Linie; Ganze Zahl; Verzeichniselement sowie Codes der Systemattribute "{0}" (Metaklasse) und "{1}" (Status).
AttrTemplates.attrUsedInTemplate=Das Attribut wird in der kompositen Attributvorlage {0} verwendet
AttrTemplates.canBeFilledBy=In dem Feld können Sie angeben:
AttrTemplates.cannotBeFilledWith=Die Angabe ist nicht zulässig:
AttrTemplates.defaultStateTitle=Status
AttrTemplates.linkToSameClassUsed=Das Attribut mit dem Code "{0}" kann in der Vorlage nicht verwendet werden. Der Attributcode "Link zu Geschäftsobjekt", der sich auf die aktuelle Metaklasse bezieht, wird angegeben.
AttrTemplates.noSuchAttr=Das in der Vorlage angegebene Attribut mit dem Code "{0}" existiert nicht in der Metaklasse {1}
AttrTemplates.isForbiddenSystem=Der Attributcode "{0}" kann in der Vorlage nicht verwendet werden. Systemattributcode "{1}" ist angegeben
quickAccessPanel=Schnellzugriffspanel
dropDownLists=Dropdown-Auswahllisten
AttrTemplates.currentAttrCode=Code des aktuellen kompositen Attributs.
AttrTemplates.isComposite=Attribut mit Code "{0}" kann nicht in einer Vorlage verwendet werden. Das Attribut ist ein komposites Attribut.
AttrTemplates.isComputable=Das Attribut mit dem Code "{0}" kann in der Vorlage nicht verwendet werden. Das Attribut ist berechenbar.
AttrTemplates.linkedObjectAttrCode=Attributcode des Typs "Attribut des verknüpften Objekts".
AttrTemplates.strConstants=String-Konstanten;
AttrTemplates.sameAttr=Das Attribut mit dem Code "{0}" kann in der Vorlage nicht verwendet werden. Der Code des aktuellen Attributs wird angegeben.
AttrTemplates.sboAttrToCurrentMetaClassCode=Attributcode vom Typ "Verweis auf Business-Objekt", der sich auf die aktuelle Metaklasse bezieht.
AttrTemplates.sboAttrToMetaClassWithCompositeTitleCode=Code des Attributtyps "Verweis auf Business-Objekt", wenn in der Klasse, auf die dieses Attribut verweist, das Attribut "title" zusammengesetzt ist.
AttrTemplates.systemAttrCode=Systemattribut-Code "{0}" ({1});
inputmaskExtensions=Erweiterungen der Eingabefeldeinstellungen mit Maske
AttrTemplates.templateAttrDescription="{0}" (Typ "{1}" Klasse "{2}")
AttributeHelper.oneRelObj=Es ist unmöglich, das einzige verwandte Objekt zu erhalten, weil:\n{0}
AttributeHelper.referencedTypesIsNotEquals=Die Klassen oder Verzeichnisse, auf die Attribute verweisen, stimmen nicht überein
AuthenticationApi.getAccessKeyForEmptyLogin=enthält einen ungültigen Link: der Login des Benutzers ist nicht angegeben
#ru.naumen.core.server.script.api.AuthenticationApi
AuthenticationApi.userNotFound=enthält einen ungültigen Link: Benutzer mit Login ''{0}'' wird im System nicht gefunden
showBreadCrumb="Brotkrümel" zeigen
AttributesForSearchResults=Attribute für Suchergebnisse
AuthenticationApi.userNotFoundByCertificate=Benutzer mit Zertifikat "{0}" nicht im System gefunden
jmsQueues=Warteschlangen
AttrTemplates.thereIsLinkToClass=Ein Attribut kann nicht komposit sein. Ein Attribut vom Typ "Link zu Geschäftsobjekt", das sich auf die aktuelle Metaklasse bezieht, ist in der Vorlage des kompositen Attributs {0} vorhanden
AttrTemplates.usesObjWithCompositeTitle=Attribut mit Code "{0}" kann nicht in einer Vorlage verwendet werden. Das Attribut "{1}" (Titel) ist in der Klasse "{2}:{3}" komposit.
AttrTemplates.wrongAttribute=Attribut mit Code "{0}" kann nicht in der Vorlage verwendet werden. Das Attribut hat einen für die Verwendung in der Vorlage inakzeptablen Wertetyp. Die Anweisungen zum Ausfüllen des Feldes sind in der [Hilfe] beschrieben.
AttrTemplates.wrongFormat=Das Format der Vorlage ist nicht korrekt. Anweisungen zum Ausfüllen des Feldes finden Sie im Abschnitt [Hilfe].
AttributeHelper.attrTypesIsNotEquals=Attributtypen stimmen nicht überein
BarcodeApi.emptyParameters=Ungültige Eingabeparameterwerte
AuthenticationApi.userNotFoundByUUID=enthält einen ungültigen Link: Benutzer mit der uuid ''{0}'' wird im System nicht gefunden
AuthorizationService.readOnlyMode=Aktion nicht verfügbar: Nur-Lese-Betrieb
AuthorizationService.systemFileAccessDenied=Sie sind nicht berechtigt, Systemdateien anzuzeigen
userArea=Benutzerdefinierter Bereich der Symbolleiste für den Schnellzugriff
BarcodeApi.incorrectSize=Ungültige Breiten-/Höhenwerte des resultierenden Bildes
systemName=Name des Systems
BeforeDeleteScriptModuleEvent.errorUsedInMobileLoginForm=In Anwendungen verwendetes Skriptmodul: {0}.
BeforeDeleteScriptModuleEvent.errorUsedInMobileLoginSettings=Das Skriptmodul wird in den Einstellungen der mobilen Anwendung verwendet (Tab "Andere", Block "Sicherheit").
#ru.naumen.core.server.systeminfo.SystemInfo
ConfiguredPropertiesContainer=Anwendungsparameter
ConnectionEvent.failure=Die Scheduler-Aufgabe {0} konnte aus folgendem Grund keine Verbindung zum Posteingangsserver {1} herstellen:
CopyMassProblemAttrsOperation.changeMassProblemInheritedAttrsDisabled=Die Werte des Attributs ''{0}'' können nicht geändert werden, solange die Abfrage mit einer Masse verbunden ist.
CustomForm.paramUsedInDateTimeRestriction=Der Parameter ist verknüpft mit dem Parameter: "{0}".
CustomForms.loopDetected=Das angegebene Wertberechnungsskript bildet zusammen mit den Parametern {0} eine zyklische Beziehung
CustomForms.paramCannotBeAdded=Ein Parameter mit dem Code ''{0}'' kann nicht hinzugefügt werden.
DecodeParametersActionHandler.gridParametersIncorrect=Die Baumabbildungsparameter sind falsch ({0}).
DecodeParametersActionHandler.linkAttributeNotFound=kein Beziehungsattribut gefunden
DelMetaClassActionHandler.cantDelete.type=Der Typ ''{0}'' kann nicht gelöscht werden.
metric=Metrisch
searchSettingsTitle=Allgemeine Sucheinstellungen
BarcodeApi.unsupportedBarcodeType=Nicht unterstützter Barcodetyp
CustomForm.cantDelParameter=Der Parameter "{0}" kann nicht gelöscht werden.
BarcodeApi.unsupportedImgType=Bildformat wird nicht unterstützt
#ru.naumen.bcp.server.registry.BcpResourceRegistration.init()
BcpResourceRegistration.init.info=Eingetragene Geschäftsvorfälle von {0}
ClassMetainfoUsedListener.linkExists=Der Typ wird in der Klasseneinstellung {0} verwendet.
#ru.naumen.core.server.CommonUtils
CommonUtils.secGroupWithGivenTitlesDoesntExist=Es gibt keine Benutzergruppe mit dem Namen "{0}".
#ru.naumen.core.server.bo.bop.CopyMassProblemAttrsOperation
CopyMassProblemAttrsOperation.changeWfInheritedAttrsDisabled=Die Werte des Attributs ''{0}'' können nicht geändert werden, da sie von der Massenabfrage geerbt werden.
CustomForm.changeCase=Formular zur Typänderung
CustomForm.changeResponsible=Formular zum Ändern der Verantwortung
CustomForm.quickAddAndEdit=Formulare schnell hinzufügen und bearbeiten
CustomForms.codeMustBeUnique=Der Parametercode muss innerhalb des Ereignisses eindeutig sein.
CustomForms.invalidParamsInCOFScript=Das Skript zur Berechnung des Parameterwertes bezieht sich auf nicht vorhandene Parameter: {0}
DecodeParametersActionHandler.listParametersIncorrect=Die Einstellungen für die Listenanzeige sind falsch ({0}). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
DecodeParametersActionHandler.objectToLinkNotFound=Beziehung-Objekt nicht gefunden
DecodeParametersActionHandler.placeUnaccessible=Dieser Übergang kann für einen Superuser nicht vorgenommen werden
DelAttributeGroupActionHandler.linkToContentAttributeGroupDelete=Die Gruppe ist an der Festlegung der Menüpunkte beteiligt: {0}
#ru.naumen.metainfo.server.spi.dispatch.sec.DelAttributeMarkerActionHandler.processDelete(String, ClassFqn)
DelAttributeMarkerActionHandler.markerDeclaredAtParent=Die Markierung wird in der übergeordneten Klasse oder dem übergeordneten Typ definiert.
DelAttributeMarkerActionHandler.markerIsSystem=Der Marker ist systemisch.
#ru.naumen.metainfo.server.spi.dispatch.DelMetaClassActionHandler
DelMetaClassActionHandler.afterDelete.clazz=Klasse
DelMetaClassActionHandler.afterDelete.type=Typ
DelMetaClassActionHandler.afterDeleteError={0} kann nicht gelöscht werden. {1}
DelMetaClassActionHandler.cantDelete.clazz=Die Klasse ''{0}'' kann nicht gelöscht werden.
navigation=Navigation
structuredObjectsView=Struktur
#ru.naumen.metainfo.server.impl.ClassMetainfoUsedListener
ClassMetainfoUsedListener.ObjectListExists=Typ {0} kann nicht gelöscht werden. Der Typ wird bei der Festlegung der Schnittstelle von Klassen und Typen verwendet: {1}.
CompOnEdit.loopDependency=Das angegebene Skript zur Berechnung der Bearbeitungswerte bildet zusammen mit den Attributen eine zyklische Abhängigkeit: {0}
CustomForms.invalidParamsInFiltrationScript=Das Skript zum Filtern von Parameterwerten beim Bearbeiten verweist auf nicht vorhandene Parameter: {0}
DecodeParametersActionHandler.contactYourAdministrator=Wenden Sie sich an einen Administrator, um das Problem zu lösen.
EventAction.SaveMultiArriveMessageOnQueue=Es ist nicht möglich, eine Aktion für ein Ereignis zu erstellen. Der Warteschlange ist bereits eine Ereignisaktion zugeordnet, z. B. "Nachricht in Warteschlange empfangen" oder die Warteschlange wurde gelöscht.
EventAction.DeleteMultiEscalationBadFqns=Aktion {0} wird in Eskalationsschemata {1} verwendet.
EditNavigationMenuItemActionHandler.chapterNestedInAnotherChapter=Abschnitt ''{0}'' ist in einem anderen Abschnitt verschachtelt und kann keine Unterabschnitte enthalten
EditNavigationMenuItemActionHandler.chapterContainNestingChapters=Abschnitt ''{0}'' enthält verschachtelte Abschnitte und kann selbst nicht in einem anderen Abschnitt verschachtelt werden
FolderCatalog.Description=Enthält Ordner für Objekte der Klasse {0}.
#ru.naumen.core.server.treefilter.FilteredTreeCache
FilteredTreeCache.notPermittedTypes.warn=Das Skript zum Filtern von Attributwerten „{0}“ ({1}) der Klasse/des Typs „{2}“ ({3}) hat ungültige Werte zurückgegeben: {4}.
DelMetaClassActionHandler.catalog=Verzeichnis
DelMetaClassActionHandler.clazz=Klasse
DelMetaClassActionHandler.errorMessage={0} wird durch zusätzliche Klassen- und Typattribute referenziert: {1}
DelMetaClassActionHandler.relatedToFormParams={0} wird durch Parameter referenziert: {1}
#DeleteAttributeActionHandler
DeleteAttributeActionHandler.linkToContentAttributeDelete=Attribut ist an der Einstellung von Menüpunkten beteiligt: {0}
DeleteMetaClassNavigationSettingsListener.usedCaseInBreadCrumb=Die "Breadcrumb"-Elemente sind auf Typ eingestellt.
EventAction.DeleteSingleEscalationBadFqns=Aktion {0} wird im Eskalationsschema {1} verwendet.
GetNavigationReferencesToTabActionHandler.referenceToTabBar={0} (Inhalt)
DelMetaClassActionHandler.metaClassUsedInTimer={0} wird in Zeitzählern verwendet: {1}.
EventAction.attachedFiles=An das Objekt angehängte Dateien
DeleteMetaClassNavigationSettingsListener.usedClassInBreadCrumb=Die "Breadcrumb"-Elemente werden für die Klasse eingerichtet.
DeleteMetaClassNavigationSettingsListener.usedInLeftMenuElements=Klasse/Typ ''{0}'' wird in den Einstellungen des linken Menüs verwendet: {1}
EventAction.SaveSingleEscalationBadFqns=Aktion {0} wird im Eskalationsschema {1} verwendet. Um die eingegebenen Klassen-/Objekttypänderungen zu speichern, entfernen Sie die aktuelle Aktion aus dem Eskalationsschema und versuchen Sie es erneut
EventAction.changedMetaClassError=Die Ereignisaktion wurde abgebrochen, weil der Objekttyp geändert wurde.
EventAction.ChangeFqnsDenied=Beim Laden einer Ereignisaktion mit dem Code "{0}" ist ein Fehler aufgetreten. Eine Änderung des Satzes von Objekttypen ist nicht möglich.
EventAction.ChangeTypeDenied=Beim Laden einer Ereignisaktion mit dem Code "{0}" ist ein Fehler aufgetreten. Der Ereignistyp kann nicht geändert werden.
EventAction.NonEdit=Die Aktion für den Ereigniscode "{0}" kann nicht geändert werden. Aktualisieren Sie die Liste.
EventAction.NonUniqueCode=Der Ereignisaktionscode "{0}" kann nicht hinzugefügt werden. Der Ereignisaktionscode muss eindeutig sein.
EventAction.SaveMultiEscalationBadFqns=Aktion {0} wird in {1} Eskalationsschemata verwendet. Um die eingegebenen Klassen-/Objekttypänderungen zu speichern, entfernen Sie die aktuelle Aktion aus dem Eskalationsschema und versuchen Sie es erneut
EventAction.error=Bei der Durchführung einer synchronisierten Ereignisaktion ist ein Fehler aufgetreten. Wenden Sie sich an einen Spezialisten für die Systemkonfiguration.
EventActionApi.absentEventAction=Es gibt keine Aktion auf ein Ereignis mit dem Code: {0}
#ru.naumen.core.server.dispatch.ExecScriptActionHandler
ExecScriptActionHandler.UnsupportedCharset=Das Skript enthält eine nicht unterstützte Kodierung ''{0}''
HandlerUtils.validatePermittedTypes.error=Der Standardwert ''{0}'' entspricht nicht den Typbeschränkungen: Der Typ ''{1}'' ist in der Liste ''Objekttypen'' nicht ausgewählt
HandlerUtils.validatePermittedTypesOnCompositeTitle.permittedTypeHasCompositeTitle=Das aktuelle Attribut kann nicht auf den Typ ''{0}'' der Klasse ''{1}'' verweisen, weil \n1.Das Attribut "{2}" ("{3}") ist bei diesem Typ zusammengesetzt und \n2.Das aktuelle Attribut ist im zusammengesetzten Attributmuster ''{4}'' (''{5}'') enthalten
#ru.naumen.metainfo.server.spi.dispatch.wf.DeleteWfProfileActionHandler
DeleteWfProfileActionHandler.canNotDeleteUsedAt=Das verknüpfte Workflow-Profil ''{0}'' kann nicht gelöscht werden. Das Profil wird in den folgenden Objekten verwendet: {1}
DeleteMetaClassNavigationSettingsListener.usedInTopMenuElements=Die Klasse/der Typ ''{0}'' wird in den Einstellungen des oberen Menüs in den Elementen verwendet: {1}
DeleteNavigationMenuItem.itemIsAbsent=Element des oberen Menüs mit dem Code ''{0}'' wird nicht gefunden. Sie wurde verschoben oder gelöscht.
DeleteNavigationMenuItem.parentItemIsAbsent=Element des oberen Menüs mit dem Code ''{0}'', der im Feld ''Eingebettet in Abschnitt'' angegeben ist, wurde nicht gefunden. Dieser Punkt wurde verschoben oder gelöscht.
DeleteReportTemplateActionHandler.canNotDelete=Die Vorlage "{0}" kann aus den folgenden Gründen nicht gelöscht werden: {1}
DeleteTimerDefinitionActionHandler.error=Der Zeitzähler ''{0}'' kann nicht gelöscht werden. Der Zähler wird durch zusätzliche Klassen- und Typattribute referenziert: {1}.
FilteredTreeCache.notExist.error=Bei der Ausführung des Skripts zum Filtern der Attributwerte von Klasse/Typ ''{0}'' ({1}) ({3}) ist ein Fehler aufgetreten: {4} existiert nicht.
#ru.naumen.core.server.dispatch.GetDtObjectTreeSelectionStateActionHandler
GetDtObjectTreeSelectionStateActionHandler.objectsSelected=Ausgewählt: {0} (Objekte der Klasse ''{1}'')
GetPossibleCasesForAddingBOActionHandler.emptyPossibleList=Für die Klasse ist kein Typ definiert. Es ist nicht möglich, Objekte zu erstellen.
GetPossibleCasesForAddingBOActionHandler.permissionError=Sie haben keine Rechte an diesem Vorgang
GetReportParametersActionHandler.reportParameterWithNotExistDtObject=Diese Vorlage verwendet ein Objekt, das nicht existiert
IncorrectAddresses=E-Mail-Adressen der Empfänger {0} sind falsch
HyperlinkUrlColumnDBRestriction=Das Attribut ''{0}'' ({1}) der Metaklasse Hyperlink vom Typ ''{2}'' ({3}) kann nicht mehr als {4} Zeichen in einer URL enthalten.
ImportModulesActionHandler.changeAttributeType=Einstellungen aus einer Datei können nicht geladen werden. Bei Typ/Klasse ''{0}'' für ein Attribut mit Code ''{1}'' unterscheidet sich der Wertetyp in den geladenen Einstellungen vom aktuellen Wertetyp.
ImportModulesActionHandler.isNotAnInteger=Der Wert des Attributs Modulversion ist keine ganze Zahl.
InboundMessageEnqueuer.logDebug=Enqueued message: {0}
ImportModulesActionHandler.moduleCode=Modul-Code
ImportModulesActionHandler.overrideAndDeclarationTogether=Einstellungen aus einer Datei können nicht geladen werden. In Typ/Klasse ''{0}'' ist ein Attribut mit dem Code ''{1}'' sowohl definiert als auch in den geladenen Einstellungen außer Kraft gesetzt.
ImportReportTemplatesActionHandler.loadError.wrongFileFormat=Es ist ein Fehler aufgetreten. Die Vorlagen können nicht geladen werden. Es wurde ein falsches Dateiformat gewählt.
ImpossibleToDelete.TaskMustHaveNoMessages=Es ist nicht möglich, eine Aufgabe zu löschen:\n - Es gibt unbearbeitete Nachrichten.
#ru.naumen.mailreader.server.dispatch.DeleteInboundMailServerConfigActionHandler
InboundMailServerConfig.cantDeleteHasTask=Posteingangsserver-Einstellungen können nicht gelöscht werden, da sie von der ''{0}'' Scheduler-Aufgabe referenziert werden
Hierarchy.export.downloadMail.description=Link zum Herunterladen einer Datei mit {0} Objekten aus dem ''{1}'' Hierarchiebaum: <br><a href="{2}">{2}</a><br><br>Nur der Benutzer, der die Liste exportiert hat, kann die Datei herunterladen.<br>Die Datei wird zum Herunterladen verfügbar sein {3} (bis zu {4}).
HyperlinkTitleColumnDBRestriction=Das Attribut ''{0}'' ({1}) der Metaklasse Hyperlink vom Typ ''{2}'' ({3}) darf nicht mehr als {4} Zeichen in seinem Namen enthalten.
ImportModulesActionHandler.mandatoryElemsAreEmpty=Die folgenden obligatorischen Parameter sind nicht angegeben: {0}.
ImportModulesActionHandler.moduleText=Modultext
ImportModulesActionHandler.overrideWithoutDeclaration=Einstellungen aus einer Datei können nicht geladen werden. In Typ/Klasse ''{0}'' wird das Attribut mit dem Code ''{1}'', das in den aktuellen Einstellungen deklariert ist, in geladenen Einstellungen überschrieben, ohne Attributdefinition in übergeordneten Klassen/Typen.
ImportModulesActionHandler.wrongCode=Ungültiges Codeformat "{0}".
Hierarchy.export.downloadMail.subject=Die Liste der Objekte aus dem ''{0}''-Hierarchiebaum wird erzeugt {1}.
ImportModulesActionHandler.wrongFileFormat=Falsches Dateiformat ausgewählt.
ImportReportTemplatesActionHandler.importError=Fehler beim Importieren von Vorlagen: {0}
InternalApplication.IOError=Fehler beim Lesen der Datei einer integrierten Anwendung
InternalApplication.applicationFileCorrupted=Dekodierung der integrierten Anwendungsdatei "{0}" ({1}) nicht möglich.
InternalApplication.applicationFileNotFound=Für die Anwendung ist keine Datei angegeben
ChangeTracking.body=Text
ActionType.changeTracking=Änderungsverfolgung
ChangeTracking.commentEdited={0} hat einen Kommentar geändert: {1}
ChangeTracking.editFormOpened={0} bearbeitet: {1}.
ChangeTracking.fileAdded={0} hat die Datei {1} angehängt.
ChangeTracking.errorInScript=Skriptfehler
ChangeTracking.object=Objekt: ''{0}''
ChangeTracking.objectEdited={0} hat geändert: {1}.
ChangeTracking.commentAdded={0} hat einen Kommentar hinzugefügt: {1}
ChangeTracking.responsibleChanged={0} änderte die verantwortliche Person in {1}.
ChangeTracking.script=Skript
ChangeTracking.stateChanged={0} hat seinen Status auf {1} geändert.
ChangeTracking.warn.disabled=Der Mechanismus der Änderungsverfolgung ist deaktiviert. Aktionen wie "Änderungsverfolgung" werden nicht durchgeführt.
ChangeTracking.warn.emptyTo=Die Aktion wird nicht ausgeführt, weil die Empfängerliste leer ist (das Feld "An" ist nicht ausgefüllt).
ChangeTracking.warn.otherEventsConflict=Möglicherweise wurde die Ereignisaktion nicht korrekt eingerichtet. Für das Ereignis "{0}" werden in der Weboberfläche gleichzeitig verschiedene Aktionen konfiguriert: "Auto-Update" und "Änderungsbenachrichtigung mit Schaltfläche Aktualisieren".
ChangeTracking.warn.liveCommentsConflict=Die Einstellung ist möglicherweise nicht korrekt. Für das Ereignis "{0}" sind verschiedene Aktionen im Web-Interface gleichzeitig konfiguriert: "Auto-Update" (in den Konfigurationseinstellungen aktiviert) und "Änderungsbenachrichtigung mit Schaltfläche Aktualisieren". Wir empfehlen, die automatische Aktualisierung in den Konfigurationseinstellungen zu deaktivieren.
#ru.naumen.core.server.util.ErrorDetails
ErrorDetails.relatedTo={0} ist den folgenden Objekten zugeordnet: {1}.
ErrorDetails.relatedTo_f={0} ist den folgenden Objekten zugeordnet: {1}.
EscalationScheme.codeAlreadyExist=Ein Eskalationsschema mit dem Code "{0}" ist bereits vorhanden.
EscalationScheme.codeMustBeUnic=Ein Eskalationsschema mit dem Code "{0}" kann nicht hinzugefügt werden. Der Code für das Eskalationsschema muss eindeutig sein.
EscalationScheme.rulesSettingsCodeMustBeUnic=Element der Korrespondenztabelle mit dem Code "{0}" kann nicht hinzugefügt werden. Der Code für einen Element der Korrespondenztabelle muss eindeutig sein.
presentation=Vorstellung
EntityCounterContainer=Informationen über die Anzahl der Objekte
ErrorDetails.childObject=Untergeordnetes Objekt {0}
EscalationScheme.deleteTargetValue=Das Eskalationsschema {0} wird als definierter Wert in den folgenden Korrespondenztabellen für Eskalationsschemata verwendet: {1}
EscalationScheme.eventActionsBadFqn=Das Eskalationsschema kann nicht geändert werden. Die Objekttypen im Eskalationsschema stimmen nicht mit den zugeordneten Aktionsobjekttypen überein: {0}
EscalationScheme.usedAsDefaultValue=Das Eskalationsschema kann nicht deaktiviert werden. Er wird als Standardwert in den Korrelationstabellen verwendet: {0}
EmployeeApi.cannotSetStrippedStack=Java-Stack-Emulation kann nicht deaktiviert werden
ErrorDetails.usedAsDefaultParamValue={0} wird als Standardwert der Parameter verwendet: {1}.
ErrorDetails.usedAsIconForUIElement={0} wird als Symbol für das Steuerelement verwendet: {1}.
ErrorDetails.usedAsMetaClassProperties={0} wird in der Metaklasseneinstellung verwendet: {1}.
ErrorDetails.usedInAttrPresentation={0} wird bei der Einstellung von Attributansichten verwendet: {1}
ErrorDetails.usedInDefaultSCProperties={0} ist als "Standard-Abfrageparameter" für die folgenden Klassen/Typen ausgewählt: {1}
ErrorDetails.vMapHasRemovedValues=Der Verzeichniseintrag ''Korrespondenztabellen'' ist mit den folgenden Archivobjekten verknüpft: {0}
ErrorDetails.usedAsDefaultValue={0} wird als Standardwert der Attribute verwendet: {1}.
ErrorDetails.usedAsDeterminer={0} wird verwendet, um Attributwerte zu definieren: {1}.
ErrorDetails.usedAsIconForObjectMenu=Objektaktionsmenü
EditSecurityProfileActionHandler.cannotReset=Profileinstellungen können aus folgenden Gründen nicht gespeichert werden:
EventAction.warn.expiredNDAP=NDAP
EventAction.warn.expiredLicense=Die Aktion wurde aufgrund des Ablaufs der Lizenz für Modul {0} deaktiviert. Um die Aktion zu aktivieren, aktualisieren Sie die Lizenzdatei.
#ru.naumen.core.server.script.api.EmployeeApi
EmployeeApi.cannotSetEmulatedStack=Java-Stack-Emulation kann nicht aktiviert werden
EscalationScheme.deleteDefaultObject=Eskalationsschema {0} wird als Standardwert in den folgenden Korrespondenztabellen für Eskalationsschemas verwendet: {1}
InternalApplication.uploadCompletedWithWarning=Anwendungsdateien werden geladen. Die folgenden Dateien wurden nicht heruntergeladen, da sie keine integrierten Anwendungen haben: {0}
InternalApplication.licenseValidate.hashNotSpecified=Prüfsumme der Datei der integrierten Anwendung nicht angegeben.
InternalApplication.requiredFilesError=Im Archiv der integrierten Anwendung "{0}" ({1}) muss die Datei „index.html“ oder Dateien mit einer lizenzierten integrierten Anwendung enthalten: license.xml und app.zip.
InternalApplication.silentModeError=Eine Verbindung zum internen Anwendungsserver ist nicht möglich. Silent-Mode aktiviert.
InternalApplication.uploadCompleted=Anwendungsdateien wurden heruntergeladen
JaxbStorageLicenseSerializer.actionsForUnlicensed=Aktionsblöcke für nicht lizenzierte Benutzer:
InternalApplication.cannotConnectToInternalApplicationsServer=Keine Verbindung zum Integrierten-Anwendungsserver
InternalApplication.internalApplicationError=Interner Anwendungsfehler
InternalApplication.licenseValidate.creationDateInFuture=Das Erstellungsdatum der Datei liegt in der Zukunft.
InternalApplication.licenseValidate.expirationDateIncorrectFormat=Das Ablaufdatum der Lizenz hat nicht das richtige Format. Der Parameterwert muss im Format "YYYY.MM.DD" angegeben werden.
InternalApplication.uploadFailedWithWarning=Anwendungsdateien wurden nicht heruntergeladen, da sie keine integrierten Anwendungen haben: {0}
#LicenseImportValidation
JaxbStorageLicenseSerializer.formatException=Der Wert des Parameters „expirationDate“ hat im folgenden {0} nicht das richtige Format. Der Parameterwert muss im Format „JJJJ.MM.TT“ angegeben werden.
JaxbStorageLicenseSerializer.groupsOfModules=Modulgruppen:
JaxbStorageLicenseSerializer.licenseGroups=Lizenzgruppen:
InternalApplication.licenseValidate.expirationDatePassed=Das Ablaufdatum der Lizenz ist abgelaufen.
InternalApplication.licenseValidate.formatError=Die Lizenzdatei kann nicht geladen werden, da sie das falsche Format hat.
InternalApplication.requiredIndexHtmlError=Das Archiv muss die Datei index.html enthalten.
InternalApplication.licenseValidate.creationDateNotSpecified=Erstellungsdatum der Datei nicht angegeben.
InternalApplication.editScriptModuleInArchiveError=Fehler beim Ändern des Skriptmoduls ''{0}'' im Archiv von integrierter Anwendung ''{1}''. {2}
InternalApplication.fileIsNotZipped=Ungültiges Dateiformat der integrierten Anwendung "{0}" ({1}). Die Datei muss im ZIP-Format vorliegen und darf nicht leer sein.
InternalApplication.internalApplicationErrorWithDetails=Interner Anwendungsfehler ({0})
InternalApplication.licenseValidate.authorNotSpecified=Der Autor, der die Datei erstellt hat, ist nicht angegeben.
InternalApplication.licenseValidate.checksumIncorrect=Die Prüfsumme der integrierten Anwendungsdatei ist falsch.
InternalApplication.cantDecryptApplication=app.zip-Datei kann nicht entschlüsselt werden. Wenden Sie sich an den Anbieter der integrierten Anwendung.
InternalApplication.editArchiveError=Fehler beim Ändern von Dateien im Archiv von integrierter Anwendung''{0}''. {1}
InternalApplication.licenseValidate.cantBeLoaded=Die Lizenzdatei konnte nicht geladen werden.
LicenseContainer=Lizenzinformationen
LicenseValidation.metaClassNotFound={0} auf der Plattform nicht verfügbar ist, wird das ''{1}''-Kontingent bestehenden Klassen/Typen zugewiesen.
LicenseValidation.metaClassesNotFound={0} sind nicht auf der Plattform vorhanden, Kontingent ''{1}'' wurde bestehenden Klassen/Typen zugewiesen.
LicenseValidation.missingQuotaAttributes=Der Lizenzdatei fehlen erforderliche Attribute: {0}.
LicenseValidation.moreThanOneQuotaForClass=In der Lizenzdatei ist eine Metaklasse ''{0}'' durch mehrere Kontingente begrenzt: ''{1}'', ''{2}''.
LicenseValidation.nonUniqueQuotaCode=Die Lizenzdatei enthält mehrere Kontingente mit demselben Namen.
LicenseValidation.existingObjectsViolateLicense=Die Anzahl vorhandener Objekte überschreitet den zulässigen Wert aus der Lizenzdatei.
JaxbStorageLicenseSerializer.outOfRangeDate=Der Wert des Parameters ''expirationDate'' ist im folgenden {0} ungültig. Die angegebenen Daten existieren nicht.
MailLogRecord.connectionEvent=Verbinden mit dem Posteingangsserver
MailReader.ContentIDParseFailed=Content-ID kann nicht abgerufen werden. Der Inhalt der E-Mail entspricht nicht dem RFC 2111
MailLogRecord.validationEvent=Validierung der E-Mail
MailLogRecord.processEvent=E-Mail-Bearbeitung
MailLogRecord.receiveEvent=Sammeln von E-Mails
MailLogRecord.reprocessEvent=Weiterverarbeitung der E-Mail
#ru.naumen.mailreader.server.processor.MailProcessHelper
MailProcessHelper.tooLongRecipientList=Die Empfängerliste der Nachricht ('{0}') ist länger als {1} Zeichen und wird daher gekürzt, wenn sie in das Posteingangsprotokoll geschrieben wird. Vollständige Empfängerliste: {2}
MailProcessorRule.cantDeleteHasTask=Sie können eine Posteingangsregel nicht löschen, da sie von den ''{0}'' Scheduler-Aufgaben referenziert wird
MailReceiver.messageSavingError=Nachricht kann nicht in Ordner {0} gespeichert werden: {1}
MessageState.outgoing=andere
MessageState.reject=Meldung abgelehnt
#ru.naumen.core.server.flex.spi.BeforeDeleteMetaClassAttributeListaner
MetaClass.deleteError=Es gibt Attribute, die auf sie verweisen:
MetaClass.deleteError.attribute.case=\n"{0}" (Typ: "{1}" Klasse "{2}")
MetaClass.deleteError.attribute.class=\n"{0}" (Klasse: "{1}")
#ru.naumen.metainfo.server.spi.elements.MetaClassImpl
MetaClassImpl.addDeclaredAttribute.duplicateCode=Ein Attribut mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Attributcode muss innerhalb von {1} eindeutig sein.
MailReceiver.dumpAttachError=Meldungsdump kann nicht an Protokolleintrag angehängt werden: {0}
MessageState.attach=Mail wird an das Business-Objekt angehängt
MessageState.error=Verarbeitungsfehler
MessageState.new_bo=Business-Objekt erstellt
MetaClassImpl.addDeclaredAttribute.notAllowedAttrCode=Das Attribut kann nicht hinzugefügt werden. Wert {1} kann nicht als benutzerdefinierter Attributcode verwendet werden
MailReaderSchedulerTaskBase.ErrorFailed=Nachrichtenverarbeitungsfehler, ID {0}: {1}
MailReaderSchedulerTaskBase.hasNoProcessor=Für die Aufgabe ''{0}'' wurde keine Regel zur Behandlung eingehender Post definiert
MailReaderSchedulerTaskBase.hasNoServer=Kein Posteingangsserver für Aufgabe ''{0}'' definiert
MailReaderSchedulerTaskBase.logDebug=Verarbeitung von Nachrichten in der Warteschlange: {0}
MailReaderSchedulerTaskBase.mailProcessorDisabled=Posteingangsregel für Aufgabe ''{0}'' deaktiviert
MailReaderSchedulerTaskBase.mailServerDisabled=Für die Aufgabe ''{0}'' ist der Posteingangsserver deaktiviert
MailServerAuthenticationFailed=Authentifizierung auf dem Postausgangsserver fehlgeschlagen
MetaClassImpl.addDeclaredAttribute.ofClass=der Klasse
MetaClassImpl.attribute.manyToManyEqualsTableCatalogs=Das Attribut kann nicht hinzugefügt werden. Ein Attribut des Typs ''Satz von Verzeichnis-Elementen'' existiert bereits mit dem Code ''{0}''. Die ersten 8 Zeichen des hinzuzufügenden Attributcodes müssen innerhalb des Attributtyps ''Satz von Verzeichnis-Elementen'' eindeutig sein. Ändern Sie den Code des Attributs.
MetaClassImpl.attributeNotFound=Attribut nicht gefunden: {0}:{1}
MetaClassImpl.maxIdLengthExceeded=Die Länge von {0} ist größer als der Maximalwert von {1}
#ru.naumen.metainfo.server.spi.listeners.MetaClassUsedinDefaultScListener
MetaClassUsedinDefaultScListener.errorDelete=Typ ist als "Standard-Ticketparameter" für die folgenden Klassen/Typen ausgewählt: {0}.
MetaClassUsedinDefaultScListener.errorDeleteChild=Der untergeordnete Typ ''{0}'' wird als "Standard-Ticketparameter" für die folgenden Klassen/Typen ausgewählt: {1}.
MimeDecoder.ErrorWhileConvertingHTML=HTML-Konvertierungsfehler
MimeDecoder.badEncodingContent=[falsche Codierung]
MetaClassImpl.addDeclaredAttribute.ofType=des Typs
MetaClassImpl.addDeclaredAttributeGroup.duplicateCode=Das "code"-Tag in der Attributgruppe für Typ/Klasse ''{1}'' enthält den Wert ''{0}'', der bereits in Typ/Klasse ''{2}'' vorhanden ist. Der Code muss innerhalb des Typs/der Klasse eindeutig sein.
MetaClassImpl.attribute.cantBeUnique=Ein Attribut mit dem angegebenen Typ kann nicht eindeutig sein
NavigationSettingsUIEventListener.contentUsedInTopMenuElements=Inhalt {0}/{1} kann nicht gelöscht werden. Es wird in den oberen Menüeinstellungen in den Elementen verwendet: {2}
MetaClassImpl.attribute.manyToManyEqualsTable=Das Attribut kann nicht hinzugefügt werden. Ein Attribut des Typs ''Satz von Links zu Geschäftsobjekten'' mit dem Code ''{0}'' ist bereits vorhanden. Die ersten 8 Zeichen des hinzuzufügenden Attributcodes müssen innerhalb des Attributtyps ''Satz von Links zu Geschäftsobjekten'' eindeutig sein. Ändern Sie den Attributcode.
MimeDecoder.file=Datei
ModifyWfProfileActionHandler.masterIsRemoved=Der Typ des führenden Objekts ist im Archiv: {0}.
NamedLicenseExceeded=Für benannte Lizenzen {0} wurde die maximal zulässige Anzahl von Benutzern überschritten.
ModifyWfProfileActionHandler.profileCantBeSwitchedOn=Das verknüpfte Workflow-Profil {0} konnte nicht aktiviert werden.
ModifyWfProfileActionHandler.slaveIsRemoved=Der Slave-Objekttyp ist im Archiv: {0}.
NavigationSettingsUIEventListener.contentUsedInLeftMenuElements=Inhalt {0}/{1} kann nicht gelöscht werden. Es wird in den linken Menüeinstellungen verwendet: {2}
NavigationSettingsUIEventListener.linkObjectUUIDNotOfClass=Objekt mit UUID={0} ist keine Instanz einer Klasse
NavigationSettingsUIEventListener.deleteProfileAttention=Achtung.<ol><li>{0}</li></ol>Profil löschen?
MailServerSettingsIncorrect=Die Verbindungseinstellungen des Postausgangsservers sind falsch
NavigationSettingsValidate.cardTemplateMissingWarning=Menüpunkt ''{0}'' (''{1}'') verweist auf die Vorlage, die im System nicht existiert: uuid = "{2}".
NavigationSettingsValidate.absentAddButtonValue=Die "list"-Eigenschaft des "value"-Tags fehlt beim oberen-Menüpunkt mit dem Code "{0}".
NavigationSettingsValidate.absentAddButtonValueFqn=Fehlendes "id"-Tag für oberes-Menüelement mit dem Code ''{0}''.
NavigationSettingsValidate.incorrectMenuItemType=Die "type"-Eigenschaft des oberen-Menüpunkts mit dem Code ''{0}'' enthält einen falschen Wert oder fehlt.
ObjectQuota.expirationError=Das Erstellen von Objekten des Typs ''{0}'' ist aufgrund von Lizenzbeschränkungen nicht verfügbar. Wenden Sie sich an Ihren Systemadministrator.\nWeitere Details: Kontingent ''{1}'' ist abgelaufen.
ObjectQuota.violationError[few]=Das Erstellen von Objekten des Typs ''{0}'' ist aufgrund von Lizenzbeschränkungen nicht verfügbar. Wenden Sie sich an Ihren Systemadministrator.\nMehr: Typ ''{0}'' nimmt am Kontingent ''{1}'' mit einem Limit von {2} Objekten teil.
ObjectQuota.violationError[many]=Das Erstellen von Objekten des Typs ''{0}'' ist aufgrund von Lizenzbeschränkungen nicht verfügbar. Wenden Sie sich an Ihren Systemadministrator.\nMehr: Typ ''{0}'' nimmt am Kontingent ''{1}'' mit einem Limit von {2} Objekten teil.
NavigationSettingsValidate.absentChildrenInChapter=Fehlendes "children"-Tag für den oberen-Menüpunkt mit dem Code ''{0}''.
NavigationSettingsValidate.absentMenuItemTitle=Fehlendes "title"-Tag für den oberen-Menüpunkt mit dem Code ''{0}''.
NavigationSettingsValidate.absentReferenceClassFqn=Fehlende "fqn"-Eigenschaft im "m:referenceValue"-Tag des oberen-Menüpunkts mit dem Code ''{0}''.
NavigationSettingsValidate.absentReferenceValue=Fehlendes "m:referenceValue"-Tag im oberen-Menüelement mit dem Code ''{0}''.
NavigationSettingsValidate.absentTab=Element {0} von Menü ''{1}'' ({2}) bezieht sich auf dem Tab {3}, die im System nicht vorhanden ist
NavigationSettingsValidate.absentTabs=Element {0} von Menü ''{1}'' ({2}) bezieht sich auf Tabs, die im System nicht vorhanden sind
NavigationSettingsValidate.tab=Tab
NavigationSettingsValidate.tabBar=Tab-Leiste
NavigationSettingsValidate.incorrectAddButtonValueFqn=Das "id"-Tag des oberen-Menüpunkts mit dem Code ''{0}'' enthält einen ungültigen Wert von ''{1}''. Das "id"-Tag muss den Metaklassencode enthalten.
NavigationSettingsValidate.incorrentReferenceClassFqn=Die Eigenschaft "fqn" im Tag "m:referenceValue" des oberen-Menüpunkts mit dem Code ''{0}'' enthält einen falschen Wert ''{1}''. Die Eigenschaft "fqn" muss den Metaklassencode enthalten.
NavigationSettingsValidate.incorrentReferenceTabUUID=Die Eigenschaft "tab-uuid" im "m:referenceValue"-Tag des oberen-Menüpunkts mit dem Code ''{0}'' enthält einen ungültigen Wert von ''{1}''. Die Eigenschaft "tab-uuid" muss die UUID des Metaklassen-Tabs enthalten
NavigationSettingsValidate.absentLeftMenuAddButtonValue=Die Eigenschaft "fqns" des Tags "fqns" fehlt beim linken Menüpunkt mit dem Code ''{0}''.
NavigationSettingsValidate.absentLeftMenuAddButtonValueFqn=Fehlendes "id"-Tag für linkes Menüelement mit Code ''{0}''.
NavigationSettingsValidate.incorrectLeftMenuAddButtonValueFqn=Das "id"-Tag des linken Menüpunkts mit dem Code ''{0}'' enthält den falschen Wert ''{1}''. Das "id"-Tag muss den Metaklassencode enthalten.
NavigationSettingsValidate.potentialUnnestedTiles=Im Schnellzugriffsbereich gibt es Kacheln für nicht vorhandene linke Menüelemente mit Codes: ''{0}''
NavigationSettingsValidate.top=oben
NavigationSettingsUIEventListener.objectWithUUIDNotFoundMessage=Objekt mit UUID={0} nicht gefunden
NavigationSettingsUIEventListener.profileIsOneOfInLeftMenuElements=Das Profil wird in den Einstellungen des linken Menüs für Elemente verwendet: {0}. Wenn Sie ein Profil löschen, wird es von den linken Menüeinstellungen ausgeschlossen.
NavigationSettingsUIEventListener.profileIsSingleInLeftMenuElements=Das Profil ist das einzige in den Sichtbarkeitseinstellungen für die Menüpunkte auf der linken Seite: {0}. Wenn das Profil gelöscht wird, werden die Menüpunkte deaktiviert.
NavigationSettingsValidate.incorrectListTemplateCode=Das "listTemplate"-Tag des linken Menüpunkts ''{0}'' enthält einen falschen Wert oder fehlt.
NavigationSettingsValidate.incorrectMenuItemTitle=Das "title"-Tag des oberen-Menüpunkts mit dem Code ''{0}'' im Gebietsschema ''{1}'' enthält den ungültigen Wert ''{2}''. Der Tag-Wert muss eine Zeichenfolge mit einer Länge zwischen 1 und 64 Zeichen sein.
NavigationSettingsValidate.incorrectMenuItemTitleLang=Das "lang"-Attribut des "title"-Tags des oberen-Menüpunkts mit dem Code ''{0}'' muss ausgefüllt werden.
NavigationSettingsValidate.left=links
ObjectQuota.violationError[one]=Das Erstellen von Objekten des Typs ''{0}'' ist aufgrund von Lizenzbeschränkungen nicht verfügbar. Wenden Sie sich an Ihren Systemadministrator.\nWeitere Details: Der Typ ''{0}'' nimmt am Kontingent ''{1}'' mit dem Objekt Constraint {2} teil.
NavigationSettingsUIEventListener.listTemplateUsedInLeftMenuElements=Vorlage kann nicht gelöscht werden, da sie in Menüelementeinstellungen verwendet wird: {0}
NavigationSettingsUIEventListener.profileUsedInLeftMenuElements=Das Profil muss mindestens eine absolute Rolle enthalten, wie sie in den linken Menüeinstellungen verwendet wird: {0}
NavigationSettingsUIEventListener.tabUsedInLeftMenuElements=Tab {0}/{1} kann nicht entfernt werden. Es oder darin verschachtelte Tabs werden in den Einstellungen des linken Menüs verwendet: {2}
NavigationSettingsUIEventListener.tabUsedInTopMenuElements=Tab {0}/{1} kann nicht entfernt werden. Es wird in den oberen Menüeinstellungen in den Elementen verwendet: {2}
NavigationSettingsValidate.nonUniqueMenuItemCode=Die Eigenschaft "code" des oberen-Menüpunkts hat einen ungültigen Wert von ''{0}''. Der Eigenschaftswert muss für alle oberen Menüelemente eindeutig sein.
profileVers=[plan.]
Priority.Normal=Standard
ProtocolAdapter.logDeletedMessageError=Message was deleted on server, skipping.
Priority.High=Hohe
ProtocolAdapter.logError=Message with subject ''{0}'' has decoding error, skipping. See file {1}.
PushPosition.bottomLeft=Unten links
PushPosition.system=System (unten volle Breite)
PushPosition.topLeft=Oben links
PushPosition.topRight=Oben rechts
PushPosition.bottomRight=Unten rechts
ProtocolAdapter.logOutOfMemoryError=Message with subject ''{0}'' is very big. Out of memory error, skipping.
#ru.naumen.metainfo.server.spi.dispatch.RemoveClassMetainfoActionHandler
RemoveClassMetainfoActionHandler.metaclassUsedInWfProfile={0} kann nicht archiviert werden. {0} wird von verknüpften Workflow-Profilen referenziert: {1}
RemoveTimerDefinitionActionHandler.error=Ein Zeitzähler ''{0}'' kann nicht archiviert werden. Der Zähler wird durch zusätzliche Klassen- und Typattribute referenziert: {1}.
ReprocessMailEvent.failure={0} wurde von der Mailverarbeitungsregel {1} mit einem Fehler erneut versucht:
ReprocessMailEvent.issues={0} wurde von der Mail-Behandlungsregel {1} mit Merkmalen erneut verarbeitet:
ReprocessMailEvent.success={0} wurde von der Mailverarbeitungsregel {1} erfolgreich weiterverarbeitet.
ResetUIActionHandler.cannotReset=Die Karteneinstellungen können aus bestimmten Gründen nicht zurückgesetzt werden:
RestServiceController.attrNotEditable=Attribut "{0}" ist nicht editierbar
RestServiceController.getEmployeeByAccessKey.unavailable=Keine Zugriffsrechte zur Ausführung der Methode oder der Integrationsparameter ist ausgeschaltet.
ReprocessMessage.notProcessed=Der Buchstabe ''{0}'' wurde noch nicht verarbeitet und kann daher nicht erneut verarbeitet werden.
RestServiceController.attrNotEditableInCurrentState=Der aktuelle Status des Objekts ("{0}") erlaubt es nicht, den Wert des Attributs "{1}" zu ändern
RestServiceController.attrNotEditableInTransition=Wenn ein Objekt vom Status "{0}" auf "{1}" wechselt, kann der Wert des Attributs "{2}" nicht geändert werden
#ru.naumen.core.server.rest.RestServiceController
RestServiceController.attributeWarning=Aufruf {0}: {1}
RestServiceController.exec.error.script.param=Fehler beim Lesen des Skripts.
ScriptApi.call.error=Beim Aufruf der Methode {0} mit den Parametern {1} ist ein Fehler aufgetreten
RuntimeContainer=Informationen über virtuelle Maschinen
SaveBreadCrumbActionHandler.validateExistAttributes=Das Element "Brotkrümel" kann nicht hinzugefügt werden. Das Attribut "{0}" existiert nicht in der Metaklasse "{1}".
SaveBreadCrumbActionHandler.validateUniqueCrumb=Das "Brotkrümel"-Element kann nicht hinzugefügt werden. Die Metaklasse "{0}" kann nur in einem einzigen "Brotkrümel"-Element vorkommen.
SaveReportTemplateActionHandler.templateFileNotSpecified=Keine Datei für Vorlage mit Code ''{0}'' angegeben
SaveUIActionHandlerBase.quickAddAndEditForm.codeMustBeUnique=Ein Formular zum schnellen Hinzufügen und Bearbeiten mit dem Code "{0}" kann nicht hinzugefügt werden. Der Formularcode muss innerhalb der Klasse eindeutig sein.
SchedulerTaskNotExists=Die Scheduler-Aufgabe mit dem Code ''{0}'' existiert nicht.
#ru.naumen.core.server.script.ScriptHelper
ScriptHelper.wrongChecksum=Die Prüfsumme des Moduls ''{0}'' ist falsch.
ScriptService.MaxNoticeScriptLength=die maximale Größe des in der Meldung verwendeten Skripts wurde überschritten (65535 Zeichen)
#ru.naumen.bcp.server.validation.ScriptFilteredValueValidator
ScriptFilteredValueValidator.parameterError=Der Wert ''{0}'' erfüllt nicht die Filterbedingungen für ''{1}''
ScriptUtils.notSupportAttachFromExternal=Die Datei ist nicht angehängt worden. Aktiver Speicher hat einen anderen Typ als ''groovy'', Einstellungen in file-storage.xml ändern
ScriptUtils.objecUUIDtNotExists=Das Objekt mit der angegebenen ID ''{0}'' existiert nicht.
ScriptUtils.wrongEditValue=Der Wert ''{0}'' stimmt nicht mit dem Typ des Bearbeitungsattributs ''{1}'' überein.
SaveReportTemplateActionHandler.templateWithCodeAlreadyExist=Eine Vorlage mit diesem Code ''{0}'' existiert bereits
#ru.naumen.metainfo.server.spi.dispatch.scheduler.SaveTriggerActionHandler
SaveTriggerActionHandler.concreteDateTrigger.error=Das Datum/die Uhrzeit der Aufgabe darf nicht kleiner als das aktuelle Datum sein.
SaveTriggerActionHandler.concreteDateTrigger.null.error=Das Datum/die Uhrzeit der Aufgabe sollte angegeben werden.
SaveTriggerActionHandler.periodicTrigger.error=Die Anzahl der Monate muss weniger als 12 betragen.
SaveUIActionHandlerBase.contentCodeCantBeUnique=Inhalte mit dem Code "{0}" können nicht hinzugefügt werden. Der Inhaltscode muss unter den Inhalten von {1} Klassen/Typen eindeutig sein.
SaveUIActionHandlerBase.contentCodeCantBeUnique.tab=Eine Tab mit dem Code "{0}" kann nicht hinzugefügt werden. Der Code muss für die einzelnen Tabs der anpassbaren Tab-Leiste eindeutig sein.
SaveUIActionHandlerBase.editForm=Formulare bearbeiten
SaveUIActionHandlerBase.newEntryForm=Formulare hinzufügen
ScriptService.MaxLength=die maximale Größe des Textes oder des Warnskripts wurde überschritten (65535 Zeichen)
#Схемы
#ru.naumen.core.server.graph.service.SchemaRegistrationService.init()
SchemaRegistrationService.init.info=Ein Schema aus {0} wird registriert
SaveEmbeddedApplicationActionHandler.applicationWithCodeAlreadyExist=Eine Anwendung mit dem Code ''{0}'' existiert bereits.
#ru.naumen.core.server.script.ScriptServiceException
ScriptServiceException.transactionTimeout=Skripttransaktion abgebrochen, möglicherweise Laufzeit überschritten.
ScriptService.MaxScriptLength=die maximale Größe des in der Meldung verwendeten Skripts wurde überschritten (65535 Zeichen)
#ru.naumen.metainfo.server.spi.dispatch.SaveUIActionHandlerBase
SaveUIActionHandlerBase.changeCaseForm=Formulare für die Änderung des Typs
ScriptFilteredValueValidator.valueError=Der Wert ''{0}'' entspricht nicht den Filterbedingungen für das Attribut ''{1}''
ScriptServiceException.errorCompilingModule=Es ist ein Modulkompilierungsfehler {0} aufgetreten. {1}
ScriptServiceException.errorCompilingModules=Es ist ein Fehler bei der Modulkompilierung aufgetreten. {0}
ScriptServiceException.invalidAnnotations=Falsche Verwendung von Anmerkungen in Modul {0}. Einzelheiten sind dem Anwendungsprotokoll zu entnehmen.
#ru.naumen.core.server.script.spi.ScriptUtils
ScriptUtils.attributeNotExists=Das bearbeitete Objekt enthält nicht die Attribute ''{0}''.
ScriptUtils.endTimeMustBeAfterStartTime=Die Endzeit des Ereignisses muss nach der Startzeit liegen
ScriptUtils.fileNotFoundInExternalStorage=Datei mit ID ''{0}'' im externen Speicher nicht gefunden
ScriptUtils.incorrectDate=Die Uhrzeit des Ereignisses muss im Bereich 02.01.1900 bis 01.01.3000 liegen
ScriptUtils.notFilledRequiredAttrs=Obligatorische Attribute nicht ausgefüllt - {0}
SaveEmbeddedApplicationActionHandler.applicationUsagePointWithCodeAlreadyExist=Ort der Verwendung der Anwendung mit dem Code ''{0}'' existiert bereits.
SchedulerTaskAlreadyExists=Eine Scheduler-Aufgabe mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Scheduler-Aufgabe-Code muss eindeutig sein.
Script.NonEdit=Das Skript mit dem Code "{0}" kann nicht geändert werden. Aktualisieren Sie die Liste.
SaveEmbeddedApplicationActionHandler.invalidCode=Die Anwendung hat einen ungültigen Code ''{0}''. Das Feld "Code" muss mindestens ein Zeichen, aber nicht mehr als {1} enthalten, mit einem Zeichen des lateinischen Alphabets beginnen und darf nur aus lateinischen Buchstaben und Zahlen bestehen.
SaveReportTemplateActionHandler.templateFileWrong=Die angegebene Vorlagendatei ist keine Berichtsvorlage oder enthält Fehler
ScriptUtils.fileNotFoundOrDirectory=Datei {0} nicht gefunden oder ist ein Verzeichnis
ScriptUtils.wrongFileParamType=Das Objekt mit der übergebenen ID stimmt nicht mit dem Dateityp überein.
ScriptUtils.wrongStorageType=Die Methode ist für plattenbasierte Dateispeicher verfügbar. Überprüfen Sie Ihren Speichertyp.
SetAttrValueOperationCreateOnly.CreationDateChangeIsDenied=Der Wert des Attributs ''Datum der Erstellung'' (creationDate) des Objekts ''{0}'' ({1}) kann nicht geändert werden.
SecSettings.groupExists=Eine Benutzergruppe mit dem Code "{0}" kann nicht hinzugefügt werden. Der Benutzergruppencode muss eindeutig sein.
SecSettings.groupNotExists=Es gibt keine Gruppe:
SecSettings.systemGroupExists=Eine Gruppe kann nicht hinzugefügt werden. Der Wert "{0}" kann nicht als Benutzergruppencode verwendet werden.
#ru.naumen.core.server.bo.bop.SetAgreementsOperation
SetAggrementsOperation.removedAgreementsInDefaultSCParams=Der Service innerhalb der ''{0}''-Vereinbarung wird als Standard-Anfrageparameter für die folgenden Klassen/Typen ausgewählt: {1}
#ru.naumen.core.server.bo.employee.SetEmployeeLicenseOperation
SetEmployeeLicenseOperation.error=Der Lizenztyp kann nicht geändert werden. \n {0}
SetEmployeeLicenseOperation.lastPerformerInTeamResponsibleForStates={0} ist der einzige Ausführende im Team ''{2}'', das in den Statusberichten als verantwortlich bezeichnet wird: {1}
#ru.naumen.core.server.bo.bop.SetCallCasesOperation
SetCallCasesOperation.removedCallCasesInDefaultSCParams=Der Service- und Ticket-Typ ''{0}'' wird als Standard-Ticket-Parameter für die folgenden Klassen/Typen ausgewählt: {1}
SetEmployeeLicenseOperation.employeeResponsibleForStates={0} ist als verantwortlich zugewiesen in den Status: {1}
#ru.naumen.bcp.server.operations.SetAttrValueOperationCreateOnly.validate()
SetAttrValueOperationCreateOnly.AttrValueChangeIsDenied=Das Attribut ''{0}'' kann nicht geändert werden!
SetEmployeeLicenseOperation.responsibleForOpenedObj={0} ist für die Objekte verantwortlich: {1}
SetEmployeeLicenseOperation.teamResponsibleForStates={0} ist als verantwortlich zugewiesen in den Status: {1}
SetEmployeeLoginOperation.employeeIsRemoved=Für den Mitarbeiter "{0}" wurde das Attribut "{1}" nicht geändert, da sich der Mitarbeiter im Archiv befindet.
SetInboundMessageStatusBusinessAction.logWarn=Versuch, den Status eines nicht existierenden InboundMailQueueItem zu ändern; uuid = {0}
#ru.naumen.core.server.catalog.SetItemParentOperation.validate()
SetItemParentOperation.ItemMustNotBeAFolderParent=Ein Verzeichniselement kann nicht übergeordnet zu einem Verzeichnisordner sein.
SetMasterMassProblemOperation.hasNoMassProblemFlag=Das Objekt ''{0}'' kann nicht als Massenobjekt verwendet werden. Weil das Massenattribut (massProblem) nicht gesetzt ist.
SetMasterMassProblemOperation.hasNoSuitableWfProfile=Es gibt kein geeignetes zugehöriges Workflow-Profil, um Objekt {0} an Objekt {1} zu binden
SetMetaClassOperation.Validate.isSystemCase=Der Typ kann nicht geändert werden. Typ ''{0}'' ist ein Systemtyp.
SetMasterMassProblemOperation.disabledStatus={0} befindet sich im Status ''{1}'', in dem das Hinzufügen von Links nicht erlaubt ist.
SetMasterMassProblemOperation.wfProfileNotFound=Kein zugeordnetes Workflow-Profil mit Code ''{0}'' gefunden
SetMetaClassOperation.Validate.addObject.isRemoved=Das Objekt kann aus folgenden Gründen nicht hinzugefügt werden: Typ ''{0}'' befindet sich im Archiv.
#ru.naumen.core.server.bo.bop.OperationValidator
SetMetaClassOperation.Validate.isRemoved=Der Typ kann nicht geändert werden. Typ ''{0}'' ist archiviert
#ru.naumen.sec.server.superuser.SuperUserDetailsService
SuperUserDetailsService.userNotFound=Benutzer mit Login ''{0}'' wird im System nicht gefunden
SilentMode.ipsIsIncorrect=Die IP hat das falsche Format
StringColumnDBRestriction=Attribut ''{0}'' ({1}) vom Typ Die Metaklassenzeichenfolge ''{2}'' ({3}) darf nicht mehr als {4} Zeichen enthalten.
StringContainsXSSVulnerability=Das Feld ''{0}'' enthält unzulässige Zeichen. Bitte geben Sie den richtigen Wert ein.
SwitchLeftMenuItemActionHandler.badItem=Es wurde kein in den Objekteinstellungen angegebenes Objekt gefunden. Ändern Sie die Einstellungen so, dass das Element eingeschaltet werden kann.
SilentMode=Stiller Modus
StringContainsXSSVulnerabilityClient=Das Feld enthält verbotene Zeichen.
#ru.naumen.core.server.catalog.servicetime.SetServiceTimeExclusionsOperation
SetServiceTimeExclusionsOperation.checkExclusionDateUnique=Eine Ausnahme kann nicht hinzugefügt werden. Nicht eindeutiger Datumswert angegeben
SetServiceTimeStateOperation.checkActiveCopy=Sie können eine Serviceklasse nicht genehmigen, weil sie keine aktive Version hat
#ru.naumen.core.server.bo.bop.SetServicesOperation
SetServicesOperation.removedServicesInDefaultSCParams=Service ''{0}'' ist als Standard-Abfrageparameter für die folgenden Klassen/Typen ausgewählt: {1}
SetStateOperation.transitionDisabledError=Übergang gemäß den Workflow-Einstellungen nicht erlaubt
SetRemovalDateOperation.dateIsNull=Das Archivdatum sollte für das Archivobjekt festgelegt werden
SetRemovalDateOperation.dateIsntNull=Das Archivdatum kann nicht für nicht -archives Objekt festgelegt werden
#ru.naumen.bcp.server.operations.SetValueMapItemAttrsOperation.validate()
SetValueMapItemAttrsOperation.AttrValueChangeIsDenied=Ein Fehler in der Korrespondenztabelle mit dem Code ''{0}''. Eine Änderung der bestimmenden Attribute ist verboten.
#ru.naumen.core.server.script.api.TeamApi
TeamApi.UUIDsNotExists=Objekte mit den angegebenen Bezeichnern existieren nicht: {0}
SystemPropertiesContainer=Anwendungsparameter (System)
UIValidatorBean.useAtCustomForm=Die Attributgruppe kann nicht gelöscht werden. Die Gruppe wird in den benutzerdefinierten Formulareinstellungen ''{1}'' verwendet
UIValidatorBean.linkToContent.useAtObjectList=Die Klasse/der Typ ''{3}'' kann nicht gelöscht werden, da sie/er an der Einstellung des linken Menüpunkts: ''{2}'' beteiligt ist
UIValidatorBean.metaclassHasNoWorkflow=Kein Workflow mit der Klasse ''{0}'' verbunden
UIValidatorBean.tabBarNoTabsError=Es muss mindestens eine Tabs geben
UIValidatorBean.tabLayoutError=Fehler bei der Karteneinstellung
UIValidatorBean.useAtAddFileTool=Die Attributgruppe kann nicht gelöscht werden. Die Gruppe wird in den Einstellungen der Schaltfläche ''{1}'' verwendet
UIValidatorBean.windowCaptionAttrNotExists=Die Einstellung kann nicht gespeichert werden. Das Attribut "{0}" existiert nicht in der Metaklasse "{1}".
UniqueAttributeIndexUpdater.cantCreateIndex=Es gibt Objekte mit einem Attributwert, der nicht eindeutig ist.\n Das Attribut ''{1}'' kann nicht eindeutig sein.
UnknownMailServerHost=Es kann keine Verbindung zum Postausgangsserver hergestellt werden
UnlicensedRuleValidator.absentEventActionOrScript=In Aktionsblöcken für nicht lizenzierte Benutzer: {0}, eine der Aktionen enthält keinen Skriptcode und/oder keinen Aktionscode.
UploadServiceImpl.errorFileItemNotFound=Ressource mit uuid={0} nicht gefunden.
UIValidatorBean.useAtRelObjPropertyListGrp=Die Attributgruppe wird verwendet, um die Parameter des zugehörigen Objekts zu konfigurieren, wenn die Schnittstelle in der ''{1}''-Metaklasse konfiguriert wird
UIValidatorBean.useAtSelectParent=Das Attribut "{3}" wird verwendet, um die übergeordneten Auswahlparameter bei der Einrichtung der Schnittstelle in der Metaklasse "{1}" festzulegen
UIValidatorBean.useAtPropertyList=Eine Attributgruppe kann nicht gelöscht werden. Die Gruppe wird in den Einstellungen der Objektkarte ''{1}'' verwendet
UIValidatorBean.useAtRelObjPropertyListAttr=Das Attribut wird verwendet, um die Parameter des verknüpften Objekts bei der Konfiguration der Schnittstelle in der Metaklasse "{1}" zu konfigurieren
UIValidatorBean.useAtRelObjPropertyListMC=Die Metaklasse wird verwendet, um die Parameter des verknüpften Objekts zu konfigurieren, wenn die Schnittstelle in der Metaklasse ''{1}'' konfiguriert wird
UIValidatorBean.useAttrRelObjectListAttrLink=Der Inhalt kann nicht mit den angegebenen Parametern gespeichert werden. Das Attribut ''{3}'' existiert nicht in der Metaklasse ''{1}''
UIValidatorBean.validatorIsNotSet=Validator für Inhalt {0} Metaklasse {1} nicht definiert
ToolTitle.calcStatus=Prüfen
UIValidatorBean.linkToContent.attrGroupUsedAtObjectList=Die Attributgruppe ''{3}'' kann nicht gelöscht werden, da sie an der Einstellung von Menüpunkten beteiligt ist: ''{2}''
UIValidatorBean.linkToContent.attrUseAtObjectList=Das Attribut ''{3}'' kann nicht entfernt werden, da es an den Einstellungen des linken Menüpunkts beteiligt ist: ''{2}''
UIValidatorBean.useAtObjectList={0} wird verwendet, wenn die Schnittstelle von Klassen und Typen festgelegt wird: ''{1}''.
UniqueAttributeIndexUpdater.cantCreateIndexExtededMessage=Die Werte des Attributs ''{0}''({1}) der Klasse ''{2}''({3}) müssen eindeutig sein.
UnlicensedRuleValidator.hasNotUniqueActionsForUnlicensedCodes=Die Lizenzdatei enthält zwei oder mehr Aktionsblöcke für nicht lizenzierte Benutzer mit denselben Codes.
UnlicensedRuleValidator.objectsForCalcCheckSumNotFound=Achtung! Objekte mit den folgenden Codes wurden im System nicht gefunden: {0}.<br>Diese Aktionen stehen Benutzern ohne Lizenz nicht zur Verfügung.
UnlicensedRuleValidator.wrongCheckSum=Achtung! In Aktionsblöcken für nicht lizenzierte Benutzer: {0} Prüfsumme für eine oder mehrere Aktionen ist falsch.<br>Diese Aktionen sind für nicht lizenzierte Benutzer nicht verfügbar.
TimerDefinition.AddWithDuplicateCode=Ein Zeitzähler mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Code muss eindeutig sein.
UIValidatorBean.useAtObjectGraphMC=Die Metaklasse wird verwendet, um die Liste der angezeigten Typen im Netz- oder Verbindungsdiagramm ''{1}'' zu konfigurieren
UnlicensedRuleValidator.absentActionsForUnlicensedCodes=Einer der Aktionsblöcke für nicht lizenzierte Nutzer enthält keinen Code.
UIValidatorBean.applicationIsMissing=Es gibt keine Anwendung mit dem Code ''{0}'', der beim Einrichten der Schnittstelle von Klassen und Typen verwendet wird: {1}
UIValidatorBean.attrGroupUsedAtObjectList=Eine Attributgruppe kann nicht gelöscht werden. Die Gruppe wird in den Einstellungen der Objektliste ''{1}'' verwendet
UIValidatorBean.attrUseAtObjectList={0} wird verwendet, wenn die Schnittstelle von Klassen und Typen festgelegt wird: ''{1}''.
UIValidatorBean.codeIsEmpty=Das Codefeld ist leer für den Inhalt "{0}" in der Klasse "{1}"
ToolTitle.savePrsOld=Ansicht speichern
UIValidatorBean.linkToContent.useAtMenuHierarchyGrid=Die Struktur mit dem Code ''{3}'', die beim Einstellen des Menüpunkts ''{2}'' verwendet wird, existiert nicht.
WebApi.notUUID=Die Zeichenfolge {0} ist kein eindeutiger uuid-Bezeichner
advimport.validation.dataSourceFileError=Die Importklasse ''{0}'' hat keinen Dateinamen (file-name) für die Datenquelle
advimport=Synchronisierung
advimport.AdvimportApi.readParameterError=Der Wert des Parameters ''{0}'' konnte nicht abgerufen werden
advimport.engine.end=Import abgeschlossen in {0} Sek.
advimport.validation.attributeNameEmpty=Die Importklasse ''{0}'' hat keinen Attributnamen
lineBreak=[Zeilenumbruch]
AttrTemplates.jsonAttrCode=Attributcode des Typs "Werte von dynamische Felder".
ObjectActions.error.relatedEventActionDisabled=Um einen Menüpunkt zu aktivieren, muss die zugehörige Ereignisaktion aktiviert sein.
advImport.ColumnNotEmptyFilter=Fehlende erforderliche Spalte {0} für Element ''{1}''
advListExportProcess.Exist.Error=Der Export einer Liste mit diesen Einstellungen ist bereits geplant und wird nach dem Prinzip "Wer zuerst kommt, mahlt zuerst" durchgeführt
advListViewSave.validation.userCanNotEditError=Sie haben keine Rechte zum Bearbeiten der Ansicht {0}
advListViewSave.validation.userCanNotSaveViewError=Benutzer ''{0}'' hat keine Rechte zum Speichern der Gesamtansicht
ValidateDeleteSecurityGroupEvent.ManyGroupDelAndOneUsedInAdvListSettings.message=Die Gruppe ''{0}'' wird in den Einstellungen für komplexe Listen im Parameter ''Erstellen gemeinsamer Ansichten ist verfügbar'' verwendet. Wenn eine Gruppe gelöscht wird, können die Mitarbeiter in dieser Gruppe keine gemeinsamen Ansichten mehr erstellen.
WebApi.fqnIsNull=Der Objekttyp muss ausgefüllt werden
#ru.naumen.advimport.server.dispatch.SaveAdvImportConnectionActionHandler
advimport.AdvImportConfigurationExists=Eine Konfiguration mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Konfigurationscode muss eindeutig sein.
advimport.AdvImportConnectionExist=Es besteht bereits eine Verbindung mit dem Code ''{0}''.
advimport.columnDoesNotExists=Der Wert für die Spalte ''{0}'' wurde in der Datenquelle nicht gefunden.
advimport.configEmpty=Importkonfiguration muss ausgefüllt werden
advimport.configNotValid=Die XML-Darstellung der Konfiguration ist falsch.\n {0}
advimport.configNotValidUnableToSave=Die Konfiguration kann nicht gespeichert werden. Die XML-Darstellung der Konfiguration ist ungültig.\n {0}
advimport.classEngine.stopNameSpaceSearching=Namespace-Suche erfolgreich abgeschlossen.
#ru.naumen.core.server.script.api.WebApi
WebApi.employeeIsArchived=Dieser Mitarbeiter wurde archiviert
WebApi.employeeLoginIsNull=Mitarbeiter-Login ist null
WebApi.employeeNotFound=Dieser Mitarbeiter wurde nicht gefunden
WebApi.methodIsDisabled=Methode in der Konfigurationsdatei deaktiviert
WebApi.objectIsNotSuperuserOrEmployee=Das übertragene Objekt ist kein Mitarbeiter oder Super-User
WebApi.superuserIsLinkedToAccount=Das Superuser-Konto darf nicht mit einem Benutzerkonto verbunden sein
advimport.AdvImportConnectionNotExistOrOtherType=Die Verbindung mit dem Code ''{0}'' existiert nicht oder ist von einem anderen Typ.
advimport.validation.noClasses=In der Konfiguration ist keine Importklasse (class) definiert
advimport.validation.noDataSource=Für die Importklasse ''{0}'' ist keine Datenquelle (data-source) angegeben
advimport.validation.noMetaClassResolver=Die Importklasse ''{0}'' hat keine Objekttyp-Definitionsregel (constant-metaclass-resolver или by-column-metaclass-resolver)
advimport.validation.noMode=Für die Importklasse ''{0}'' ist kein Importmodus (mode) angegeben
advimport.validation.objectConverterMetaClassError=Die Importklasse ''{0}'' für die einfache Wert-zu-Objekt-Regel (object-converter) gibt keinen Objekttyp (metaclass) an
advimport.validation.scriptConverterError=Die Importklasse ''{0}'' für eine beliebige Wertkonvertierungsregel (script-converter) gibt kein Skript an
advimport.engine.start=Beginn der Importe
advimport.titleEmpty=Der Name der Konfiguration muss ausgefüllt werden
advimport.validation.columnNotEmptyFilterError=Die Importklasse "{0}" gibt fälschlicherweise die zu filternde Spalte im Filter an (column-not-empty-filter)
ObjectActions.error.emptyEventAction=Um einen Menüpunkt zu aktivieren, füllen Sie das Feld "Aktion" in den Einstellungen des Menüpunkts aus.
advimport.validation.attributeNotExistsError=Die Klasse/der Typ mit dem Bezeichner ''{1}'' hat ein Attribut mit einem nicht existierenden Code: ''{2}''
absoluteRoles=Absolute Rollen
advimport.validation.dataSourceColumnNameError=In der Importklasse ''{0}'' für die Datenquelle ist kein Spaltenname angegeben
advimport.validation.scriptFilterError=Importklasse "{0}" für einen beliebigen Filter (script-filter) gibt kein Skript an
addExclusionValidationFailed=Ausnahmezeitraum {0} kann nicht zu Klassen {1} hinzugefügt werden. Es wurde ein nicht eindeutiger Datumswert oder ein ungültiger Start-/Endwert angegeben.
adminLiteValueMapItem=Korrespondenztabelle ''{0}''
advimport.csv.noFileProvided=Es wurde keine Datei für den Import ausgewählt
VersionContainer=Informationen über die Version
ValidateDeleteSecurityGroupEvent.ManyGroupUsedInAdvListSettings.message=Die ''{0}''-Gruppen werden in den Einstellungen für komplexe Listen im Parameter ''Erstellen gemeinsamer Ansichten ist verfügbar'' verwendet. Wenn Gruppen gelöscht werden, können die Mitarbeiter in den Gruppen keine gemeinsamen Ansichten erstellen.
ValidateDeleteSecurityGroupEvent.OneGroupUsedInAdvListSettings.message=Eine Gruppe wird in den Einstellungen der komplexen Liste in der Option "Erstellen gemeinsamer Ansichten ist verfügbar" verwendet. Wenn eine Gruppe gelöscht wird, können die Mitarbeiter in dieser Gruppe keine gemeinsamen Ansichten mehr erstellen.
ValidateMailEvent.failure=Es ist ein Mail-Validierungsfehler aufgetreten: {0}
#ru.naumen.core.server.catalog.valuemap.ValueMapHelper
ValueMapRow.singleRowNotFound=Es konnte keine einzelne Zeichenkette anhand der angegebenen definierenden Attribute gefunden werden (gefunden: {0}).
advimport.AdvImportHelper.notRunning=Import läuft nicht.
addingLinkFormCaption=Name des Formulars zum Hinzufügen eines Links
agreement=Vereinbarung
#ru.naumen.core.server.upload.spi.UploadServiceImpl;
UploadServiceImpl.errorNoUUIDTransferred=Die Datei wurde nicht geladen. Mögliche Ursachen: Die Dateigröße ist größer als zulässig ({0} MB), das Dateisystem des Servers ist nicht beschreibbar oder die Datei ist bösartig.
advimport.classEngine.stop=Importvorgang ''{0}'' gestoppt, {1} sec. Verarbeitete Anschläge: {2}. Eingeführt: {3}. Geändert: {4}. Verfehlt: {5}. Es sind Fehler aufgetreten: {6}.
advimport.configNotValidXSD=Die Konfiguration kann nicht gespeichert werden. Die XML-Darstellung der Konfiguration ist falsch (Zeile: {0}, Zeichen: {1}).
advimport.RemoveCustomizer.noRootProvided=Das zu archivierende Stammobjekt ist nicht angegeben
advimport.classEngine.end=Import ''{0}'' abgeschlossen in {1} sec. Verarbeitete Strings: {2}. Eingeführt: {3}. Geändert: {4}. Verfehlt: {5}. Es sind Fehler aufgetreten: {6}.
advimport.classEngine.start=Beginn des Imports von Objekten ''{0}''
advimport.skipEnabled=Importmodus ''{0}''. Das Erstellen und Ändern von Objekten ist deaktiviert.
advimport.validation.csvDataSourceDelimiterError=Die Importklasse ''{0}'' hat ein ungültiges Begrenzungszeichen (delimiter) für die Datenquelle: "{1}". Das Begrenzungszeichen muss ein einzelnes Zeichen sein
advimport.AdvImportHelper.notExist=Es gibt keine Importkonfiguration mit dieser ID.
advimport.AdvImportHelper.stopError=Der Importvorgang ''{0}'' kann nicht gestoppt werden. {1}
advimport.AdvimportApi.noParameter=Parameter sind nicht ausgefüllt: {0}
advimport.AdvimportApi.wrongParameter=Ungültiger Parameterwert: {0}
WebApi.wrongAttrSearchSettings=Das Attribut {0} ist nicht für die Suche konfiguriert oder das Attribut wird nicht gefunden. Wenden Sie sich an Ihren Systemadministrator.
advimport.validation.scriptMetaClassResolver=Die Importklasse ''{0}'' verfügt nicht über ein Skript für die Objekttyp-Definitionsregel (script-metaclass-resolver)
advimport.validation.threadNumber=Die Anzahl der Threads (threads-number) in der Konfiguration ist falsch: {0}. Die Anzahl der Threads sollte eine positive ganze Zahl sein
advimport.validation.timerCustomizerColumnError=Die Importklasse ''{0}'' für die Anpassung des direkten Zeitzählers (timer-customizer) hat eine falsche Spalte (column): ''{1}''
advimport.validation.wrongConnectionTimeout=Die Konfiguration kann nicht gespeichert werden. Der Wert des Parameters connection-timeout-min ist falsch.
advimport.validation.dateTimeConverterFormatError=Die Importklasse ''{0}'' für die Regel zur Konvertierung von Werten in den Typ ''Date/Time'' (datetime-converter) gibt das Format (format) nicht an
advimport.validation.hierarchicalFilterIdColumnError=Die Importklasse "{0}" hat eine falsche Spalte (id-column) für Objekte
advimport.validation.noObjectSearcher=Die Importklasse ''{0}'' hat keine Objektsuchregel (object-searcher или complex-object-searcher)
advimport.validation.objectConverterAttributeError=Suchattribut (attr) nicht angegeben in Importklasse ''{0}'' für einfache Wert-zu-Objekt-Regel (object-converter)
advimport.xml.uselessHierarchicalFilter=Der Tag <hierarchical-filter> kann nicht verwendet werden, wenn Daten objektweise importiert werden.
advimportConfigHasErrors=Die Konfiguration ''{0}'' kann nicht gestartet werden. Die Konfiguration enthält Fehler.
#metainfo export
allAttributes=Liste der Attribute
advimport.validation.byColumnMetaClassResolverColumnError=Die Importklasse ''{0}'' für die Objekttypregel (by-column-metaclass-resolver) gibt keine Quellspalte an
advimport.validation.byColumnMetaClassResolverMetaClassError=Die Importklasse ''{0}'' für die Objekttypregel by-column-metaclass-resolver gibt keine Objektklasse (Metaklasse) an
advimport.validation.backTimerCustomizerDeadLineColumnError=Die Importklasse ''{0}'' für den Backtimer-Customizer hat eine ungültige Deadline-Spalte: ''{1}''
advimport.validation.classThreadNumber=Die Importklasse ''{0}'' hat eine falsche Anzahl von Threads: {1}. Die Anzahl der Threads sollte eine positive ganze Zahl sein
advimport.validation.columnMetaClassResolverColumnError=Die Importklasse ''{0}'' für die Objekttypregel (column-metaclass-resolver) gibt keine Quellspalte an
advimport.validation.complexConverterEmpty=Für die Importklasse ''{0}'' sind keine verschachtelten Regeln für die komplexe Wert-zu-Objekt-Regel (complex-object-converter) definiert.
advimport.validation.constantMetaClassResolverColumnError=Die Importklasse ''{0}'' für die Objekttypregel (constant-metaclass-resolver) gibt keinen Objekttyp (Metaklasse) an
advimport.validation.csvDataSourceTextDelimiterError=Die Importklasse ''{0}'' hat ein ungültiges Begrenzungszeichen (text-delimiter) für die Datenquelle: "{1}". Das Begrenzungszeichen sollte aus einem Zeichen bestehen
advimport.validation.metaClassNotExistsError=Es wird eine Klasse/ein Typ mit einem nicht existierenden Bezeichner angegeben: ''{1}''
any=Jeder
WebApi.fqnCollectionMustConsistOfFqnsWithSameClassId=Eine Sammlung von Objekttypwerten sollte Typen der gleichen Metaklasse enthalten
WebApi.fqnCollectionIsEmpty=Die Sammlung von Objekttypwerten darf nicht leer sein
WebApi.fqnCollectionMustIncludeFqnsWithCases=Eine Sammlung von Objekttypwerten muss mindestens einen Metaklassentypbezeichner enthalten
WebApi.wrongAttrValues=Referenzattributwerte müssen vom Typ ''Zeile'', ''Objekt'' oder Liste von ''Zeilen'', ''Objekten'', andere Attribute ''Nummer'', ''Logisch'', ''Datum'', ''dtInterval'', ''Hyperlink'', ''Karte von Zeichenfolgen oder Zahlen'' sein.
activeFileStorageAreNotS3Type=Die Methode ist nicht verfügbar, da der aktive Speicher nicht über S3 funktioniert.
bcp.DeleteMailOperation.cantDeleteMailInProgress=Mail ''{0}'' kann nicht gelöscht werden, da sie verarbeitet wird.
#operator/public/index.jsp
applicationLog=Anwendungsprotokolle
changeTrackingSettings=Verfolgung von Veränderungen in Echtzeit
commonSearchSettings.analyzerIsUsed=Der Analysatortyp "{0}" kann nicht gelöscht werden.\nDer Analysator-Typ wird in den Einstellungen für die Objektsuche verwendet {1}
commentsSettings=Kommentare
advimport.validation.backTimerCustomizerAllowanceColumnError=Die Importklasse ''{0}'' für den Backtimer-Customizer hat eine ungültige Zulässigkeitsspalte (allowance-column): ''{1}''
attribute.noOne=Keine
application.loading.indicator=Anwendungslast-Indikator
connection.validation.appId=Anwendungs-ID
childObjectList=Liste der eingebetteten Objekte
attributes=Attribute
authorization.errorEmployeeForIntegration=Autorisierungsfehler. Sie haben keine Berechtigung.
clusterApi.cantReload=Die Synchronisierung läuft bereits oder die automatische Synchronisierung wurde gestartet.
clusterCacheRegion.error=Beim Zurücksetzen des Caches ist ein Fehler aufgetreten.
clusterCacheRegion.success=Cache erfolgreich zurückgesetzt.
clusterCacheRegion.type.error=Zurücksetzen des Cache für den angegebenen Metainformationstyp wird nicht unterstützt.
connectTo=Verbindung zu ''{0}''
attrChain=Kommunikation mit dem aktuellen Benutzer
attributeGroup=Attribut-Gruppe
backLink=Backlink
connection.validation.authentication.error.flag=SMTP-Authentifizierung ist erforderlich. Aktivieren Sie das Kontrollkästchen "SMTP-Authentifizierung" und geben Sie den richtigen Benutzernamen und das richtige Passwort ein.
connection.validation.invalid.folders=Die folgenden Ordner fehlen im Briefkasten: {0}. Der Stammordner heißt "INBOX". Ordner werden durch "," oder ";" voneinander getrennt. Ein "/" oder "\\" wird als Pfadseparator in einem Ordnernamen verwendet.
connection.validation.invalid.folders.ews=Es liegt ein Fehler in der Mailbox vor oder die folgenden Ordner fehlen zum Scannen: {0}. Der Stammordner heißt "INBOX". Ordner werden durch "," oder ";" voneinander getrennt. Ein "/" oder "\\\\" wird als Pfadseparator in einem Ordnernamen verwendet.
connection.validation.invalidCorrectMailFolder=Der Ordner für Briefe ohne Fehler {0} existiert nicht in der Mailbox. Ein "/" oder "\\" wird als Pfadseparator im Ordnernamen verwendet.
associationValidationOperation.cantChangeAttrValue=Attributwert kann nicht geändert werden {0}
#ru.naumen.core.server.bo.bop.AssociationValidationOperation
associationValidationOperation.clientNotSet=Nicht ausgefüllte obligatorische Attribute: Gegenpartei
api.mq.startFatalError.jms=Bei der Initialisierung von JMS-Integrationen ist ein Fehler aufgetreten und die Integrationen wurden nicht gestartet.
api.mq.startFatalError.kafka=Bei der Initialisierung der Integrationen mit Kafka ist ein Fehler aufgetreten, die Integrationen konnten nicht gestartet werden.
associationValidationOperation.badContract=Kunde ''{0}'' ist nicht an den Vereinbarung ''{1}'' gebunden
attributeType.boLinksType=Satz von Links auf Geschäftsobjekte
attributeType.catalogItemSetType=Satz von Verzeichniseinträgen
body=Körper
cannotLoadExclusionsFileWrongFormat=Es ist ein Fehler aufgetreten. Die Einstellungen in der Datei können nicht geladen werden. Das Dateiformat ist nicht korrekt.
changeMainAssociation=Ändern der Bindung eines Tickets
changeStatusNotAllowedWf=Der Übergang ist nach den Einstellungen des Workflow nicht zulässig.
connection.validation.clientId=Katalog-ID
filterRestrictionStrategy.DEFAULT=Ohne Grenzen
#Исправления опечаток(превод на другие языки не требуется)
commentView=Kommentare anzeigen
attrUsedInTimerDefinitions=Das Attribut wird in der Zählereinstellung verwendet: {0}
#ru.naumen.core.server.catalog.valuemap.AttributeDeleteListener
attrUsedInValueMapCatalogItems=Das Attribut wird in den Tabellen der Verzeichniseinträge ''{0}'' verwendet: {1}
commonSearchSettings.indexedFormats=Typen von Dokumenten, deren Inhalt durchsuchbar sein wird
connection.validation.authentication.error=Authentifizierungsfehler. Überprüfen Sie die Felder {0}.
configNotValid=Konfigurationsfehler {0}. Die XML-Darstellung der Konfiguration ist fehlerhaft.\n {1}
configNotValidXSD=Konfigurationsfehler {0}. Die XML-Darstellung der Konfiguration ist falsch (Zeichenfolge: {1}, Zeichen: {2}).
connection.validation.clientSecret=Geheimnis des Kunden
associationValidationOperation.manyClients=Mehrere Bindungsclients angegeben
attrGroupAllredyExists=Eine Gruppe von Attributen mit dem Code ''{0}'' existiert bereits in der Klasse/dem Typ ''{1}''. Der Code muss innerhalb der Art/Klasse eindeutig sein.
clusterService.sendMessageError=Während des Vorgangs ist ein Fehler aufgetreten. Versuchen Sie es später noch einmal oder wenden Sie sich an Ihren Systemadministrator.
filePreviewSettings.props.enabled=Aktiviert
eventService.eventCategory.messageSendFailed=Fehler beim Senden der Änderungsnachricht
eventService.messageSendFailed=Es wurde keine Änderungsmeldung gesendet ({0} - {1}{3}), weil {2}.{4}
importMetainfo.DeletingListTemplatesDone=Listenvorlagen entfernt: '{}'
eventService.messageGroovySyntaxError=Syntaxfehler im Feld ''{0}'' der Nachricht entdeckt
customLoginPage.mandatorryVariablesDuplicates=Im Vorlagentext darf nur ein Systemparameter aus der Liste vorhanden sein: {0}
customLoginPage.mandatorryVariablesNotFound=Es gibt keine obligatorischen Systemparameter im Vorlagentext: {0}
customLoginPage.wrongVariables=Es gibt ungültige Parameter im Text der Vorlage: {0}
design.YYDesign.HelpString=Konstruktionen der Form '{'YY'}' werden vom System durch die letzten 2 Ziffern der Jahreszahl ersetzt
connection.validation.invalidIncorrectMailFolder=Der Ordner für Mails mit Fehler {0} existiert nicht in der Mailbox. Ein "/" oder "\\" wird als Pfadseparator im Ordnernamen verwendet.
connection.validation.outgoing.protocol.error=Der Server (Port) ist kein Postausgangsserver. Überprüfen Sie die Parameter "Server" und "Port". Häufig verwendete Ports: 25, 465, 587.
errorGetStructureElement=Strukturelemente, die der Klasse entsprechen, werden nicht gefunden: {0}
eventActionType.userEvent=[Benutzerdefiniertes Ereignis]
connection.validation.silentmode.is.on=Es ist nicht möglich, eine Verbindung mit dem Server herzustellen, da der stille Modus aktiviert ist.
importMetainfo.DeletingListTemplate=['{}'/'{}'] Listenvorlage '{}' löschen
importMetainfo.DeletingMailProcessorRule=['{}'/'{}'] Mail-Verarbeitungsregel '{}' löschen
importMetainfo.DeletingMailProcessorRulesDone=Mail-Verarbeitungsregeln entfernt: '{}'
importMetainfo.DeletingMetaClass=['{}'/'{}'] Entfernung der Metaklasse '{}'
eventService.messageGroovyTemplateSyntaxError=Syntaxfehler in der mit der Nachricht verbundenen Vorlage gefunden
doubleValueMapRulesBySourceAttributeSet=Die Korrespondenztabelle enthält mehrere Regeln, die demselben Satz von definierenden Attributen entsprechen
errorStructureNotFound=Keine Struktur gefunden passender Code: {0}
filePreviewSettings.props.docxActivePreviewLimit=Maximale Anzahl von Dateien im ".docx"-Format, die gleichzeitig im Vorschaumodus geöffnet werden können
filePreviewSettings.props.otherFormatsMaxFileSize=Maximale Größe für alle anderen für die Vorschau verfügbaren Formate (in MB)
imageNotFound=Bild nicht gefunden
employee=Mitarbeiter
connection.validation.login=Login
eventActionType.ndapMessage=NDAP-Nachricht
eventActionType.onsetTimeOfAttr=Der Beginn der Attributzeit
filePreviewSettings.props.imageActivePreviewLimit=Maximale Anzahl von Bildern, die gleichzeitig im Vorschaumodus geöffnet werden können
filePreviewSettings.props.imageMaxFileSize=Maximale Größe der für die Vorschau verfügbaren Bilder (in MB)
csrf.chatLicenseRestriction=Der Zugang ist eingeschränkt. Die Lizenzdatei enthält nicht das Chat-Modul.
customLoginPage.errorDurringApplyCustomLoginForm=Fehler bei der Anwendung der Benutzeranmeldeseite. Ursache: {0}
customLoginPage.errorDurringApplyDefaultLoginForm=Fehler bei der Anwendung der Systemanmeldeseite. Ursache: {0}
customLoginPage.errorDurringApplyTestLoginForm=Fehler bei der Anwendung der Login-Testseite. Ursache: {0}
fileStorage.moving=Umzug
hasReferencedEscalationSchemes={1} werden von Eskalationsschemata referenziert: {0}
filePreviewSettings.props.fileTypesForNewWindow=Dateitypen, die in einem separaten Tab mit Browser-Tools angezeigt werden
employeeNotFoundFailure=Sie können sich nicht einloggen. Konto nicht gefunden, wenden Sie sich an Ihren Administrator.
copyForType=Kopie für Typ ''{0}'' _{1}
eventActionType.add=Hinzufügen eines Objekts
filePreviewSettings.props.fileTypes=Für die Vorschau verfügbare Dateitypen
fileStorage.storageInUse=Dateien nicht abgeschlossen sind: Speicher ''{0}'' ist gerade dabei, Dateien zu verschieben oder zu komprimieren. Bitte versuchen Sie die Aktion später.
email=E-Mail:{0}
importMetainfo.DeletingEventActionsDone=Ereignisaktion gelöscht: '{}'
importMetainfo.DeletingEventMetaClass=Entfernen Event for von Metaklasse '{}'
importMetainfo.DeletingFastLinkSetting=['{}'/'{}'] Entfernt die '{}'-Erwähnungseinstellung
importMetainfo.DeletingFastLinkSettingsDone=Erwähnungseinstellungen entfernt: '{}'
importMetainfo.DeletingFolderCatalog=Löschung eines Katalogs '{}' für die Metaklasse
fileStorage.error=Fehler bei der Dateispeicherung
get.log.files.error=Kein Zugriff auf das Anwendungsprotokollverzeichnis. Überprüfen Sie die Zugriffsrechte des Benutzers, in dessen Namen die Anwendung ausgeführt wird.
importMetainfo.DeletingQueue=['{}'/'{}'] Warteschlange '{}' löschen
importMetainfo.DeletingQueuesDone=Warteschlangen gelöscht: '{}'
hasReferencedValueMapCatalogItems={2} wird von Verzeichniselemente ''{0}'' referenziert: {1}
hasReferencedWfProfiles={1} werden von Profilen von verbundenen Workflows referenziert: {0}
helpNotFound=Es ist keine Hilfe verfügbar. Wenden Sie sich an Ihren Systemadministrator.
currentUser=aktueller Benutzer
eventService.eventCategory.switchToPlanningMode=Übergang in den Planungsmodus
empty=leer
eventActionType.arriveMessageOnQueue=Eintrag einer Nachricht in die Warteschlange
eventActionType.delete=Löschen eines Objekts
eventActionType.insertMention=Erwähnung innerhalb der ausgewählten Objekte
eventActionType.mail=Email erhalten
eventService.attach_file_to_attr=Das Objekt ''{0}'' wurde geändert: {1}: Datei ''{2}'' hinzugefügt
hierarchyGrid=Hierarchischer Baum
errorMoreOneStructureElement=Es wurde mehr als ein Strukturelement für die Klasse gefunden: {0}. Die Suche nach Objekten wird nicht durchgeführt
errorStructureItemNotFound=Es wurde kein Strukturelement gefunden, das dem Code entspricht: {0}
eventService.eventCategory.switchToMainMode=Übergang in den Basismodus
filePreviewSettings.props.maxFileSize=Maximal verfügbare Dateigröße für die Vorschau (in MB)
filePreviewSettings.props.otherFormatsActivePreviewLimit=Maximale Anzahl von Dateien in jedem anderen Format, die gleichzeitig im Vorschaumodus geöffnet werden können
filePreviewSettings.props.pdfActivePreviewLimit=Maximale Anzahl von Dateien im ".pdf"-Format, die gleichzeitig im Vorschaumodus geöffnet werden können
filePreviewSettings.props.pdfMaxFileSize=Maximale Größe der für die Vorschau verfügbaren ".pdf"-Dateien (in MB)
fileStorage.fileUtils.base64.fileSizeExceedLimit=Die Dateigröße überschreitet die vom Systemadministrator festgelegte Grenze: {0} Kb.
filePreviewSettings.props.xlsxActivePreviewLimit=Maximale Anzahl von Dateien im ".xlsx"-Format, die gleichzeitig im Vorschaumodus geöffnet werden können
filePreviewSettings.props.xlsxMaxFileSize=Maximale Größe der für die Vorschau verfügbaren ".xlsx"-Dateien (in MB)
fileStorage.deduplicationActivatedError=Der Dateiaustausch ist noch nicht abgeschlossen. Der Parameter ''deduplication'' ist in der aktuellen Konfiguration der Dateiablage aktiv.
fileStorage.fileBlockDownloading=Das Herunterladen von "{0}" ist nicht erlaubt. Die Datei ist nur im Anzeigemodus verfügbar.
fileStorage.fileUtils.base64.fileNotFound=Die angeforderte Datei wurde nicht gefunden oder der Dateispeicher ist nicht verfügbar.
fileStorage.notExists=Dateispeicher ''{0}'' existiert nicht
fileUuidCanNotBeEmpty=UUID muss angegeben werden.
filterRestrictionStrategy.LIST=Beschränkung des Inhalts der Liste
filterRestrictionStrategy.SCRIPT=Beschränkung durch Skript
external.employeeNotFound=Ein Mitarbeiter mit den angegebenen Attributen wird nicht gefunden. Einstellungen des Authentifizierungsanbieters prüfen
external.esia.chooseSubjectPage.loginAs=Anmelden als
external.esia.subjects.physicalPerson=Privatperson
external.esia.subjects.organization=Organisation
external.moreThenOneEmployee=Es wurde mehr als ein Mitarbeiter mit diesen Attributen gefunden. Prüfen Sie die Einstellungen des Authentifizierungsanbieters oder die Konfiguration des externen Authentifikators
external.wrongProtocolCount=Die Datei {0} kann entweder für OIDC oder SAML2 konfiguriert werden. Sie müssen die Konfigurationsdatei überprüfen, um festzustellen, ob nur ein Protokoll verfügbar ist.
eventActionType.loginSuccessful=Einloggen
eventService.switchToPlanningMode=Benutzer ''{0}'' hat den Änderungsplanungsmodus {1}
fileStorage.compressing=Kompression
fts.parseError=Es gibt kein Ergebnis für diese Anfrage. Die Suchanfrage ist nicht korrekt
connection.validation.sharedMailbox=Gemeinsame Mailbox
connection.validation.ssl=SSL-Verbindung
connection.validation.unknown.host=Die Verbindung zum Server ist nicht möglich. Überprüfen Sie die Parameter "Server", "Port" und "SSL-Verbindung". Häufig verwendete Ports: 25, 465, 587.
connection.validation.unknown.url=Die Verbindung zum Server ist nicht möglich. Überprüfen Sie den Wert für den Parameter "Server".
connection.validation.wrong.encryption.protocol=Der Server (Port) unterstützt das angegebene Verschlüsselungsprotokoll nicht. Überprüfen Sie den Parameter "Verschlüsselungsprotokoll".
connection.validation.outgoing.unknown.host=Die Verbindung zum Server ist nicht möglich. Überprüfen Sie die Parameter "Server", "Port" und "Verschlüsselungsprotokoll". Häufig verwendete Ports: 25, 465, 587.
connection.validation.protocol.error=Der Server (Port) unterstützt das angegebene Protokoll nicht (es handelt sich nicht um einen Posteingangsserver). Überprüfen Sie die Parameter "Server", "Port" und "Protokoll". Häufig verwendete Ports: 143 (IMAP4), 993 (IMAP4, SSL), 110 (POP3), 995 (POP3, SSL).
connection.validation.wrong.encryption.skip.verification=Das Zertifikat hat die Prüfung nicht bestanden.
connection.validation.wrong.encryption.ssl=Der Server (Port) unterstützt keine SSL-Verbindung. Deaktivieren Sie das Kontrollkästchen "SSL-Verbindung".
eventActionType.alertActivated=Aktivierung des Alarms
eventActionType.alertChanged=Alarmaktivität ändern
eventActionType.alertDeactivated=Deaktivierung des Alarms
eventActionType.bindMassSlave=Kommunikation mit untergeordneten Objekten
eventActionType.bindToMassMaster=Verknüpfung mit einer Masseneinrichtung
eventActionType.changeMassAttr=Wechsel des Vorzeichens des Massencharakters
eventActionType.escalation=Eskalation
eventAction=Ereignisaktion
delConfigurationActionHandler.error=Die Konfiguration "{0}" mit dem Code "{1}" kann aus den folgenden Gründen nicht gelöscht werden:\n - Die Konfiguration wird in den Einstellungen der Scheduler-Tasks verwendet: {2}.
eventService.switchToMainMode=Benutzer ''{0}'' ist in den Hauptmodus eingetreten
favicon.validation.png=Ungültige PNG-Bildgröße. Wählen Sie ein PNG-Bild mit einer Größe von {0}x{0} oder {1}x{1} Pixel aus.
file.upload.error=Datei kann nicht hochgeladen werden: {0}
file.icon.size.error=Die Höhe des hochgeladenen Bildes sollte 16px nicht überschreiten.
fileFromRTFCanNotBeCopy=RTF-Bilder dürfen nicht kopiert werden.
filePreviewSettings=Dateivorschau
filePreviewSettings.props.docxMaxFileSize=Maximale Größe der für die Vorschau verfügbaren Dateien im ".docx"-Format (in MB)
favicon.validation.format=Ungültiges Dateiformat. Wählen Sie eine Datei in einem der folgenden Formate: {0}.
favicon.validation.image=Ungültiges binäres Bildformat. Wählen Sie ein anderes Bild aus.
eventActionType.openEditForm=Öffnen eines Formulars zur Bearbeitung
contacts=Kontaktpersonen
importMetainfo.DeletingCatalog=['{}'/'{}'] Verzeichnis '{}' löschen
importMetainfo.DeletingCatalogItem=['{}'/'{}'] Element '{}' aus dem Verzeichnis '{}' löschen
importMetainfo.DeletingCatalogItemsDone=Elemente des Verzeichnisses '{}' gelöscht: '{}'
importMetainfo.DeletingCatalogsDone=Entfernte Verzeichnisse: '{}'
importMetainfo.DeletingContentTemplate=['{}'/'{}'] Inhaltsvorlage '{}' entfernen
importMetainfo.DeletingContentTemplatesDone=Inhaltsvorlagen entfernt: '{}'
importMetainfo.DeletingCustomForm=['{}'/'{}'] Benutzerdefiniertes Formular '{}' löschen
importMetainfo.DeletingCustomFormsDone=Benutzerformulare entfernt: '{}'
importMetainfo.DeletingCustomJs=['{}'/'{}'] Anpassungsdatei '{}' löschen
importMetainfo.DeletingCustomJssDone=Anpassungsdateien entfernt: '{}'
importMetainfo.DeletingEmbeddedApplication=['{}'/'{}'] Entfernung der integrierten Anwendung '{}'
importMetainfo.DeletingEmbeddedApplicationsDone=Integrierte Anwendungen entfernt: '{}'
importMetainfo.DeletingEventAction=['{}'/'{}'] Ereignisaktion '{}' löschen
importMetainfo.DeletingAdvImportConfig=['{}'/'{}'] Synchronisationskonfiguration löschen '{}'
importMetainfo.DeletingAdvImportConfigsDone=Synchronisationskonfigurationen entfernt: '{}'
importMetainfo.DeletingSecurityProfile=['{}'/'{}'] Profil löschen '{}'
importMetainfo.DeletingSecurityGroup=['{}'/'{}'] Benutzergruppe '{}' löschen
importMetainfo.error.loadingEventStorageRule=Beim Laden der Regel ''{0}'' ({1}) ist ein Fehler aufgetreten: {2}
importMetainfo.loadingPlannedVersionsSettings=Start des Ladens der geplanten Versionierungseinstellungen
importMetainfo.loadingUserJMSQueue=['{}'/'{}'] Benutzerwarteschlange laden '''{}':'{}'''
importMetainfo.restoreStateUserEvent=Benutzerereignisaktivierung wiederherstellen: '{}'
licenseSeviceImpl.incorrectMaxSessionsValueUnlic=Der Wert von "maxSessionsForUnlicensedUsers" hat ein unkorrektes Format.
licenseServiceImpl.licenseVerificationFailedLogin=Die Lizenzdatei ist nicht korrekt.
metainfo.DelAttributeActionHandler.attrUsedAtRelationSchemaContent=Das Attribut wird im Inhalt verwendet: {0}
jmsQueue.notFound=Es wurde keine Warteschlange gefunden.
jmsQueue.system.title.Queue.EventAction.WebSocketMessages=Systemwarteschlange für WebSocket-Nachrichten
jmsQueue.system.description.Queue.bridgeNdapAlertIn=Die Ereigniswarteschlange verarbeitet Ereignisaktionen, für die ein NDAP-Ereignis ausgewählt wurde - "Alarmaktivierung", "Alarmdeaktivierung", "NDAP-Nachricht".
importMetainfo.DeletingRole=['{}'/'{}'] Entfernen der Rolle '{}'
jmsQueue.system.action.title.IntegrationEventAction=Senden an eine externe Warteschlange
licenseSeviceImpl.absentCreationDate=Erstellungsdatum der Datei nicht angegeben.
importMetainfo.parameterDoesNotExists=Der Parameter "'{}'" des Objektaktionsmenüs, der in '{}' angegeben ist, ist in der geladenen Datei nicht definiert. Der Wert des Parameters ist standardmäßig auf "LEFT" eingestellt
importMetainfo.parameterHasUnSupportedValue=Der Parameter "'{}'" des Objektaktionsmenüs, der in '{}' angegeben ist, hat einen nicht unterstützten Wert: "'{}'". Der Wert des Parameters ist standardmäßig auf "LEFT" eingestellt
jmsQueue.system.action.title.PushPortalEventAction=Benachrichtigung an das Portal
jmsQueue.system.description.Queue.UserEventAction=Die Warteschlange verarbeitet Ereignisaktionen, für die das Ereignis "Benutzerdefiniertes Ereignis" ausgewählt wurde.
keystore.notfound=Es wurde kein Zertifikat gefunden.
licenseSeviceImpl.incorrectMaxSessionsValueBoth=Der Wert des Parameters "maxSessions" hat in den folgenden Lizenzgruppen ein inkorrektes Format: {0}; für nicht lizenzierte Nutzer. Der Wert des Parameters "maxSessionsForUnlicensedUsers" hat ein inkorrektes Format.
licenseSeviceImpl.incorrectPermissionsSetForUnlicensedUsersValue=Die Datei enthält einen falschen Code für Zugriffsrechte für nicht lizenzierte Benutzer: {0} .
metainfo.AddMetaClassActionHandler.parent=Elternteil
listTemplate.dynamicApplyError=Display-Parameter der Liste sind nicht korrekt (die Listenansicht stimmt nicht überein). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
importMetainfo.loadingEmbeddedApp=['{}'/'{}'] Laden der integrierten Anwendung '''{}':'{}'''
importMetainfo.loadingEventAction=['{}'/'{}'] Ereignisaktion '''{}':'{}''' laden
importMetainfo.loadingCommentsSettingsStart=Beginn des Ladens der Kommentareinstellungen
importMetainfo.loadingCommonSearchSettingsStart=Start des Ladens der allgemeinen Sucheinstellungen
importMetainfo.loadingContentTemplate=['{}'/'{}'] Einstellungen der Inhaltsvorlage laden: '{}'
importMetainfo.loadingContentTemplatesDone=Einstellungen der Inhaltsvorlage geladen: '{}'
metainfo.MetainfoServiceBean.catalogExists=Ein Verzeichnis mit dem Code ''{0}'' existiert bereits.
importMetainfo.type.withoutAdminLite=Keine Einstellungen verfügbar im Administrator-Lichtmodus
importMetainfo.warn.AdminLiteNotAvalible=Vollständige Meta-Informationen hochgeladen, da die Lightweight-Konfigurationsschnittstelle ausgeschaltet ist oder kein Admin-Lite-Modul in der Lizenzdatei vorhanden ist.
importMetainfo.warn.eventStorageRuleClassNotExists=Klasse/Typ ''{0}'' wurde nicht gefunden
importMetainfo.warn.eventStorageRuleStateNotExists=Status ''{1}'' wurde in Klasse/Typ ''{0}'' nicht gefunden
metainfo.CopyMetaClassActionHandler.roles=Rollen {0}
metainfo.CopyMetaClassActionHandler.timerDefinitions=Zeitzähler {0}
metainfo.DelAttributeActionHandler.aggregateAttrError="Das Attribut ''{0}'' kann nicht entfernt werden. Das Attribut ''{0}'' ist ein Aggregat zum Attribut ''{1}''.
importMetainfo.DeletingReportTemplate=['{}'/'{}'] Berichtsvorlage '{}' löschen
importMetainfo.loadingEmbeddedAppContent=['{}'/'{}'] Laden des Inhalts der integrierten Anwendung
importMetainfo.DeletingReportTemplatesDone=Berichtsvorlagen entfernt: '{}'
importMetainfo.DeletingRolessDone=Entfernte Rollen: '{}'
importMetainfo.DeletingSchedulerTask=['{}'/'{}'] Scheduler-Aufgabe '{}' löschen
importMetainfo.DeletingSchemeEscalation=['{}'/'{}'] Entfernen des Eskalationsschemas '{}'
importMetainfo.DeletingSchemeEscalationsDone=Eskalationsschemata gelöscht: '{}'
importMetainfo.DeletingSecurityDomain=['{}'/'{}'] Berechtigungseinstellungen entfernen (security-domain tag) '{}'
importMetainfo.loadingImportConfiguration=Importkonfiguration laden '''{}':'{}'''
importMetainfo.loadingPlannedVersionsSettingsDone=Einstellungen für die geplante Versionierung wurden geladen
importMetainfo.loadingProfile=['{}'/'{}'] Profil herunterladen '''{}':'{}'''
importMetainfo.warn.attrUsedInBrowserTabSettings=Beim Laden von Metainformationen wurde der Wert des Parameters "Überschreibe Browser-Tab-Titel für Objektkarte" für die Klasse ''{0}'' auf "false" geändert, da das Attribut mit dem Code ''{1}'' nicht gefunden wurde .
importMetainfo.warn.immutableTagsCantBeChanged=Tags {0} sind lizenziert und können nicht geändert werden
jmsQueue.system.action.title.ChangeTrackingEventAction=Verfolgung von Änderungen
jmsQueue.system.action.title.PushEventAction=Benachrichtigung in der Schnittstelle
licenseSeviceImpl.licenseSignVerificationFailed=Die Lizenzdatei kann nicht heruntergeladen werden. Das Dateiformat ist nicht korrekt: ein Fehler bei der Signaturprüfung.
listAttrChain.incorrectAttr=Die Listenparameter sind nicht korrekt (die angegebene Objektklasse und die Objektklasse im Beziehungsattribut stimmen nicht überein). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
maintenanceMode=Sperrung des Zugangs für die Dauer der Wartungsarbeiten
importMetainfo.DeletingSchedulerTasksDone=Scheduler-Aufgaben gelöscht: '{}'
importMetainfo.DeletingSecGroupsDone=Benutzergruppeneinstellungen entfernt: '{}'
importMetainfo.parameterMenuItemActionDoesNotExists=Der ''{}''-Parameter des ''{}''-Elements des Aktionsmenüs des Objekts, der in '{}' angegeben ist, referenziert auf die Aktion, die im System nicht vorhanden ist: uuid = "'{}'". Der Standardwert ist auf "[nicht definiert]" gesetzt.
importMetainfo.warn.attrGroupUsedInFastLinkSettings=Die Attributgruppe {0} wurde beim Laden der Metainformationen gelöscht, wurde aber in der in Erwähnungseinstellungen {1} verwendet; die Erwähnung muss bearbeitet oder gelöscht werden.
jmsQueue.system.description.Queue.EventAction.WebSocketMessages=Die Warteschlange verarbeitet Aktionen zum Senden von Nachrichten an den WebSocket-Kanal.
importMetainfo.loadingCommentsSettingsDone=Kommentar-Einstellungen wurden hochgeladen
importMetainfo.warn.attrUsedInEventActions=Attribut {0} wurde beim Laden von Meta-Informationen entfernt, aber in der Aktionseinstellung für das Ereignis {1} verwendet wird, muss die Aktion bearbeitet oder gelöscht werden.
importMetainfo.warn.attrUsedInFastLinkSettings=Das Attribut {0} wurde beim Laden der Metainformationen entfernt, aber in Erwähnungseinstellungen {1} verwendet, muss die Erwähnung bearbeitet oder gelöscht werden.
metainfo.CreateCatalogActionHandler.catalogItemPrefix=Verzeichnis-Element ''{0}''
metainfo.DelStateActionHandler.cantDeleteState=Der Status ''{0}'' kann nicht gelöscht werden.
licenseSeviceImpl.incorrectUnlicMaxInactiveIntervalValue=Der Wert von "unlicensedMaxInactiveInterval" hat nicht das richtige Format.
login=Name des Benutzers
metainfo.CopyMetaClassActionHandler.timerDefinition="Zeitzähler" für Attribute: {0}
metainfo.CreateCatalogActionHandler.badCode=Falscher Verzeichniscode-Wert ''{0}''
importMetainfo.loadingOtherAdminOptionsStart=Laden anderer Verwaltungseinstellungen
importMetainfo.loadingOtherAdminOptionsDone=Herunterladen der anderen Verwaltungseinstellungen abgeschlossen
jmsQueue.system.title.Queue.EventAction.Escalations=System-Eskalationswarteschlange
jmsQueue.system.title.Queue.EventAction=System-Skript-Warteschlange
jmsQueue.system.title.Queue.EventAction.Notifications=System-Warnungswarteschlange
leftMenu.BrowsingHistoryLeftMenuItemValue.defaultTitle=Geschichte
licenseSeviceImpl.hasNotUniqueLicenseCodes=Eine Lizenzdatei enthält zwei oder mehr Gruppen von Lizenzen mit demselben Code.
licenseSeviceImpl.incorrectMaxSessionsValueGroup=Der Wert des Parameters "maxSessions" hat in den folgenden Lizenzgruppen ein ungültiges Format: {0}.
linkToBo=Link zu Geschäftsobjekt
licenseSeviceImpl.licenseVerificationFailed=Die Lizenzdatei wurde hochgeladen. Die Lizenzdatei kann nicht hochgeladen werden. Das Dateiformat ist nicht korrekt: {0}
licenseSeviceImpl.namedLicensesCountLessThanExist=Die Anzahl der benannten Lizenzen des Typs {0} in der Lizenzdatei ist geringer als die Anzahl der derzeit verwendeten Lizenzen.
listTemplate.dynamicApplyErrorClass=Die Parameter für die Listenanzeige sind falsch (die Klasse oder der Typ der Listenobjekte stimmen nicht mit der Klasse oder dem Typ der Vorlage überein). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
listTemplate.dynamicApplyErrorLinkObject=Die Parameter der Listenanzeige sind falsch (kein Beziehung-Objekt gefunden). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
listTemplate.dynamicApplyErrorTemplate=Die Parameter der Listenanzeige sind falsch (die Attributgruppe in der Liste stimmt nicht mit der Attributgruppe der Vorlage überein). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
metainfo.AddMetaClassActionHandler.errorCaseToAbstract=Der Typ ''{0}'' kann nicht hinzugefügt werden. Die Typklasse ist abstrakt.
metainfo.AddMetaClassActionHandler.errorCaseToSysObj=Serviceklassen können nicht verwendet werden.
metainfo.AddMetaClassActionHandler.errorCatalogItemCaseDisallowed=Ein Verzeichnis kann nicht hinzugefügt werden. Der Wert ''{0}'' kann nicht als Code verwendet werden
jmsQueue.system.description.Queue.External.EventAction=Die Ereigniswarteschlange verarbeitet Aktionen des Ereignistyps "Skript", bei denen das Attribut "Interaktion mit externem System" gesetzt ist.
licenseSeviceImpl.licenseUploadComplete=Die Lizenzdatei wurde hochgeladen.<br>
metainfo.AddMetaClassActionHandler.errorBadCase=Unstimmigkeit zwischen der ID des Typs und der zu erstellenden Klasse
metainfo.CreateCatalogActionHandler.emptyTitle=Der Name des Verzeichnisses ist nicht angegeben
metainfo.DelAttributeActionHandler.cantDelAttr=Das Attribut {0} kann nicht entfernt werden.
metainfo.MetainfoServiceBean.EscalationNotFound=Eskalationsschema mit Code {0} nicht gefunden oder Synchronisierung nicht abgeschlossen!
importMetainfo.importFastLinkSettings.skip=['{}'/'{}'] Objekterwähnungseinstellungen überspringen: '{}'
importMetainfo.importFastLinkSettingsDone=Objekterwähnungseinstellungen geladen: '{}'
importMetainfo.importFastLinkSettingsStart=Beginn des Ladens der Einstellungen für Erwähnungen von Objekten
importMetainfo.loadingWorkflowProfileFolder=['{}'/'{}'] Laden des Workflow-Profilordners '{}'
importModules.error=Es ist ein Fehler aufgetreten. Module können nicht geladen werden. {0}
jmsQueue.system.action.title.NotificationEventAction=Alarm
importMetainfo.loadingUserJMSQueuesStart=Start des Ladens von Benutzerwarteschlangen
licenseSeviceImpl.hasNotExistAllowedClassesCodes=Warnung! Die Datei für Lizenzgruppen {0} enthält Parameterwerte {1}, die nicht den im System vorhandenen Klassen entsprechen: {2}.
licenseSeviceImpl.containsIncorrectModules=Die Lizenzdatei kann nicht heruntergeladen werden. Die Datei enthält falsche Modulcodes: {0}.
licenseSeviceImpl.existLicensesIsAbsent=Die Lizenzgruppen sind in der heruntergeladenen Datei nicht enthalten: {0}, die den Benutzern zugewiesen sind.
listAttrChain.errorStructure=Falsches Format für Attributverknüpfungseinstellungen. Wenden Sie sich an den Administrator, um das Problem zu lösen.
listAttrChain.incorrectType=Die Listenparameter sind nicht korrekt (der angegebene Typ des Beziehungsattributs ist nicht zulässig). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
listAttrChain.dynamicApplyAttrChain=Display-Parameter der Liste sind nicht korrekt (kein Beziehungsattribut gefunden). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
metainfo.CopyMetaClassActionHandler.cantCopyClassManyToManyAttributeTableExist=Die Klasse kann nicht kopiert werden. Die Anforderung, dass die ersten acht Zeichen des Klassencodes, des Typs und des Attributtyps ''{0}'' eindeutig sein müssen, wird verletzt, da es in der kopierten Klasse ein Attribut mit dem Code ''{1}'' gibt. Ändern Sie den Klassencode.
metainfo.MetainfoServiceBean.catalogNotExists=Das Verzeichnis mit dem Code ''{0}'' existiert nicht!
importMetainfo.eventActionTemplateDoesNotExists=Der Parameter "'{}'" in der Ereignisaktion "'{}'" ('{}') referenziert auf eine Stilvorlage, die im System nicht vorhanden ist: uuid = "'{}'"
importMetainfo.eventActionJMSQueueNotCorrectActionType=Beim Laden von Metainformationen für eine Aktion mit dem Ereignis "{0}" wurde der Wert von "Aktionswarteschlange" auf "{1}" ({2}) geändert, da in der Warteschlange mit dem Code "{3}" eine andere Typ von Ereignisaktion verarbeitet wird.
importMetainfo.loadingStructuredObjectsViewsDone=Geladene Strukturen: '{}'
importMetainfo.loadingStructuredObjectsViewsStart=Beginn des Ladens von Strukturen
metainfo.CopyMetaClassActionHandler.cantCopyHardcoded=Nur benutzerdefinierte Metaklassen können kopiert werden
metainfo.FixEmptyFieldsInSearchSettingsResult=Leere Felder in den Sucheinstellungen sind erfolgreich ersetzt worden.
importMetainfo.DeletingStructureObjectsView=['{}'/'{}'] Entfernung der Struktur '{}'
importMetainfo.DeletingStructureObjectsViewsDone=Entfernte Strukturen: '{}'
importMetainfo.DeletingStyleTemplate=['{}'/'{}'] Entfernen der Stilvorlage '{}'
importMetainfo.DeletingStyleTemplatesDone=Stilvorlagen entfernt: '{}'
importMetainfo.DeletingTag=['{}'/'{}'] Tag '{}' löschen
importMetainfo.DeletingTagsDone=Entfernte Tags: '{}'
importMetainfo.DeletingTimeDefinition=['{}'/'{}'] Zeitzähler '{}' löschen
importMetainfo.DeletingTimeDefinitionsDone=Zeitzähler gelöscht: '{}'
importMetainfo.DeletingUserAction=['{}'/'{}'] Benutzerereignis '{}' löschen
importMetainfo.DeletingScript=['{}'/'{}'] Skript löschen '{}'
importMetainfo.DeletingScriptModule=['{}'/'{}'] Entfernung des Skriptmoduls '{}'
importMetainfo.DeletingScriptModulesDone=Skriptmodule entfernt: '{}'
importMetainfo.DeletingScriptsDone=Entfernte Skripte: '{}'
importMetainfo.DeletingSecProfilesDone=Profile gelöscht: '{}'
jmsQueue.setRoutingType.success=Der Modus wurde erfolgreich geändert.
listTemplate.removed=Die Parameter der Listenanzeige sind nicht korrekt (Vorlage nicht gefunden). Wenden Sie sich an Ihren Administrator, um das Problem zu lösen.
metainfo.AddMetaClassActionHandler.errorForbidUserClasses=Die Klasse ''{0}'' darf keine Metaklassen erstellen.
metainfo.DelAttributeActionHandler.attrUsedAtGroup=Das Attribut ist Teil der folgenden Attributgruppen: {0}
metainfo.GetMetaClassActionHandler.metaClassNotFound=Typ/Klasse nicht gefunden: fqn={0}
importMetainfo.DeletingUserEventsDone=Benutzerereignisse gelöscht: '{}'
importMetainfo.DeletingValueMapCatalogItem=['{}'/'{}'] Korrespondenztabelle '{}' löschen
importMetainfo.DeletingValueMapCatalogItemInEscalation=['{}'/'{}'] Löschen der Korrespondenztabellen für das Eskalationsschema '{}'
importMetainfo.DeletingValueMapCatalogItemsDone=Korrespondenztabellen gelöscht: '{}'
importMetainfo.DeletingValueMapCatalogItemsInEscalationDone=Korrespondenztabellen für Eskalationsschema gelöscht: '{}'
importMetainfo.error.systemQueueCannotBeDelete=In der Datei ist keine Systemwarteschlange {0} vorhanden.
importMetainfo.loadingChangeTrackingSettingsStart=Beginn des Herunterladens der Einstellungen für die Änderungsverfolgung
importMetainfo.loadingContentTemplatesStart=Beginn des Herunterladens von Inhaltsvorlagen
jmsQueue.system.description.Queue.EventAction.Escalations=Ereignisaktionen mit dem ausgewählten Ereignis "Eskalation" werden in der Warteschlange verarbeitet.
jmsQueue.system.description.Queue.EventAction.Notifications=Die Warteschlange verarbeitet Aktionen zu einem Ereignis wie "Alarm".
jmsQueue.system.description.Queue.EventAction.Pushes=Die Warteschlange verarbeitet Ereignisaktionen der Typen "Benachrichtigung in Schnittstelle", "Benachrichtigung in Portal", "Benachrichtigung in mobiler Anwendung" (falls diese Module verbunden sind).
jmsQueue.system.action.title.PushMobileEventAction=Benachrichtigung in der mobilen Anwendung
jmsQueue.system.action.title.ScriptEventAction=Skript
jmsQueue.system.description.Queue.EventAction=Die Warteschlange verarbeitet Aktionen zu einem Ereignis vom Typ Skript.
licenseSeviceImpl.hasNoCorrectCode=Der Wert des Parameters "code" hat in den folgenden Lizenzgruppen ein ungültiges Format: {0}.
jmsQueue.sendMessageToUserJMSQueue.error=Beim Senden einer Nachricht an die Warteschlange ist ein Fehler aufgetreten.
linkObject=Beziehung-Objekt
importMetainfo.warn.attrUsedInValueMapCatalog=Das Attribut {0} wurde gelöscht, als die Metainformationen geladen wurden, aber bei der Erstellung der Positionen in der Korrespondenztabelle {1} verwendet, die Tabelle muss gelöscht werden.
localization=(Gebietsschema: "{0}")
metainfo.DelAttributeActionHandler.attrUsedInMSC=Das Attribut wird im Parameter ''Suchrelevanzkriterium'' der Klasse ''{0}'' verwendet
metainfo.DelAttributeActionHandler.attrRelatedToBackLink=Das Attribut ist mit den anderen Attributen ''Backlink'' verknüpft: {0}
metainfo.DelAttributeActionHandler.attrUsedAtCommentListContent=Das Attribut wird im Inhalt von ''Kommentare zum Objekt'' verwendet: {0}
metainfo.DelAttributeActionHandler.attrUsedAtFileListContent=Das Attribut wird im Inhalt der ''Dateiliste'' verwendet: {0}
metainfo.DelAttributeActionHandler.attrUsedAtRelObjectContent=Das Attribut wird im Inhalt ''Liste der verknüpften Objekte'' verwendet: {0}
metainfo.DelAttributeActionHandler.attrUsedInBreadCrumb=Das Attribut wird in den Breadcrumb-Einstellungen verwendet.
metainfo.DelAttributeActionHandler.attrUsedInDateTimeRestriction=Das Attribut ist mit dem Attribut ''{0}'' verknüpft.
metainfo.DeleteContentActionHandler.cantDelete=Tab ''{0}'' kann nicht gelöscht werden.
importMetainfo.loadingImportConfigurationDone=Importkonfigurationen geladen: '{}'
importMetainfo.loadingImportConfigurationStart=Laden von Importkonfigurationen
importMetainfo.loadingMailRule=['{}'/'{}'] Herunterladen von Mail-Verarbeitungsregeln '''{}':'{}'''
importMetainfo.loadingMetaClassStart=Beginn des Ladens von Metaklassen
importMetainfo.importMetaclass=Importieren '{}'
loadStatusExceptionMessage=Es gibt Objekte im System, die sich in einem Status befinden, der nicht in den Metainformationen enthalten ist.
metainfo.MetaClassIsNotSpecified=Der Objekttyp ist nicht angegeben.
importMetainfo.eventActionJMSQueueDoesNotExists=Beim Laden der Metainformationen für die Aktion zum Ereignis "{0}" wurde der Wert des Feldes "Aktionswarteschlange" auf "{1}" ({2}) geändert, da keine Warteschlange mit dem Code "{3}" gefunden wurde.
importMetainfo.error.statesInheritance=Die Einstellungen aus der Datei können nicht geladen werden. Es gibt einen Status {0} im System, für den die Vererbung unterbrochen ist und der nicht in den geladenen Metainformationen der Klasse {1} enthalten ist.
importMetainfo.fileInformation=Metainformationsdatei\nImport type: '{}'\nVersion der Metadaten-Datei: '{}'\nDatum des Hochladens: '{}'
importMetainfo.ignoringHiddenMetaClass=['{}'/'{}'] Versteckte Metaklasse '''{}''' weglassen
importMetainfo.jmsQueueMoreMaxUserQueues=Die Benutzerwarteschlangen werden erfolgreich geladen. Die Anzahl der geladenen Benutzerwarteschlangen ist größer als die zulässige Anzahl - {0}
importMetainfo.loadingAdvListSettings.AttentionMissingSecGroup=Meta-Informationen mit Kommentaren geladen. Beim Laden von Metainformationen wurde die Benutzergruppe mit dem Code ''{0}'' aus der Liste der Gruppen entfernt, die gemeinsame Typen komplexer Listen erstellen dürfen, da die Benutzergruppe nicht gefunden wurde
importMetainfo.loadingResponsibleTransfersDone=Informationen zur Möglichkeit der Verantwortungsübertragung zwischen Teams geladen: '{}'
importMetainfo.loadingSchedulerTask=['{}'/'{}'] Laden der Scheduler-Aufgabe '''{}':'{}'''
importMetainfo.loadingSchedulerTaskDone=Scheduler-Einstellungen geladen: '{}'
importMetainfo.loadingSchedulerTaskStart=Laden der Scheduler-Aufgaben
importMetainfo.loadingUserJMSQueuesDone=Benutzer-Warteschlangen geladen: '{}'
licenseSeviceImpl.hasNotLicensedCode=Der Lizenzgruppencode kann nicht ''{0}'' sein.
metainfo.CopyMetaClassActionHandler.determiner="Definitionsregel" für Attribute: {0}
metainfo.CopyMetaClassActionHandler.determiners=Definitionsregeln {0}
metaClassAttrNotFound=Im Objekt ist keine Metaklasse angegeben
metainfo.AddStateActionHandler.stateCodeExist=Status mit Code ''{0}'' kann nicht hinzugefügt werden. Der Statuscode muss innerhalb des Typs eindeutig sein
metainfo.CopyMetaClassActionHandler.cantCopyTypeManyToManyAttributeTableExist=Der Typ kann nicht kopiert werden. Die Anforderung, dass die ersten acht Zeichen des Klassencodes, des Typs und des Typattributs ''{0}'' eindeutig sein müssen, wird verletzt, da es ein Attribut mit dem Code ''{1}'' in dem kopierten Typ gibt. Ändern Sie den Typencode.
metainfo.CopyMetaClassActionHandler.copyWarning=Für die Klasse "{0}" ist kein {1} definiert. Die Klasse "{0}" hat nicht die folgenden Einstellungen für die Klasse "{2}": {3}
metainfo.CopyMetaClassActionHandler.errorBadCase=Bezeichnerkonflikt zwischen erstelltem Typ und Klasse
metainfo.CopyMetaClassActionHandler.mustBeClass=Die zu erstellende Metaklasse muss eine Klasse sein
metainfo.CopyMetaClassActionHandler.role=Zugriffsrechte für die Profile: {0}
metainfo.DelAttributeActionHandler.usedInExportNdapClassOnly=Das Attribut wird in der Liste der vom Überwachungssystem im Attribut ''{0}'' (Klasse ''{1}'') verfügbaren Attribute ausgewählt.
metainfo.DelAttributeActionHandler.usedInTool=Das Attribut wird bei der Einstellung des Aktionsleistenelements {0} verwendet.
metainfo.DelAttributeActionHandler.usedInTools=Das Attribut wird bei der Einstellung von Aktionsleistenelementen {0} verwendet.
metainfo.DelAttributeActionHandler.wfProfileError=Das Attribut wird in dem verknüpften Workflow-Profil verwendet: {1}
metainfo.DelAttributeActionHandler.wfProfilesError=Das Attribut wird in verknüpften Workflow-Profilen verwendet: {1}
metainfo.DelSecurityGroupActionHandler.cantDeleteGroup=Die Benutzergruppe "{0}" kann aus den folgenden Gründen nicht gelöscht werden:
metainfo.DelStateActionHandler.cantDeleteHardcoded=Systemstatus ''{0}'' kann nicht gelöscht werden
metainfo.DelStateActionHandler.cantDeleteInherited=Der Status ''{0}'' kann nicht gelöscht werden, da er der übergeordneten Klasse/dem übergeordneten Typ hinzugefügt wurde
metainfo.DeleteScriptModuleActionHandler.cantDeleteModule=Das Skriptmodul {0} kann aus den folgenden Gründen nicht entfernt werden:
metainfo.DeleteScriptModuleActionHandler.cantDeleteModules=Das Skriptmodule "{0}" kann aus den folgenden Gründen nicht entfernt werden:
metainfo.EditMetaClassActionHandler.addWorkflowError=Der Workflow ist der Klasse nicht hinzugefügt worden. Grund: {0}.
metainfo.EditSecurityGroupMembersActionHandler.cantEditDisabledGroup=Die Systemgruppe "{0}" kann nicht geändert werden, da sie ausgeschaltet ist.
metainfo.MetaClassDeleteListener.CatalogReference=Metaklasse ''{0}'' kann nicht gelöscht werden, da sie von Verzeichnis ''{1}'' referenziert wird
metainfo.MetainfoServiceBean.catalog=Verzeichnis
jmsQueue.system.description.Queue.PlannedEvent=Die Warteschlange verarbeitet Ereignisaktionen mit dem Aktion "Beginn der Attributzeit" ausgewählt.
jmsQueue.system.title.Queue.EventAction.Pushes=System-Warteschlange für Benachrichtigungen
jmsQueue.system.title.Queue.External.EventAction=Systemaktionswarteschlange mit Attribut "Interaktion mit externem System"
jmsQueue.system.title.Queue.bridgeNdapAlertIn=Systemaktionswarteschlange für NDAP-Ereignisse
jmsQueue.unableToAddNonUniqueCode=Eine Benutzerwarteschlange mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Code der Benutzerwarteschlange muss eindeutig sein. Eine Warteschlange mit diesem Code existiert bereits oder wartet darauf, gelöscht zu werden.
leftMenu.CompanyLeftMenuItemValue.defaultTitle=Firma
leftMenu.FavoritesLeftMenuItemValue.defaultTitle=Favoriten
leftMenu.LeftMenuRootValue.defaultTitle=Menü
leftMenuSettings=Einstellungen im linken Menü
library.validation.mustHaveAtLeastOneClass=Datei {0} muss mindestens eine .class-Datei enthalten
library.validation.notAZip=Datei {0} ist kein jar-Archiv
licenseSeviceImpl.absentAuthor=Der Autor, der die Datei erstellt hat, ist nicht angegeben.
jmsQueue.setRoutingType.error=Beim Moduswechsel ist ein Fehler aufgetreten oder die Benutzerwarteschlange wurde nicht gefunden.
jmsQueue.system.title.Queue.PlannedEvent=System-Aktionswarteschlange für das Ereignis "Beginn der Attributzeit"
jmsQueue.system.title.Queue.UserEventAction=Systemwarteschlange der Benutzeraktionen nach Ereignis
metainfo.AddAttributeActionHandler.aggregateAttrError=Die Klassen "Abteilung" und/oder " Team" müssen für die Aggregation ausgewählt werden.
metainfo.AddMetaClassActionHandler.errorCaseDisallowed=Der Typ/die Klasse kann nicht hinzugefügt werden. Der Wert ''{0}'' kann nicht als Code verwendet werden
metainfo.AddMetaClassActionHandler.errorRelCase=Eine Klasse kann nicht innerhalb eines Typs verschachtelt werden.
metainfo.ClassIsNotSpecified=Die Objektklasse ist nicht angegeben.
metainfo.DelAttributeActionHandler.attrDeclaredAtParent=Das Attribut ist in einer übergeordneten Klasse oder einem übergeordneten Typ definiert.
metainfo.DelAttributeActionHandler.attrInEventActionsUse=Das Attribut wird in Aktionen zum Ereignistyp "{0}" verwendet: {1}
metainfo.DelAttributeActionHandler.attrIsSystem=Das Attribut ist ein Systemattribut.
metainfo.DelAttributeActionHandler.attrRelatedToAttributeOfRelatedObject=Das Attribut ist mit den folgenden Attributen vom Typ ''Attribut des verknüpften Objekts'' verbunden: {0}
metainfo.DelAttributeActionHandler.attrUsedInWindowCaption=Das Attribut wird in den Kopfeinstellungen des Metaklassenobjekts verwendet: {0}
metainfo.DelAttributeActionHandler.usedInExportNdap=Das Attribut wird in der Liste der im Überwachungssystem verfügbaren Attribute "{0}" (Typ "{1}", Klasse "{2}") ausgewählt.
metainfo.EditMetaClassActionHandler.changeStateButtonMissingForCase=Die Schaltfläche "{0}" wird nicht in die Aktionsleiste des Objektkartentyps {1} (Klasse {2}) verschoben, da die Vererbung der Einstellungen unterbrochen ist.
metainfo.EditMetaClassActionHandler.changeStateButtonMissingForClass=Die Schaltfläche "{0}" wird nicht in die Aktionsleiste der Objektkarte der Klasse {1} verschoben, da die Vererbung der Einstellungen unterbrochen ist.
metainfo.EditMetaClassActionHandler.editNestedToOtherError=Die Verschachtelung einer Klasse kann nur geändert werden, wenn die Klasse in sich selbst verschachtelt ist oder wenn keine Verschachtelung angegeben ist.
importMetainfo.loadingCommonSearchSettingsDone=Allgemeine Sucheinstellungen geladen: '{}'
importMetainfo.loadingMetaClass=['{}'/'{}'] Metaklasse '''{}''' laden
licenseSeviceImpl.cannotLoadLicenseFile=Die Lizenzdatei kann nicht heruntergeladen werden.\n{0}
listTemplates.newTemplate=[neu hinzufügen]
metainfo.DelSecurityGroupActionHandler.cantDeleteSystemGroup=Die Systemgruppe "{0}" kann nicht gelöscht werden.
importMetainfo.loadingDomain=['{}'/'{}'] Laden von Berechtigungseinstellungen
importMetainfo.reloadingCache=Neustart des Caches
importMetainfo.loadingCatalog=['{}'/'{}'] Verzeichnis laden '''{}'''
importMetainfo.loadingMetaClassDone=Metaklasseneinstellungen geladen: '{}'
importMetainfo.ignoringHiddenMetaClassDomain=['{}'/'{}'] Berechtigungseinstellungen für versteckte Metaklasse '''{}''' ignorieren
importMetainfo.importObjectError=Objektimportfehler
importMetainfo.jmsQueueMoreMaxThread=Die Warteschlangen werden erfolgreich geladen. Die Anzahl der Verarbeitungsfäden für geladene Warteschlangen "{0}" ist größer als die zulässige Anzahl - {1}
importMetainfo.loadingSystemJMSQueuesStart=Beginn des Ladens der Systemwarteschlangen
importMetainfo.loadingUi=['{}'/'{}'] Benutzeroberfläche laden '''{}':'{}'''
importMetainfo.loadingSystemJMSQueue=['{}'/'{}'] Systemwarteschlange laden '''{}':'{}'''
importMetainfo.loadingSystemJMSQueuesDone=System-Warteschlangen geladen: '{}'
importMetainfo.importFastLinkSettings=['{}'/'{}'] Objekterwähnungseinstellungen laden: '{}'
importMetainfo.loadingSecGroup=['{}'/'{}'] Benutzergruppe laden '''{}':'{}'''
importMetainfo.loadingStructuredObjectsView=['{}'/'{}'] Ladestruktur: '''{}'''
importMetainfo.removeDeclaredAttributeGroup=Attributgruppe {0} in Metaklasse {1} entfernt
importMetainfo.reloadingCacheDone=Abgeschlossen ('{}'): Neustart des Cache
importMetainfo.removeDeclaredAttribute=Attribut {0} in Metaklasse {1} gelöscht
invalidAccessKey=Die Anmeldung beim Link ist fehlgeschlagen, weil der übermittelte Autorisierungsschlüssel nicht funktioniert (abgelaufen oder zugangsbeschränkt). Melden Sie sich bei Ihrem Konto an, um fortzufahren.
metainfo.DelAttributeActionHandler.attrUsedInMenu=Das Attribut ''{0}'' kann nicht entfernt werden, da es an der Einstellung von Menüpunkten beteiligt ist: {1}
metainfo.DelStateActionHandler.StateUsedInEventStorageRule=Der Status wird in der Speicherregel von Ereignisprotokoll verwendet: {0}.
metainfo.DelStateActionHandler.StateUsedInTimer=Der Status wird in Zeitzählern verwendet: {0}.
importMetainfo.iconDoesNotExists=Der Parameter "'{}'" im Menü für Objektaktionen, der in '{}' angegeben ist, referenziert auf ein Symbol, das im System nicht existiert: uuid = "'{}'". Der Wert des Parameters wird auf den Standardwert gesetzt - Symbol "Zusätzliche Aktionen"""
importMetainfo.loadingChangeTrackingSettingsDone=Änderungsverfolgungseinstellungen geladen
metainfo.defaultTheme=Standard ({0})
metainfo.actionBarUsage=Aktionsleiste
metainfo.MetainfoServiceBean.metaClassNotFound=Metaklasse nicht gefunden: fqn={0}
metainfoValidation.aggregateAggregateTypeError=Der "type/property"-Tag mit dem XML-Attribut ''attributes'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{2}''. Ein Attribut mit dem Code ''{3}'' in Typ/Klasse ''{0}'' kann nicht aggregiert werden.
metainfoValidation.aggregateAggregateParentError=Das "type/property"-Tag mit XML-Attributcode ''attributes'' im Attributcode ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert ''{2}''. Das Attribut mit dem Code ''{3}'' ist nicht in der Klasse, auf die das Attribut mit dem Code ''{1}'' verweist.
metainfo.inMobileApp=auf der mobilen Anwendung
metainfo.objectCard=Objektkarte
metainfo.objectCardWithCode=Objektkarte mit Code
metainfo.security.superuserLoginNonAscii=Der Superuser kann nicht hinzugefügt werden. Der Superuser-Login darf nur lateinische Buchstaben enthalten.
metainfo.tooLongTitle=Der Name ''{0}'' überschreitet {1} Zeichen.
metainfo.nextObjectsHaveTheState=Die folgenden Objekte sind im Status ''{0}'': {1}
metainfo.security.profileCodeMustBeUnic=Profil mit Code ''{0}'' kann nicht hinzugefügt werden. Der Profilcode muss eindeutig sein.
metainfo.security.superuser.add.error=Ein Technologe kann aus folgenden Gründen nicht hinzugefügt werden:\n1. Das Konto des ausgewählten Mitarbeiters ist ein Dienstkonto.
metainfo.security.superuser.edit.error=Technologe {0} kann aus folgenden Gründen nicht geändert werden:\n1. Das Konto des ausgewählten Mitarbeiters ist ein Dienstkonto.
metainfo.security.superuserExists=Der Superuser kann nicht hinzugefügt werden. Ein Superuser mit diesem Login existiert bereits.
metainfo.RestoreClassMetainfoActionHandler.errorRemovedParent=Typ ''{0}'' kann nicht aus dem Archiv wiederhergestellt werden, der übergeordnete Typ {1} befindet sich im Archiv
metainfo.errorDefineAgrServ=Die Vereinbarung und der Service sind nicht spezifiziert.
metainfo.incorrectExportMode=Metainformationen konnten nicht geladen werden. Ungültige Upload-Methode angegeben.
metainfo.toolUsage=, Taste ''{0}''
metainfo.toolsUsage=Taste ''{0}''
metainfo.themeStyleAbsentValueError=Parameter ''{0}'' ist nicht gesetzt oder in der hochgeladenen Datei nicht vorhanden. Der Parameterwert wird auf den Standardwert ''{1}'' gesetzt.
metainfo.themeStyleBadValueError=Parameter ''{0}'' enthält einen ungültigen Wert. Der Parameterwert wird auf den Standardwert ''{1}'' gesetzt.
metainfo.inlineCommentUsage=Kommentarfeld
metainfo.security.roleWithCodeDoesNotExist=Rolle mit Code ''{0}'' existiert nicht
metainfo.security.superusers.limit.exceeded=Der Superuser kann nicht hinzugefügt werden. Die maximal zulässige Anzahl an Superuser-Lizenzen wurde überschritten.
metainfo.security.userExists=Der Superuser kann nicht hinzugefügt werden. Ein Mitarbeiter mit diesem Login existiert bereits: ''{0}''.
metainfo.errorServiceHaveNotScTypes=Dienst ''{0}'' ist keinem Tickettyp zugeordnet.
metainfo.SetLocaleError=Beim Festlegen des Gebietsschemas ist ein Fehler aufgetreten. Überprüfen Sie, ob der eingestellte Gebietsschemacode korrekt ist. Verfügbare Optionen: {0}.
metainfo.errorServiceScTypeRelation=Der Service ''{0}'' ist nicht mit dem Tickettyp ''{1}'' verbunden.
metainfo.SetThemeError=Bei der Einrichtung des Themes ist ein Fehler aufgetreten. Überprüfen Sie, ob der Code des Themas, das Sie installieren, korrekt ist. Verfügbare Optionen: {0}.
metainfo.SetThemeError.WrongModule=Bei der Einrichtung des Themes ist ein Fehler aufgetreten. Prüfen Sie, ob der installierte Modulcode korrekt ist. Verfügbare Optionen: {0}.
metainfo.SetTimeZoneError=Beim Einstellen der Zeitzone ist ein Fehler aufgetreten. Überprüfen Sie, ob die eingestellte Zeitzonenkennung korrekt ist.
metainfo.SetTimeZoneForRelatedEmployees=Die Zeitzonen für die Mitarbeiter der Abteilung wurden erfolgreich eingestellt.
metainfo.bulkOperationsBarUsage=Panel für Massenoperationen
metainfo.menuOfMobileCardUsage=Aktionsmenü der Objektkarte mit dem Code ''{0}'' in der mobilen Anwendung
metainfo.SecGroupUsedAtNextProfiles=Die Benutzergruppe wird in den folgenden Profilen verwendet: {0}
metainfoValidation.actionToolActionError=Das "action"-Tag des Steuerelements mit Code ''{2}'' für Typ/Klasse ''{0}'' fehlt.
metainfoValidation.actionToolCaptionEmptyError=Der "caption"-Tag des Steuerelements mit dem Code ''{2}'' für Typ/Klasse ''{0}'' fehlt.
metainfoValidation.actionToolCaptionError=Der "caption"-Tag eines Steuerelements mit dem Code ''{2}'' für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{3}''. Der Tag-Wert muss eine Zeichenkette mit einer Länge zwischen 1 und 64 Zeichen sein.
metainfoValidation.actionToolPresentationTypeError=Das "presentationType"-Tag des Steuerelements mit dem Code ''{2}'' für den Typ/die Klasse ''{0}'' enthält einen ungültigen Wert von ''{3}''. Das Tag kann folgende Werte annehmen: "default", "push", "link".
metainfoValidation.aggregateAggregateError=Der "type/property"-Tag mit dem XML-Attribut ''attributes'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{2}''. Das Attribut mit dem Code ''{3}'' ist in der Typ/Klasse ''{0}'' nicht vorhanden.
metainfo.SwitchThemeError=Das Thema ''{0}'' kann nicht für die Benutzerauswahl deaktiviert werden, da es als Standardthema für die Anzeigeoberfläche verwendet wird.
metainfo.cannotLoadMetainfoFileWrongFormat=Ein Fehler ist aufgetreten. Metainformationen konnten nicht geladen werden. Das Dateiformat ist falsch. {0}
metainfo.domain.roleCodeMustBeUnic=Rolle mit Code ''{0}'' kann nicht hinzugefügt werden. Die Rollen-ID muss eindeutig sein.
metainfo.emptyTitle=Leerer Titel.
metainfo.errorDefObjsRemove=Archivobjekte sind als Standardeinstellungen ausgewählt.
metainfo.errorDefaultScType=Die Metaklasse ''{0}'' existiert nicht oder ist kein Anforderungstyp oder ist archiviert.
metainfo.errorRelationDefaultAgrServ=Die ''{0}''-Vereinbarung und der ''{1}'' Service sind nicht verbunden.
metainfo.menuOfActionsWithObjectUsage=Objektaktionsmenü
metainfo.agreementUndefined=Es wird keine Vereinbarung angezeigt.
metainfo.security.superuser.alreadyDeleted=Das Superuser-Passwort kann nicht geändert werden, weil dieser Superuser wurde gelöscht.
metainfo.template=Listenvorlage
metainfoValidation.aggregateError=Es gibt keinen "type/property"-Tag mit dem XML-Attribut ''attributes'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}''.
metainfo.content=Inhalt
metainfoValidation.applicationCannotBeDeleted=Die Anwendung {0} kann aus den folgenden Gründen nicht gelöscht werden:
metainfoValidation.applicationCannotBeDeleted.few=Die Anwendungen {0} können aus den folgenden Gründen nicht gelöscht werden:
metainfo.SameThemeCodeError=Ein Schnittstellenthema mit dem Code ''{0}'' kann nicht hinzugefügt werden. Der Code muss eindeutig sein.
metainfo.SecGroupIsRequiredValue=Die Gruppe ist mit einem obligatorischen Attribut der folgenden Objekte verbunden: {0}
metainfo.MetainfoServiceBean.entityExistsError=kann nicht hinzugefügt werden. Der Wert ''{0}'' kann nicht als Code für benutzerdefinierte Klassen verwendet werden.
metainfo.MetainfoServiceBean.exists=kann nicht hinzugefügt werden. Es gibt bereits {0} mit dem Code ''{1}''.
metainfo.MetainfoServiceBean.typeExists=kann nicht hinzugefügt werden. Die Klasse ''{0}'' hat bereits einen Typ mit dem Code ''{1}''.
metainfo.security.profileNotFound=Das Profil mit dem Code "{0}" wurde in der Klasse "{1}" nicht gefunden.
metainfo.tab=Tab
metainfoValidation.attributeAccessError=Klasse/Typ {0} hat kein Attribut {1}
metainfoValidation.applicationUsedInNavigationMenuMKReason=Elemente des Navigationsmenüs: {0}.
metainfoValidation.applicationUsedInMKReason=Inhalt: {0} auf der Objektkarte mit dem Code ''{1}''.
metainfoValidation.applicationUsedInOtherSettingsSecurityBlock=Tab "Andere", Block "Sicherheit".\n
metainfoValidation.applicationUsedInUIReason=Die Anwendung wird zum Festlegen der Schnittstelle von Klassen und Typen verwendet: {0}.
metainfoValidation.attrGroupNotCorrectInStructuredObjectsViewItem=Falsche Attributgruppe "{0}" verwendet in Struktur "{1}", in Strukturelement "{2}"
metainfoValidation.attrGroupUsedAtObjectList=Das "attributeGroup"-Tag im Inhalt mit dem Code ''{2}'' in der type/class-Objektkarte ''{0}'' enthält einen ungültigen Wert von ''{3}''.
metainfoValidation.applicationUsedInMKReasonHeader=Die Anwendung wird in den Einstellungen der mobilen Anwendung verwendet.
metainfoValidation.attrUseAtObjectList=Der Wert ''{3}'' wird auf der Karte für Typ/Klasse ''{0}'' im Inhalt ''{2}'' verwendet, existiert aber nicht
metainfoValidation.attributeAccessorError=Das "accessor"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Tag kann Werte annehmen: {3}.
metainfoValidation.applicationCannotBeDisabled=Die Anwendung {0} kann aus den folgenden Gründen nicht ausgeschaltet werden:
metainfoValidation.applicationCannotBeDisabled.few=Die Anwendungen {0} können aus den folgenden Gründen nicht ausgeschaltet werden:
metainfoValidation.attributeTitleEmptyError=Das Attribut mit dem Code ''{1}'' für den Typ/die Klasse ''{0}'' enthält keinen "title"-Tag.
metainfoValidation.attributeGroupTitleEmptyError=Es gibt keinen "title"-Tag in der Attributgruppe mit dem Code ''{1}'' für den Typ/die Klasse ''{0}''.
metainfoValidation.notAllowChangeEndStateProperty=Metainformationseinstellungen können nicht angewendet werden: Der Wert von "Endstatus" für Status "{0}" in Typ/Klasse "{1}" kann im System nicht von true auf false geändert werden.
metainfoValidation.attributePresentationError=Der Tag "{3}Presentation/code" im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Für Attribute dieses Typs kann das Tag Werte annehmen: {4}.
metainfoValidation.attributeCodeError=Der "code"-Tag im Attribut für Typ/Klasse "{0}" enthält den ungültigen Wert "{1}". Der Wert des Tags muss eine Zeichenfolge aus lateinischen Buchstaben und Zahlen mit einer Länge zwischen 1 und 14 Zeichen sein, die mit einem Buchstaben beginnt.
metainfoValidation.importMetaClasses.deleteMetaClassForExistedObjects=Die Metainformationen können nicht geladen werden. Es gibt Objekte der Metaklasse "{0}" im System, die nicht in der Metainformation enthalten sind.
metainfoValidation.tabBarNoTabsError=Es gibt kein "tab"-Tag im Inhalt mit dem Code ''{2}'' in der Objektkarte des Typs/Klasse ''{0}''.
metainfoValidation.tabCaptionError=Der "caption" -Tag in der Tab mit dem Code "{2}" für den Typ/die Klasse "{0}" enthält einen ungültigen Wert von "{3}". Der Wert des Tags muss eine Zeichenfolge mit einer Länge zwischen 1 und 64 Zeichen sein.
metainfoValidation.uiChangeCaseForFqnAlreadySet=Für den Typ ''{0}'' (''{1}'') ist bereits ein benutzerdefiniertes Formular zum Ändern des Typs konfiguriert. Für einen Typ kann nicht mehr als ein Formular konfiguriert werden.
metainfoValidation.structuredObjectsViewInContentNotExist=Die Struktur mit Code "{0}", auf die im "Hierarchischen Baum" mit der uuid "{1}" verwiesen wird, existiert nicht.
metainfoValidation.structuredObjectsViewInParameterNotExist=Die Struktur mit Code "{0}", auf die "{1}" in der Aktion zum Ereignis "{2}" ("{3}") verweist, existiert nicht.
metainfoValidation.timerAttributeDefinitionError=Der "type/property"-Tag mit dem XML-Attribut ''definition'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Die Konfiguration des Zeitzählers ''{2}'' fehlt.
metainfoValidation.uiChangeResponsibleForFqnAlreadySet=Für den Typ ''{0}'' (''{1}'') ist bereits ein benutzerdefiniertes Formular zum Ändern der verantwortlichen Person konfiguriert. Für einen Typ kann nicht mehr als ein Formular konfiguriert werden.
metainfoValidation.structureCodeError=Der "code"-Tag für die Struktur/den Eintrag "{0}" enthält einen ungültigen Wert. Der Wert des Tags muss eine Zeichenfolge aus lateinischen Buchstaben und Zahlen mit einer Länge zwischen 1 und 255 Zeichen sein, die mit einem Buchstaben beginnt.
metainfoValidation.uiCodeEmptyError=Es gibt kein "code"-Tag in der Beschreibung der Benutzeroberfläche für Typ/Klasse ''{0}''.
metainfoValidation.uiCodeError=Der "code"-Tag in der Beschreibung der Benutzeroberfläche für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{1}''.
metainfoValidation.uiContentEmptyError=Die Tags "window" und "form" fehlen in der Beschreibung der Typ-/Klassenschnittstelle ''{0}''.
metainfoValidation.uiFqnError=Der "fqn"-Tag in der Beschreibung der Typ-/Klassenschnittstelle enthält den ungültigen Wert ''{0}''. Typ/Klasse ''{0}'' ist nicht definiert.
metainfoValidation.usagePointInParameterInStructuredObjectsView=In der Struktur "{0}" in der Liste "Verwendungsstellen" gibt es einen Verweis auf Parametercode = "{1}" in der Aktion zum Ereignis "{2}", der nicht im System vorhanden ist.
metainfoValidation.attributeTitleError=Der "title"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Wert des Tags muss eine Zeichenfolge mit einer Länge zwischen 1 und 64 Zeichen sein.
metainfoValidation.importCatalogs.deleteCatalogForExistedReference=Die Metainformationen können nicht geladen werden. Es gibt Objekte im System, die mit dem Verzeichniselement "{0}" verbunden sind, das nicht in den Metainformationen enthalten ist.
metainfoValidation.invalidImportMode=Metainformationen konnten nicht geladen werden. Teilweise Metainformationen können nicht geladen werden, wenn die Einstellungen vollständig überschrieben werden
metainfoValidation.linkToContent.attrGroupUsedAtObjectList=Der "attributeGroup"-Tag in den Einstellungen des linken Menüpunkts ''{2}'' enthält einen ungültigen Wert von ''{3}''. Der Wert des Tags sollte der Code einer bestehenden Attributgruppe sein.
metainfoValidation.commentListRelationAttrInvalidCode=Der "relationAttrCode"-Tag im Inhalt mit dem Code ''{2}'' in der Objekttyp/Klassenkarte ''{0}'' enthält den ungültigen Wert ''{3}''. Der Wert des Tags sollte ein Code eines bestehenden Attributs vom Typ "Verweis auf Business-Objekt" sein.
metainfoValidation.importEventActions.arriveMessageOnQueue.errorLoad=Beim Laden von Metainformationen wurde die Ereignisaktion ''{0}'' nicht geladen, weil eine Warteschlangenaktion bereits mit einem Ereignis wie "Ankunft einer Nachricht in der Warteschlange" verknüpft war oder der Typ der verarbeiteten Warteschlangennachrichten für diesen Ereignistyp nicht verfügbar war oder die Warteschlange nicht gefunden wurde.
metainfoValidation.attributeCodeEmptyError=Es gibt keinen "code"-Tag im Attribut für Typ/Klasse "{0}".
metainfoValidation.attributeTypeEmptyError=Das Attribut mit dem Code ''{1}'' für den Typ/die Klasse ''{0}'' enthält keinen "type"-Tag.
metainfoValidation.scriptsNotExists=Es gibt keine Skripte mit Codes: {0}.
metainfoValidation.attributeUsagePlace="{0}" (Klasse "{1}"{2}, Objektkarten-Tab "{3}", Inhalt "{4}", Attribut "{5}")
metainfoValidation.linkToContent.useAtMenuHierarchyGrid=Die Struktur mit dem Code ''{3}'', die beim Einstellen des Menüpunkts ''{2}'' verwendet wird, existiert nicht.
metainfoValidation.linkToContent.contentCaptionError=Der "caption"-Tag in der Einstellung "{2}" für den linken Menüpunkt hat den ungültigen Wert "{3}". Der Wert des Tags sollte eine Zeichenfolge mit einer Länge zwischen 1 und 255 Zeichen sein.
metainfoValidation.objectListBasePresentationError=Der "presentation"-Tag im Inhalt mit dem Code ''{2}'' in der Objekttyp-/Klassenkarte ''{0}'' enthält den ungültigen Wert ''{3}''. Der Tag kann folgende Werte annehmen: "default", "advlist".
metainfoValidation.attributeComputableError=Es gibt keinen "script"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}''.
metainfoValidation.linkToContent.objectListBasePresentationError=Der Tag "presentation" in der Einstellung für den linken Menüpunkt "{2}" hat den ungültigen Wert "{3}". Der Tag kann die folgenden Werte annehmen: "default", "advlist".
metainfoValidation.metaclassHasNoWorkflow=Kein Workflow mit der Klasse ''{0}'' verbunden
metainfoValidation.relAttrNotCorrectInStructuredObjectsViewItem=Inkorrektes Beziehungsattribut "{0}" in Struktur "{1}" verwendet, in Strukturelement "{2}"
metainfoValidation.nullLicenseAttrubuteMessage=Der Wert des Attributs Lizenz oder Standard ist NULL. Metaklasse: "'{}'"
metainfoValidation.objectAttributeTypeAttrChainError=Der "type/property"-Tag mit dem XML-Attribut ''attrChain'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert ''{3}''. Attribut mit Code ''{3}'' fehlt in Typ/Klasse ''{2}''.
metainfoValidation.objectAttributeTypeBackLinkError=Der "type/property"-Tag mit dem XML-Attribut ''backLinkAttr'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert ''{3}''. Attribut mit Code ''{3}'' fehlt in Typ/Klasse ''{2}''.
metainfoValidation.scriptsCodeIncorrect=Der "code"-Tag des Skripts "{0}" hat einen ungültigen Wert von "{1}". Das Feld "Code" muss mindestens ein Zeichen, aber nicht mehr als 64 Zeichen enthalten, mit einem Zeichen des lateinischen Alphabets beginnen und darf nur aus lateinischen Buchstaben und Zahlen bestehen.
metainfoValidation.structuredObjectsViewInAttributeNotExist=Die Struktur mit Code "{0}", auf die das Attribut "{1}" in Typ/Klasse "{2}" verweist, existiert nicht.
metainfoValidation.linkToContent.attrUseAtObjectList=Der Wert des Attributs ''{3}'' wird in den Einstellungen des linken Menüpunkts ''{2}'' verwendet, existiert aber nicht oder enthält einen ungültigen Wert.
metainfoValidation.linkAttributeCollision=Die Referenz im geladenen Referenzattribut "{0}" unterscheidet sich von der bestehenden Referenz in der Klasse "{1}". Das geladene Attribut verweist auf die Klasse "{2}" und das vorhandene Attribut verweist auf die Klasse "{3}"
metainfoValidation.attributeUsagePlaceType=, Typ "{0}"
metainfoValidation.commentListDetailedAttrGroupInvalidCode=Der "detailedAttrGroup"-Tag im Inhalt mit dem Code ''{2}'' in der Typ/Klassen-Objektkarte ''{0}'' enthält den ungültigen Wert ''{3}''. Der Wert des Tags sollte der Code einer bestehenden Attributgruppe sein.
metainfoValidation.checkLicenseGroupsError=Die Lizenzdatei enthält nicht die Lizenzgruppen, die als Standardlizenz für die Klasse/Typen angegeben sind: {0}.
metainfoValidation.tabCaptionEmptyError=Fehlendes "caption"-Tag im Tab mit Code ''{2}'' für Typ/Klasse ''{0}''.
metainfoValidation.tabLayoutError=In der Tab mit dem Code ''{2}'' für den Typ/die Klasse ''{0}'' ist kein " layout"-Tag vorhanden.
metainfoValidation.importEventActions.eventActionIdDoesNotSyncWithUserEventId=Metainformationen können nicht geladen werden. Die ID des Benutzerereignisses {0} stimmt nicht mit der ID der zugehörigen Ereignisaktion {1} überein
metainfoValidation.timerAttributeError=Es gibt keinen "type/property"-Tag mit einem XML-Attribut ''definition'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}''.
metainfoValidation.contentCaptionError=Der "caption"-Tag im Inhalt mit dem Code ''{2}'' in der Objekttyp-/Klassenkarte ''{0}'' enthält den ungültigen Wert ''{3}''. Der Wert des Tags muss eine Zeichenfolge mit einer Länge zwischen 1 und 255 Zeichen sein.
metainfoValidation.fileListRelationAttrInvalidCode=Der "attrChain"-Tag im Inhalt mit dem Code ''{2}'' in der Objekttyp/Klassenkarte ''{0}'' enthält einen ungültigen Wert von ''{3}''. Der Wert des Tags sollte ein Code eines bestehenden Attributs vom Typ "Verweis auf Business-Objekt" sein.
metainfoValidation.usagePointInContentInStructuredObjectsView=In der Struktur "{0}" in der Liste " Verwendungsstellen" gibt es einen Verweis auf den Inhalt "Hierarchischer Baum", der nicht im System vorhanden ist: uuid = "{1}".
metainfoValidation.usagePointInAttributeInStructuredObjectsView=In der Struktur "{0}" in der Liste "Verwendungsstellen" gibt es einen Verweis auf ein Attribut, das nicht im System vorhanden ist: fqn = "{1}".
metainfoValidation.linkToContent.useAtObjectList=Der Klassen-/Typwert ''{3}'' wird in den Einstellungen für den linken Menüpunkt ''{2}'' verwendet, existiert aber nicht.
metainfoValidation.attributeGroupTitleError=Der "title"-Tag in der Attributgruppe mit dem Code ''{1}'' für den Typ/die Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Wert des Tags muss eine Zeichenkette mit einer Länge zwischen 1 und 256 Zeichen sein.
metainfoValidation.attributeOverrideNotExistsError=Der "code"-Tag im Attribut override für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{1}''. Ein Attribut mit diesem Code muss in der Metaklasse vorhanden sein.
metainfoValidation.attributeGroupError=Der "attribute"-Tag in der Attributgruppe mit dem Code ''{1}'' für den Typ/die Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. In der Metaklasse gibt es kein Attribut mit dem Code ''{2}''.
metainfoValidation.objectAttributeTypeRelatedAttributeError=Der "type/property"-Tag mit dem XML-Attribut ''relatedObjectAttribute'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{3}''. Attribut mit Code ''{3}'' fehlt in Typ/Klasse ''{2}''.
metainfoValidation.objectAttributeTypeError=Es gibt keinen "type/property"-Tag mit dem XML-Attribut ''metaClassFqn'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}''.
metainfoValidation.objectAttributeTypeMetaClassError=Der "type/property"-Tag mit dem XML-Attribut ''metaClassFqn'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält den ungültigen Wert ''{2}''. Typ/Klasse ''{2}'' fehlt.
metainfoValidation.licenseGroup=Lizenzgruppe: {0}
metainfoValidation.attributeTypeError=Der "type/code"-Tag im Attribut mit dem Code ''{1}'' für den Typ/die Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''.
metainfoValidation.catalogAttributeCollision=Die Referenz im geladenen Attribut "{0}" unterscheidet sich von der bestehenden Referenz in der Klasse "{1}". Das geladenen Attribut bezieht sich auf das Verzeichnis "{2}" und das vorhandene Attribut auf das Verzeichnis "{3}"
metainfoValidation.plannedVersion.allowedClassesConflict=Die Metainformationen können nicht geladen werden. Die Eigenschaft "Erstellung geplanter Versionen zulassen" ist in Klassen aktiviert, für die die Lizenzdatei-Einstellungen keine Versionierung zulassen: {0}.
metainfoValidation.propertyListAttributeGroupEmpty=Es gibt kein "attributeGroup"-Tag im Inhalt mit dem Code ''{2}'' in der type/class-Objektkarte ''{0}''.
metainfoValidation.parametersNotCorrectInStructuredObjectsViewItem=Strukturelement ''{0}'' (''{1}'') der Struktur ''{2}'' (''{3}'') enthält Parameter, die im System nicht vorhanden sind: code = ''{4}''. Der Wert des Parameters war auf den Standardwert - "[not specified]" - gesetzt worden
metainfoValidation.timerAttributePossibleError=Der "type/property"-Tag mit dem XML-Attribut ''definition'' im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Zeitzähler ''{2}'' ist von einem anderen Typ.
metainfoValidation.attributeUsagePlaceOthers=und andere.
metainfoValidation.formLayoutError=Es gibt kein " layout"-Tag in der Form von Typ/Klasse ''{0}''.
metainfoValidation.contentCaptionEmptyError=Es gibt keinen "caption"-Tag im Inhalt mit dem Code ''{2}'' in der Objekttyp-/Klassenkarte ''{0}''.
metainfoValidation.attributeBoostError=Der "searchBoost"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Tag kann folgende Werte annehmen: "0,2", "0,5", "1,0", "1,5", "2,0".
metainfoValidation.attributeAnalizerError=Der "searchAnalizer"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Tag kann Werte annehmen: {3}.
metainfoValidation.attributeAliasError=Der "searchAlias"-Tag im Attribut mit dem Code ''{1}'' für Typ/Klasse ''{0}'' enthält einen ungültigen Wert von ''{2}''. Der Wert des Tags muss eine Zeichenkette zwischen 0 und 64 sein.
metainfoValidation.uiFqnEmptyError=In der Beschreibung der Typ-/Klassenschnittstelle ist kein "fqn"-Tag enthalten.
metainfoValidation.useAtAddFileTool=Der "attributeGroupForAddFile"-Tag in der Taste ''{2}'' für Karte/Formular Typ/Klasse ''{1}'' enthält den ungültigen Wert ''{3}''. Der Wert des Tags sollte der Code einer bestehenden Attributgruppe sein.
metainfoValidation.useAtRelObjPropertyListMC=Der Wert ''{3}'' wird auf der Karte für Typ/Klasse ''{0}'' im Inhalt ''{2}'' verwendet, existiert aber nicht
metainfoValidation.wfProfile.noCode=Es ist kein zugehöriger Workflow-Profilcode ''{0}'' angegeben
metainfoValidation.useAtObjectGraphMC=Die Metaklasse mit dem Code ''{3}'' wird auf der Karte für den Typ/die Klasse ''{1}'' im Inhalt mit dem Code ''{2}'' verwendet, ist aber nicht vorhanden
metainfoValidation.useAtObjectList=Der Wert ''{3}'' wird auf der Karte für Typ/Klasse ''{0}'' im Inhalt ''{2}'' verwendet, existiert aber nicht
metainfoValidation.useAtCustomForm=Der "attributeGroup"-Tag im Inhalt mit dem Code ''{2}'' im Benutzerformulartyp ''{0}'' enthält einen ungültigen Wert von ''{3}''. Der Wert des Tags sollte der Code einer bestehenden Attributgruppe sein.
metainfoValidation.wfProfile.noDisconnectingState=Kein Status für Verbindungsabbruch für verknüpftes Workflow-Profil ''{0}'' ist nicht angegeben
metainfoValidation.useAtRelObjPropertyListAttr=Der "attrCode"-Tag im Inhalt mit dem Code ''{2}'' in der Typ/Klassen-Objektkarte ''{0}'' enthält einen ungültigen Wert ''{4}''. Der Tag-Wert muss eine Zeichenfolge aus lateinischen Buchstaben und Zahlen mit einer Länge zwischen 1 und 14 Zeichen sein, die mit einem Buchstaben beginnt.
mobile.currentType=Aktueller Typ
metainfoValidation.useAtRelObjPropertyListGrp=Der "attributeGroup"-Tag im Inhalt mit dem Code ''{2}'' in der Typ/Klassen-Objektkarte ''{1}'' enthält den ungültigen Wert ''{3}''. Der Wert des Tags sollte der Code der Attributgruppe sein, die in Typ/Klasse ''{4}'' oder ihrem Vorgänger definiert ist.
metainfoValidation.wfProfile.noMaster=Für das verknüpfte Workflow-Profil ''{0}'' ist keine Hauptklasse angegeben
metainfoValidation.wfProfileFolder.noFolder=Für verknüpfte Workflow-Profile wird kein Ordnercode angegeben: {0}
metainfoValidation.windowTabBarError=Es gibt kein "tabBar"-Tag in der Typ-/Klassenkarte ''{0}''.
metainfoValidation.useAttrRelObjectListAttrLink=Das Attribut ''{3}'' existiert nicht in der Metaklasse ''{1}''
mobile.addButton=Hinzufügen
mobile.contents.information.caption=Informationen
metainfoValidation.wfProfile.noSlave=Für das verknüpfte Workflow-Profil ''{0}'' ist keine Slave-Klasse angegeben
metainfoValidation.wfProfile.noTitles=Der Name des zugehörigen Workflow-Profils ''{0}'' ist nicht angegeben
mobile.addForms=Formulare zum Hinzufügen von Objekten
mobile.addFormObjectCreatedWithoutTitle=Objekt wurde erfolgreich erstellt
metainfoValidation.useAtSelectParent=Das Attribut "{3}" wird verwendet, um die übergeordneten Auswahlparameter bei der Einrichtung der Schnittstelle in der Metaklasse "{1}" festzulegen
metainfoValidation.wfProfile.noTitle=Kein zugehöriger Workflow-Profilname ''{0}'' für Gebietsschema ''{1}''
metainfoValidation.youHaveDeletedAttributesWithButtons=Sie haben Attribut(e) "{0}" aus der Attributgruppe "{1}" ausgeschlossen.\nWird dies bestätigt, werden die Tasten zusammen mit den Attributen gelöscht:\n{2}.
mobile.addFormNotFound=Das angeforderte Hinzufügungsformular "{0}" wird in den Einstellungen der mobilen App nicht gefunden.\nWählen Sie auf der Einstellungsseite der mobilen App ein vorhandenes Formular aus oder geben Sie die richtige Adresse in die Browserleiste ein.
mobile.attrNotFoundInCard=Das Kartenattribut kann nicht geändert werden, da das Attribut nicht in der Karte gefunden wird.
mobile.attrNotFoundInList=Das Listenattribut kann nicht geändert werden, da das Attribut nicht in der Liste gefunden wird.
mobile.canOpenInMobileApp=Dieser Link kann<br>auf der mobilen App geöffnet werden
mobile.commentWasDeleted=Der Kommentar ist nicht mehr verfügbar, da er gelöscht wurde.
mobile.contentDetails="{0}" auf der Objektkarte mit dem Code "{1}"
mobile.addFormNotFoundByParams=Sie können Objekte dieses Typs nicht über die mobile App erstellen.
mobile.addFormObjectCantBeCreated=Sie können ein Objekt nicht über das ausgewählte Hinzufügungsformular erstellen.
mobile.attrNotFound=Attribut mit Code "{0}" nicht in Klasse/Typen gefunden.
metainfoValidation.useAtPropertyList=Der "attributeGroup"-Tag im Inhalt mit dem Code ''{2}'' in Typ/Klasse-Objektkarte ''{0}'' enthält einen ungültigen Wert ''{3}''. Der Wert des Tags sollte der Code einer bestehenden Attributgruppe sein.
mobile.cardNotFound=Die angeforderte Karte "{0}" wird in den Einstellungen der mobilen App nicht gefunden.\nWählen Sie eine vorhandene Karte auf der Einstellungsseite der mobilen App aus oder geben Sie die richtige Adresse in die Browserleiste ein.
mobile.deleteContents.addForms=Formulare zum Hinzufügen von Objekten: {0}.
mobile.addFormObjectCreated=Objekt "{0}" wurde erfolgreich erstellt
orange=Orange
mobile.loginType.external=Authentifizierung über SSO
objectsExistsListener.objectsOfClassExist=Es gibt Objekte dieser Klasse: {0}.
plannedVersion.branches=Zweige
quickForms.unableToEditCases=Das Formular "{0}" kann aus folgenden Gründen nicht bearbeitet werden:{1}
mobile.rest.account.associated_super_user_error=Ihr Superuser-Konto wurde mit einem Benutzerkonto verknüpft. Sie müssen sich erneut am System anmelden.
plannedVersion.editAttrNotAllowedInPlaningMode=Das Attribut ''{0}'' ({1}) vom Typ ''{2}'' ist im Änderungsplanungsmodus nicht bearbeitbar.
plannedVersion.deleteBranch.usedByAnotherUser=Das Objekt "{0}" kann nicht gelöscht werden. Die folgenden Benutzer befinden sich im Änderungsplanungsmodus des aktuellen Objekts: {1}.
quickForms.usedInTool=Das Formular wird für die Einstellung des Aktionsleistenelements {0} verwendet.
plannedVersion.skipAddUnversionedObjects=Methode {0} wird für Objekte {1} nicht ausgeführt, da die Klassen {2} keine Versionen erstellen dürfen.
quickAccessPanel.MenuRoot.defaultHint=Alle Menüs anzeigen
plannedVersion.bcp.ValidateCreationPlannedVersionOperation.error=Die Erstellung geplanter Versionen für die Klasse ''{0}''({1}) ist durch die Einstellungen in der Lizenzdatei verboten
plannedVersion.branchSysNonBlockedAttributeGroup=Nicht-sperrende Attribute (Versionierung)
quickActions.cyclicDependency=der Wert des obligatorischen Attributs "{0}" von Objekt "{1}" bezieht sich auf Objekt "{2}
mobile.usedInContentButton="{0}" (Objektkarte mit Code ''{1}'', Inhalt ''{2}'' in der mobilen App)
mobile.error.createRasterIcon=Bei der Konvertierung der Datei ''{0}'' in das PNG-Format ist ein Fehler aufgetreten: {1}.
mobile.metainfo.error.objectCard.attrOfListNotFill=Der Parameter "Liste für die Anzeige des Wertes" des Attributs "{0}" auf der Karte "{1}" darf nicht gefüllt werden. Der Wert des Parameters war auf den Standardwert - "[nicht angegeben]" - gesetzt worden.
mobile.metainfo.error.objectCard.parameterControlNotExist=Der Parameter "Aktion" der Position "{0}" des Aktionsmenüs der Karte "{1}" bezieht sich auf die Aktion, die im System nicht existiert: uuid = "{2}". Der Wert des Parameters war auf den Standardwert - "[nicht angegeben]" - gesetzt worden.
plannedVersion.linkToListCanNotBeOpened=Der Link kann aus den folgenden Gründen nicht geöffnet werden:\n1. Der Link führt zu einer Liste von Objekten aus dem Planungsmodus.
plannedVersion.plannedVersionCantBeEnabled=Die Eigenschaft "Erstellung von geplanten Versionen zulassen" kann aus folgenden Gründen nicht aktiviert werden:
ok=Ok
plannedVersion.bcp.AttributesAreBlockedOnEditPlannedBranch.error=Die Attribute von Objekt {0} sind für die Bearbeitung gesperrt, da sich Zweig {1} im Sperrstatus befindet.
plannedVersion.bcp.ObjectAreBlockedOnDeletePlannedBranch.error=Das Objekt ist für die Löschung gesperrt, da sich der {0}-Zweig im Sperrzustand befindet.
quickForms.attributeOfCase="{0}" (Typ "{1}" Klasse "{2}")
mobile.deleteContents.editForms=Formulare zur Objektbearbeitung: {0}.
mobile.error.couldNotPerformAction=Aktion kann nicht ausgeführt werden
mobile.rest.wrong.associatedSuperUserAuthError=Es ist nicht möglich, sich am System anzumelden. Das Superuser-Konto ist mit dem Benutzerkonto verknüpft.
mobile.transitionsNotFoundInCases=In den Klassen/Typen "{0}":{1} werden die folgenden Übergänge zwischen den Status nicht gefunden: {2}
plannedVersion.bcp.CheckEnvironmentObjectOperation.error=Das Umgebungsobjekt ist im Änderungsplanungsmodus nicht zur Bearbeitung verfügbar. Um das Objekt bearbeiten zu können, fügen Sie es zu einem Zweig hinzu.
mobile.error.couldNotDeleteAddForm=Das Formular zum Hinzufügen von Objekten ''{0}'' ({1}) kann nicht gelöscht werden.
mobile.rest.custom_auth_error=Fehler bei der Benutzerautorisierung. Wenden Sie sich an den Systemadministrator.
plannedVersion.bcp.ObjectAreBlockedOnDeleteMainBranch.error=Das Objekt ist zum Löschen gesperrt, weil das Objekt in den folgenden Zweigen Versionen hat, die sich im Sperrstatus befinden: {0}
quickForms.attributeOfClass="{0}" (Klasse "{1}")
quickForms.usedInAttributeParam=Das Formular wird bei der Einstellung des Ereignisaktionsparameters {0} verwendet.
quickForms.usedInAttributes=Das Formular wird in den Attributeinstellungen {0} verwendet.
quickForms.usedInTools=Das Formular wird für die Einstellung von Aktionsleistenelementen {0} verwendet.
reportParameters.missingMetaclass=Der Parameter "{0}"({1}) bezieht sich auf eine Metaklasse, die nicht im System enthalten ist: fqn={2}.
mobile.metainfo.error.objectCard.content.tagsNotExistOnCard=Die folgenden Tags werden auf der Karte "{0}" im Parameter "Tags" nicht gefunden: "{1}". Die nicht erkannten Tags im Parameter werden nicht gesetzt.
mobile.metainfo.error.objectCard.content.tagsNotExistOnContent=Die folgenden Tags werden im Inhalt "{0}" auf Karte "{1}" nicht gefunden, wie im Parameter "Tags" angegeben: "{2}". Die nicht erkannten Tags im Parameter werden nicht gesetzt.
mobile.rest.custom_settings_error=Beim Empfang des Anmeldeformulars ist ein Fehler aufgetreten. Wenden Sie sich an Ihren Systemadministrator.
mobileQuickAction.take=Mir selbst zuweisen
quickForms.usedInAttributesRestrictions=Das Formular wird in Attributeinstellungen {0} verwendet, bei denen die Typbeschränkungen nicht mit den Typen übereinstimmen, für die das Formular angewendet wird.
plannedVersion.locked=Version gesperrt
quickForms.unableToDelete=Das Formular "{0}" kann aus folgenden Gründen nicht gelöscht werden:{1}
mobile.fileWasDeleted=Die Datei kann nicht mehr angezeigt werden, da sie gelöscht wurde.
mobile.formHasChildren=Das Formular ist das übergeordnete Formular von {0}.
mobile.haveSettingsForAttr=Das Attribut wird in den Einstellungen der mobilen App verwendet.
mobile.haveSettingsForCase=Der Typ wird in den Einstellungen der mobilen App verwendet.
mobile.haveSettingsForClass=Die Klasse wird in den Einstellungen der mobilen App verwendet.
mobile.listNotFound=Die angeforderte Liste "{0}" wird in den Einstellungen der mobilen App nicht gefunden.\nWählen Sie eine vorhandene Liste auf der Einstellungsseite der mobilen App aus oder geben Sie die richtige Adresse in die Browserleiste ein.
mobile.rest.action.requires_location_error=Der Standort des Benutzers ist für die Durchführung der Aktion erforderlich.
mobile.rest.add_form_is_not_set_error=Es wurde kein Formular zum Hinzufügen von Objekten des Typs "{0} ({1})" eingerichtet
mobile.rest.card_is_not_set_error=Objektkarte vom Typ "{0} ({1})" ist nicht eingerichtet
mobile.rest.comment_add_form_title=Hinzufügen eines Kommentars
mobile.rest.content_is_not_available=Sie haben keine Berechtigung, Inhalte zu sehen.
plannedVersion.wrongHomePageURL=Fehler. Ungültiger homePage-Wert - geplante Version oder Umgebungsobjekt. Geben Sie die Objektseite in der Grundbetriebsart an.
mobile.remember=Auswahl speichern
mobile.rest.access_token_error=Der Autorisierungsschlüssel ist abgelaufen. Sie müssen sich erneut anmelden.
mobile.rest.view_comment_card_permission_error=Sie haben keine Berechtigung, diesen Kommentar zu sehen.
mobile.rest.view_comment_delete_permission_error=Sie haben keine Berechtigung, diesen Kommentar zu löschen.
mobile.rest.view_comment_edit_form_permission_error=Sie haben keine Berechtigung, diesen Kommentar zu bearbeiten.
personalServiceTimeCreatorValidator.canNotSetPersonalServiceTimeToSC=Die persönliche Serviceklasse "{0}" kann für das Objekt der Klasse "{1}" nicht eingestellt werden.
personalServiceTimeCreatorValidator.canNotCreatePersonalServiceTime=Es ist nicht möglich, ein persönliches Serviceklassenelement auf der Grundlage eines anderen persönlichen Serviceklassenelements zu erstellen.
plannedVersion.restricted=Erstellung von geplanten Versionen für die Klasse ''{0}''({1}) ist verboten
plannedVersion.selectAvailableObject=\nWählen Sie ein verfügbares Objekt über das Navigationsmenü oder die Suche aus.
plannedVersion.stateUsedInBlockingVersionsSettings=Der Status ''{0}'' wird bei der Einrichtung der Objektversionssperre verwendet.
plannedVersion.unableToFindSnapshot=Der Snapshot des Objekts konnte nicht gefunden werden. Derselbe Zweig wird als Quellzweig und als Zielzweig angegeben.
quickActions.cyclicDependenciesDetected=Es ist nicht möglich, Objekte hinzuzufügen, da ihre obligatorischen Attributwerte zyklisch voneinander abhängig sind
quickActions.noname=[{0} ohne Titel]
mobile.rest.file_error.large=Dateigröße ist größer als erlaubt ({0})
plannedVersion.BranchApi.objectNotFoundInBranch=Objekt {0} kann nicht zu einem Zweig hinzugefügt werden. Das Objekt befindet sich nicht im Ursprungszweig.
reports.sentEmail.failure=Nicht an E-Mail gesendet:
mobile.noAddForms=Sie können keine Objekte über die mobile App erstellen.
mobile.loginType.customForm=Benutzer-Login-Formular
mobile.loginType.customModule=Externer Authentifizierungsdienst
mobile.loginType.system=System-Login-Formular
mobile.noAppLoadFrom=Keine App? <br>Laden Sie sie auf Ihr Gerät herunter
mobile.noAppLoadFromIos=Keine App? <br>Herunterladen von&nbsp;
mobile.metainfo.error.securitySettings.passwordStorageTime=Seite "Mobile App", Tab "Andere", Block "Sicherheit", ungültiger Wert des Parameters "Passwortspeicherzeit auf dem Client".
mobile.metainfo.error.securitySettings.loginAttemptsCount=Seite "Mobile App", Tab "Andere", Block "Sicherheit", ungültiger Wert des Parameters "Anzahl der fehlgeschlagenen Versuche, eine PIN einzugeben, nach der alle App-Daten gelöscht werden".
mobile.rest.wrong.login_pass_error=Falsches Login oder Passwort
phones={0}
reports.report=''{0}'' Bericht
#ru.naumen.core.server.script.spi.ScriptDtOHelper
propertyChangingDeniedInScript=Das direkte Einstellen der Eigenschaftswerte eines Objekts ist nicht zulässig. Um Eigenschaften zu bearbeiten, verwenden Sie entsprechende Methodenaufrufe
#utils.processDocx
processDocx.error=Bei der Vorlageverarbeitung ist ein Fehler aufgetreten.
processDocx.notFileError=Es sieht so aus, als ob die an die Methode übergebenen Daten keine Datei sind.
processDocx.wrongFormat=Falsches Dateiformat! Bitte verwenden Sie *.docx
#ru.naumen.core.server.script.api.DbApi
notSupportedDBMS=Das aktuelle DBMS wird nicht unterstützt
objectAlreadyIsInNewState=Der Übergang zum Status ''{0}'' kann nicht erfolgen. Objekt {1} befindet sich bereits im neuen Status.
newEntryFormClazz=Formular zum Hinzufügen von Klassen
mobile.rest.view_comments_permission_error=Sie haben keine Berechtigung, Kommentare zu sehen
mobile.navigationMenu.embeddedApplication=Anwendung
mobile.navigationMenu.linkObjectAttrs=Beziehungsattribut für Objekt der Arbeit der Anwendung
mobile.navigationMenu.linkObjectCase=Typ des Mitarbeiters
mobile.navigationMenu.tags=Tags
mobile.rest.get_edit_form_error=Sie können über dieses Formular keine Objekte bearbeiten.
mobile.rest.account.archived_error=Ihr App-Konto wurde archiviert, wenden Sie sich an Ihren Administrator.
#ReportParameters
reportParameters.catalogNotFound=Der Parameter "{0}"({1}) bezieht sich auf ein Verzeichnis, das sich nicht im System befindet: Verzeichniscode={2}.
reports.sentEmail.export=Berichts-Upload "{0}" generiert: {1}
mobile.rest.account.locked_error=Ihr Konto ist in der App gesperrt, wenden Sie sich an Ihren Administrator.
mobile.rest.password.should_be_changed_error=Ihr Passwort wurde zurückgesetzt. Legen Sie ein neues Passwort in der Webanwendung fest.
plannedVersion.wrongArgumentType=Das Objekt, das im Argument übergeben wird, muss eine Instanz der SnapshotsDto-Klasse sein.
privateCommentView=Private Kommentare ansehen
reloadableSessionFactoryBean.concurrentRealoadError=Der Vorgang kann nicht ausgeführt werden, weil der vorherige Metainformationsvorgang nicht abgeschlossen wurde. Wiederholen Sie den Versuch nach einer gewissen Zeitspanne.
mobile.navigationMenu.error.couldNotGetLinkedObject=Es konnte kein Arbeitsobjekt für die integrierte Anwendung im Navigationsmenüelement "{0}" ("{1}") abgerufen werden.
mobile.rest.file_error.main=Die Datei konnte nicht hochgeladen werden. {0}.
mobile.deleteContents.lists=Objektlisten: {0}.
mobile.editForms=Formulare zur Objektbearbeitung
mobile.error.couldNotCreateObjectForClass=Die Erstellung von Klassenobjekten ist nicht möglich. Wählen Sie den richtigen Typ.
mobile.error.couldNotCreateObjectOfSelectedCase=Es ist nicht möglich, Objekte des angegebenen Typs über das ausgewählte Formular zu erstellen.
mobile.error.couldNotDeleteContent=Das Element "{0}" kann nicht gelöscht werden, da es im Navigationsmenü verwendet wird.
mobile.error.couldNotDeleteMobileListAttribute=Die Liste wird in der Attributeinstellung "{0}" ({1}) in der Objektkarte mit Code "{2}" verwendet.
mobile.metainfo.attributeOfMetaclass={0} von Metaklasse {1}
mobile.metainfo.error.navigationMenu.notImported=Der Parameter "{0}" des Navigationsmenüpunkts in der mobilen Anwendung "{1}" verweist auf ein Objekt(-e), das (die) im System nicht existiert: {2}. Der Navigationsmenüpunkt wurde nicht importiert.
relObjectList=Liste der verknüpften Objekte
objLinkedToCurrentUser=Objekt, das mit dem aktuellen Benutzer verbunden ist
objectList=Liste der Objekte
ou=Abteilung
objectCases=Typen von Objekten
mobile.duplicateContentUuidError=Die Metainformationen konnten nicht geladen werden. Mobile App-Einstellungen ({0}) mit demselben Code werden in der heruntergeladenen Datei gefunden: ''{1}''
mobile.metainfo.error.objectCard.attrOfListNotExist=Der Parameter "Liste für die Anzeige des Wertes" des Attributs "{0}" auf der Karte "{1}" bezieht sich auf die Liste, die im System nicht existiert: uuid = "{2}". Der Wert des Parameters war auf den Standardwert - "[nicht angegeben]" - gesetzt worden.
mobile.metainfo.error.objectCard.attrOfCaptionNotExist=Der Parameter "Objekttitel in der mobilen App" der Objektkarte "{0}" bezieht sich auf das Attribut, das im System nicht vorhanden ist: "{1}". Der Wert des Parameters war auf den Standardwert - "Titel" - gesetzt worden.
reports.sentEmail.startGeneration=Beginn des Berichts/Druckformulars Konstruktion "{0}"
reports.sentEmail.success=An E-Mail gesendet:
mobile.rest.file_error.malicious=Die Datei ist bösartig
reportParameters.nonExistentObjects=Der Standardwert "{0}" ({1}) bezieht sich auf Objekte, die im System nicht vorhanden sind: uuids = {2}.
reportParameters.nonExistentStates=Der Standardwert "{0}"({1}) bezieht sich auf Zustände in der Metaklasse {2}, die im System nicht existieren: {3}.
mobile.objectCards=Objektkarten
mobile.objectLists=Objektlisten
mobile.noPermissionsToSCAssociation=Sie haben keine Berechtigung, die Bindung dieses Objekts zu ändern.
mobile.noPermissionsToChangeTypeAndAdd=Sie haben keine Berechtigung, den Typ dieses Objekts in "{0}" zu ändern.
mobile.noPermissionsToChangeType=Sie haben keine Berechtigung, den Typ dieses Objekts zu ändern.
mobile.noPermissionsToChangeResponsible=Sie haben keine Berechtigung, den Verantwortlichen in der Klasse/Typ "{0}" zu ändern.
mobile.openInApp=In der App öffnen
mobile.openInWeb=Fortsetzen im Browser
mobile.openLink=Link öffnen
mobile.rest.action.not_for_mobile=Diese Aktion ist in der mobilen App nicht verfügbar.
mobile.rest.edit_form_is_not_set_error=Formular zur Objektbearbeitung vom Typ "{0} ({1})" ist nicht eingerichtet
originalFileDoesNotExists=Datei nicht gefunden.
plannedVersion.BranchApi.versionInfoNotFound=Das Objekt mit den Versionsinformationen wurde nicht gefunden.
plannedVersion.ErrorDetails.relatedTo={0} ist mit den folgenden Objekten im Planungsmodus ''{1}'' verbunden: {2}
mobile.error.couldNotDeleteMobileList=Die Objektliste "{0}" ({1}) kann nicht gelöscht werden.
naming.ruleNotValid=In der Regel dürfen nur die in [Hilfe] aufgeführten Konstruktionen verwendet werden.
must.have.javascipt=JavaScript muss im Browser aktiviert sein, um zu funktionieren.
objectsAlreadyAreInNewState=Der Übergang in den Status ''{0}'' kann für die folgenden Objekte der Klasse ''{1}'' nicht durchgeführt werden: {2}. Die Objekte befinden sich bereits im neuen Status.
quickForms.usedInAttributeParamRestrictions=Das Formular wird in benutzerdefinierten Aktionseinstellungen durch das Ereignis {0} verwendet, wobei die Typeinschränkungen nicht mit den Typen übereinstimmen, für die das Formular angewendet wird.
openObjectCard=Objektkarte öffnen
plannedVersion.PlannedVersionListener.module.off=Die Lizenz für das geplante Versionierungsmodul ''{0}'' fehlt oder ist ungültig. Der Mechanismus der geplanten Versionierung ist deaktiviert.
mobile.rest.mobile_list_not_available_error=Diese Liste ist nicht mehr verfügbar
mobile.rest.module_is_not_available_error=Die Lizenzdatei enthält nicht das Modul "Mobile Anwendung"
objectsTransitionDisabled=Der Übergang in den Status ''{0}'' kann für die folgenden Objekte der Klasse ''{1}'' nicht durchgeführt werden: {2}. Der Übergang ist gemäß den Workflow-Einstellungen nicht zulässig.
plannedVersion.bcp.AttributesAreBlockedOnEditMainBranch.error=Die Attribute des Objekts {0} sind für die Bearbeitung gesperrt, da das Objekt Versionen in den folgenden Zweigen hat, die sich im Sperrstatus befinden: {1}.
plannedVersion.PlannedVersionListener.module.on=Es wird eine Lizenzdatei geladen, in der das geplante Versionierungsmodul ''{0}'' aktiviert ist. Der Mechanismus der geplanten Versionierung ist aktiviert.
quickForms.import.invalidEditAttributeReference=Das Attribut "{0}" in der Klasse/dem Typ "{1}" verweist auf ein Schnellbearbeitungsformular mit dem Code "{2}", das innerhalb der Einschränkungen des Attributs nicht existiert. Die Referenz wird durch "null" ersetzt.
quickForms.usedInAttribute=Die Form wird in der Attributeinstellung {0} verwendet.
quickForms.usedInAttributeParamsRestrictions=Das Formular wird in benutzerdefinierten Aktionseinstellungen durch das Ereignis {0} verwendet, wobei die Typeinschränkungen nicht mit den Typen übereinstimmen, für die das Formular angewendet wird.
quickForms.usedInAttributeRestrictions=Das Formular wird in der Attributeinstellung {0} verwendet, in der die Typeinschränkungen nicht mit den Typen übereinstimmen, für die das Formular verwendet wird.
quickForms.import.invalidAddAttributeReference=Das Attribut "{0}" in der Klasse/dem Typ "{1}" bezieht sich auf ein Schnellhinzufügungsformular mit dem Code "{2}", das innerhalb der Beschränkungen des Attributs nicht existiert. Die Referenz wird durch "null" ersetzt.
mobile.deleteContents.cards=Objektkarten: {0}.
mobile.deleteContents.contentCards=Inhalt: {0}.
mobile.editFormNotFound=Das angeforderte Bearbeitungsformular "{0}" wird in den Einstellungen der mobilen App nicht gefunden.\nWählen Sie ein vorhandenes Bearbeitungsformular auf der Einstellungsseite der mobilen App aus oder geben Sie die richtige Adresse in die Browserleiste ein.
mobile.metainfo.error.securitySettings.customLoginModuleCode=Seite "Mobile App", Tab "Andere", Block "Sicherheit", ungültiger Wert des Parameters "Skriptmodul".
mobile.metainfo.error.securitySettings.customLoginFormCode=Seite "Mobile App", Tab "Andere", Block "Sicherheit", ungültiger Wert des Parameters "Login-Formular".
mobile.error.couldNotDeleteUsedContent=Die Objektliste "{0}" ({1}) kann nicht gelöscht werden. Die Liste wird in der Inhaltseinstellung "{2}" ({3}) auf der Objektkarte mit Code "{4}" verwendet.
mobile.error.couldNotHideContent=Das Element "{0}" kann nicht ausgeblendet werden, da es im Navigationsmenü verwendet wird.
mobile.error.defaultServiceCallIsRemoved=Es ist nicht möglich, ein Objekt über das ausgewählte Hinzufügungsformular zu erstellen. Der Typ ''{0}'' befindet sich im Archiv.
mobile.error.noDefaultServiceCallAssociation=Es ist keine Standardbindung für Sie definiert. Wenden Sie sich an Ihren Systemadministrator.
mobile.rest.unsupported_api=Diese API ist nicht mehr verfügbar. Bitte melden Sie sich erneut bei der Anwendung an.
mobile.rest.upload_file_size_exceed_error=Die Datei wurde noch nicht hochgeladen. Mögliche Gründe: Die Dateigröße ist größer als erlaubt, das Dateisystem des Servers ist nicht beschreibbar oder die Datei ist bösartig.
mobile.rest.view_card_permission_error=Sie haben keine Berechtigung, eine Objektkarte des Typs "{0} ({1})" anzuzeigen
mobile.rest.view_comment_add_form_permission_error=Sie haben keine Berechtigung, einen Kommentar abzugeben.
mobile.rest.view_edit_form_permission_error=Sie haben keine Rechte, um das Formular zur Objektbearbeitung "{0} ({1})" anzuzeigen
mobile.rest.view_files_permission_error=Sie haben keine Berechtigung zum Anzeigen von Dateien
mobile.rest.view_form_attrs_empty=Es gibt keine Attribute, die Sie bearbeiten können.
mobile.toolCaptionIsEmpty=Fehler in den Einstellungen der mobilen App: Es wurde kein Name für das Steuerelement in der Objektkarte mit dem Code "{0}" festgelegt.
moreThanOneEmployeeFailure=Sie können sich nicht anmelden. Wenn mehr als ein Konto gefunden wird, wenden Sie sich an Ihren Administrator.
plannedVersion.branch={0} "{1}"
mobile.transitionsNotFoundInClass=Die folgenden Übergänge zwischen den Status sind in der Klasse "{0}" nicht gefunden: {1}
mobile.useAttrRelObjectListAttrLink=Das Attribut ''{0}'' wird in der Klasse ''{1}'' nicht gefunden
plannedVersion.plannedVersions=Geplante Versionierung
plannedVersion.editDisabled=Die Eigenschaft "Erstellung von geplanten Versionen zulassen" kann nicht geändert werden.
plannedVersion.plannedVersionCantBeDisabled=Die Eigenschaft "Erstellung von geplanten Versionen zulassen" kann nicht deaktiviert werden.\n1. Objektversionen oder Umgebungsobjekte der Klasse "{0}" werden in verwendet: {1}\nSchließen Sie Klassenobjekte von allen Zweigen aus.
plannedVersion.plannedVersionCantBeEnabledReason=Im Planungsmodus ''{0}'' sind Klassenobjekte mit Objektversionen verknüpft: {1}
reloadableSessionFactoryBean.concurrentSchemaOptimizationError=Der Vorgang kann nicht ausgeführt werden, da der Optimierungsprozess der Datenbank läuft. Bitte versuchen Sie es in Kürze erneut.
onlyFileTypeParam=Nur Objekte der Klasse ''Datei'' können im Parameter ''@files'' übergeben werden.
mobile.newType=Neuer Typ
notAddAlreadyLinkFiles=Die ''{0}''-Dateien werden dem Formular nicht hinzugefügt, da sie bereits mit anderen Objekten verbunden sind.
notFileUuid=Die UUID einer nicht Datei wird angegeben: ''{0}''.
reports.sentEmail.endGeneration=Ende des Exports von Objekten der Klasse "{0}"
mobile.rest.file_error.unacceptable_extension=Ungültiger Dateityp
mobileQuickAction.takeTeam=mein Team zuweisen
responsibleStrategy.serviceResponsibleStrategy=Dienstleistungskurator
responsibleStrategy.teamLeaderResponsibleStrategy=Leiter des Teams der aktuellen verantwortlichen Person
responsibleStrategy.teamResponsibleStrategy.notSetError=Team der aktuellen verantwortlichen Person ist nicht angegeben
responsibleValidationOperation.notInTeamError=Mitarbeiter ''{0}'' ist kein Mitglied von Team ''{1}''
responsibleValidationOperation.permissionError=Sie haben keine Berechtigung, eine benannte Person zu benennen
responsibleStrategy.emptyResponsibleStrategy=Ohne einen verantwortlichen Mitarbeiter
responsibleStrategy.teamResponsibleStrategy=Team der aktuellen verantwortlichen Person
responsibleStrategy.solvedResponsibleStrategy=Wer hat das Ticket gelöst
responsibleStrategy.teamLeaderResponsibleStrategy.teamError=Team der aktuellen verantwortlichen Person ist nicht angegeben
responsibleStrategy.prevTeamResponsibleStrategy.notSetError=Team des bisherigen verantwortlichen Mitarbeiters ist nicht angegeben
responsibleStrategy.prevTeamResponsibleStrategy=Team des bisherigen verantwortlichen Mitarbeiters
responsibleStrategy.teamLeaderResponsibleStrategy.leaderError=Leiter des Teams der aktuellen verantwortlichen Person ist nicht angegeben
responsibleValidationOperation.statusError=In den ''{0}''-Statuseinstellungen ist die zuständige Klasse ''{1}'' nicht verfügbar
responsibleValidationOperation.teamEmptyError=Team ''{0}'' enthält keine aktiven Mitarbeiter
responsibleCurrentTeamEmployee=Zuordnung eines Mitarbeiters aus dem Team des aktuell zuständigen Mitarbeiters zum Verantwortlichen für das Objekt
responsibleStrategy.authorResponsibleStrategy.superUserError=Superuser kann nicht für ein Ticket verantwortlich sein
responsibleStrategy.currentResponsibleStrategy=Aktuell verantwortlicher Mitarbeiter
responsibleStrategy.prevResponsibleStrategy=Bisheriger verantwortlicher Mitarbeiter
responsibleValidationOperation.performerEmployeeError=Mitarbeiter ''{0}'' ist archiviert oder hat keine Lizenz
responsibleValidationOperation.performerError.employee=Mitarbeiter ist archiviert oder hat keine Lizenz
responsibleValidationOperation.performerError.team=Es gibt keine nicht archivierten Mitarbeiter-Führungskräfte im Team oder das Team ist archiviert
responsibleValidationOperation.notTeamError=Verantwortliche Team ist nicht angegeben
responsibleValidationOperation.performerTeamError=Es gibt keine nicht archivierten Mitarbeiter-Führungskräfte im Team oder das Team ist archiviert
ru.naumen.sd.bobjects.nomenclature.designs.IdDesign.HelpString=Eine Konstruktion wie '{'N'}' wird vom System durch eine Zahl ersetzt, die innerhalb des Zahlenzählers dieser Klasse eindeutig ist
websocket.notSetInitializerConnectionIsActive=Sie können keinen Initialisierer für eine aktive Verbindung mit dem Code "{0}" einstellen
scheduler.daily=Täglich.
workflowValidation.stateIncomingTransitionsError=Für ''{0}'' vom Status ''{1}'' gibt es Ausgänge, aber keine Eingänge
serviceTime.loadedExclusions=<br>Ausschlüsse geladen:
sms.isTooLarge=Textnachricht ist zu lang (mehr als {0} Zeichen)!
sms.notSent=Es wurde keine Textnachricht gesendet!
ru.naumen.soap.server.SoapServiceHelper.find_method_error=Maximal zulässige Anzahl zurückgegebener Objekte überschritten: {0}
sortingCriteria=Das Kriterium für die Relevanz der Suche
themeParamsInvalidNames=Die Datei enthält ungültige Parameter: {0}.
tempFileCanNotBeCopy=Das Kopieren von temporären Dateien ist nicht erlaubt.
scheduler.invalidStrategy=Die Strategie ist nicht korrekt eingegeben. Verwenden Sie from_start oder from_last_execution.
scriptConditions.betweenConditionError=Bedingte Operation "{0}". Die Anfangs- und Endwerte des Bereichs müssen vom gleichen Typ sein.
scheduler.invalidDateError=Das Datum darf nicht in der Vergangenheit liegen. Bitte ändern Sie das Datum.
scheduler.interruptingError=Beim Versuch, eine Aufgabe mit dem Namen "{0}" abzubrechen, ist ein Fehler aufgetreten.
scriptConditions.nullConditionError=Bedingte Operation "{0}". Der zu vergleichende Wert muss ungleich Null sein.
ru.naumen.soap.server.SoapServiceHelper.incorrect_request=Ungültige SOAP-Anfrage. Überprüfen Sie die Korrektheit des XML-Dokuments.
serviceTime.editServiceTimeHasDraft=Die Serviceklasse ''{0}'' kann nicht geändert werden. Die Serviceklasse hat bereits einen Entwurf, den Sie ändern können.
serviceTime.ignoredEclusions=<br>Bereits vorhandene Ausnahmen in der Serviceklasse werden ignoriert:
serviceTimeApi.errorOldServiceTime=Im Zustand ''Alte Version'' können Sie die Serviceklasse nicht bearbeiten
themeParamsCyclicDependency=Zirkuläre Abhängigkeit in Parametern gefunden: {0}.
themeParamsInvalidValues=Die Datei enthält Parameter mit ungültigen Werten: {0}
valueUnavailable=Wert nicht verfügbar
validation.integerNotNegative=Der Attributwert "{0}" muss eine nichtnegative ganze Zahl sein.
structuredObjectsViews.useInMenuItem=Die Struktur wird in den Einstellungen der Menüpunkte verwendet: {0}.
silentmode.connection.denied=Verbindung zu Server {0}:{1} kann nicht hergestellt werden, da der stille Modus aktiv ist.
root.window.Teams=Teams
secProfiles=Profile
services=Services
smtLessByNum=''{0}'' {1} weniger
ru.naumen.sd.bobjects.nomenclature.designs.DayIdDesign.HelpString=Eine Konstruktion wie '{'ND'}' wird vom System durch eine Zahl ersetzt, die innerhalb des Tages und innerhalb des Zahlenzählers dieser Klasse eindeutig ist
ru.naumen.sd.bobjects.nomenclature.designs.HourDesign.helpString=Ein Konstruktion wie '{'HH'}' wird vom System durch die aktuelle Stunde im 24-Stunden-Format ersetzt
ru.naumen.sd.bobjects.nomenclature.designs.MonthDesign.HelpString=Konstruktionen wie '{'MM'}' werden vom System für den laufenden Monat ersetzt
scheduler.cantEnable=Der Zeitplanname "{0}" kann nicht wiederaufgenommen werden. Bitte überprüfen Sie, ob der Name des Zeitplans korrekt ist.
scheduler.cantDisable=Der Zeitplan mit dem Namen "{0}" kann nicht pausiert werden. Bitte überprüfen Sie, ob der Name des Zeitplans korrekt ist.
scheduler.triggerChanged=Der Zeitplan wurde erfolgreich geändert.
scheduler.triggerDisabled=Der Zeitplan mit dem Namen {0} wurde erfolgreich ausgesetzt.
scheduler.triggerEnabled=Der Zeitplan mit dem Namen {0} wurde erfolgreich wiederaufgenommen.
scheduler.triggerName=Name des Zeitplans : {0}
securityPolicy.props.maxFailedTries=Maximale Anzahl von fehlgeschlagenen Anmeldeversuchen
serviceCall.solvedError={1} muss der Macher sein
scheduler.invalidPeriod=Der Zeitraum ist nicht korrekt eingegeben. Verwenden Sie täglich/monatlich/wöchentlich/jährlich.
scheduler.monthly=Monatlich.
websocket.configCreationFailed=Bei der Erstellung der Konfiguration ist ein Fehler aufgetreten.
shareViewAvailableFor.forUserGroups=Nur für ausgewählte Benutzergruppen
scheduler.period=Zeitplanperiode : {0}
scheduler.yearly=Jährlich.
scriptConditions.orConditionError=Bedingte Operation "{0}". Die Liste der Argumente muss ungleich Null sein und mehr als einen Wert haben.
ruleVersion=Version: {0}
scheduler.getStatusError=Bei der Statusaktualisierung ist ein Fehler aufgetreten.
scheduler.periodicTriggerPrefix=Die periodische Regel.
scriptPossibleValues.dispatchException=Beim Abrufen der Werte nach der Filterung des Attributs ist ein Fehler aufgetreten.
securityPolicy.props.enabled=Aktiviert
serviceCallValidationOperation.agsSettingYouShouldSelectAgreement=Im Feld "Vereinbarung/Service" muss nur "Vereinbarung" ausgewählt werden
serviceTimeApi.errorFolderServiceTime=Sie können ein Element, das ein Verzeichnisordner ist, nicht bearbeiten
tryAgain=Erneut anmelden
serviceCall=Anfrage
slmService=Service
team=Team
ru.naumen.soap.server.SoapServiceHelper.get_method_error=Der "uuid"-Tag enthält die UUID der Datei, nicht des Objekts
workflow.editAttrInStateForbidden=Die Bearbeitung des Attributs "{0}" des Objekts "{1}" im Status "{2}" ist verboten.
workflowValidation.closedTransitionsError=Für ''{0}'' gibt es keinen Eintrag zum Status ''{1}''
workflowValidation.registeredTransitionsError=Für ''{0}'' gibt es keinen Ausgang vom Status ''{1}''
smpsync.moduleTextIsNotAvailable=Der Modultext ''{0}'' ist nicht verfügbar
userEvent.cannotDeleted=Die Aktion zum Ereignis "{0}" kann aus den folgenden Gründen nicht gelöscht werden:
userEvent.isUsing=- Diese Ereignisaktion wird in den folgenden Steuerelementen verwendet:\n{0}
root.window.Issues=Abfragen
scheduler.concreteDateTriggerPrefix=Ausführung zu einem bestimmten Datum und einer bestimmten Uhrzeit.
securityPolicy.props.maxFailedTriesWithoutSuspesion=Anzahl der fehlgeschlagenen Anmeldeversuche ohne vorübergehende Kontosperrung
sms.sent=Es wurde eine Textnachricht gesendet!
validation.image.invalidVectorIcon=Die Datei enthält redundante Tags. Damit eine SVG-Datei in der Schnittstelle korrekt angezeigt wird, muss sie Tags aus dem Basissatz enthalten: {0}.
userEvent.changeTypeDenied=Die Ereignisaktionsart "{0}" kann nicht geändert werden. Diese Ereignisaktion wird in den folgenden Steuerelementen verwendet: {1}
websocket.notInModule=Eine Verbindung mit dieser Signatur ist nur möglich, wenn Methoden für diese Konfiguration im Modul vorhanden sind
scheduler.deletedMailsForTask=Alle unbearbeiteten E-Mails wurden für die Scheduler-Aufgabe {0} gelöscht.
scheduler.addRandomDelay=Für {0} ({1}) ist die Randomisierung der Startzeit von {2} Sekunden aktiviert
someFilesNotExists=Einige Dateien wurden dem Formular nicht hinzugefügt, weil sie im System fehlen.
serviceExistsListener.usesInScripts=Der Typ wird in dem berechneten Attribut der Klasse {0} verwendet.
structuredObjectsViews.useInContentTemplate=Die Struktur wird in den Einstellungen der Inhaltsvorlage verwendet: {0}.
userEvent.actionStarted=Aktion "{0}" wird ausgeführt
userEvent.changeFqnsDenied=Der Satz von Objekttypen für die Ereignisaktion "{0}" kann nicht geändert werden. Diese Ereignisaktion wird in den folgenden Steuerelementen verwendet: {1}
websocket.reloadError=Der Websocket konnte nicht neu geladen werden
russianLang=Russisch
scriptConditions.compareConditionError=Bedingte Operation "{0}". Der zu vergleichende Wert muss eine Zahl oder ein Datum sein.
scriptConditions.inConditionError=Bedingte Operation "{0}". Die Liste der Argumente darf nicht Null sein und muss mindestens einen Wert enthalten.
serviceTime.titleForLoadResult=Serviceklasse {0}
#ru.naumen.core.server.timer.TimerUtils.getTimerContext()
timerUtils.attrMustBeFilled=Das Attribut ''{0}'' muss ausgefüllt werden. Sie wird für die Berechnung verwendet: ''{1}''
timerUtils.attrsMustBeFilled=Die Attribute ''{0}'' müssen ausgefüllt werden. Sie werden für die Berechnung verwendet: ''{1}''
userEvent.info=Information
responsibleValidationOperation.transferError=Übertragung der Verantwortung von "{0}" auf "{1}" ist nicht zulässig
restServiceFailture=Geben Sie Ihren Benutzernamen und Ihr Passwort ein, um die Aktion zu bestätigen.
ru.naumen.metainfo.shared.elements.mail.SendMailParameters.resendDelay=Der Parameter 'Pause zwischen den Versuchen, eine E-Mail zu senden' muss größer sein als ''{0}'' Sek.
ru.naumen.sd.bobjects.nomenclature.designs.DayDesign.HelpString=Eine Konstruktion wie '{'DD'}' wird vom System für den aktuellen Tag des Monats ersetzt
ru.naumen.sd.bobjects.nomenclature.designs.MinuteDesign.HelpString=Konstruktionen wie '{'mm'}' werden vom System durch Minuten der aktuellen Uhrzeit ersetzt
ru.naumen.sd.bobjects.nomenclature.designs.RandomIdDesign.HelpString=Eine Konstruktion wie '{'RND'}' wird vom System durch eine innerhalb des Nummernzählers dieser Klasse eindeutige Zufallszahl ersetzt
ru.naumen.sd.bobjects.nomenclature.designs.ServiceCallIdTitleDesign.HelpString=Eine Konstruktion wie '{'SCID'}' wird vom System durch die Anfragekennung ersetzt (zur Verwendung im Namen der Anfrage)
ru.naumen.sd.bobjects.nomenclature.designs.SourceNameDesign.HelpString=Eine Konstruktion wie '{'SName'}' wird vom System durch den Namen der Quelle ersetzt
scheduler.suspendSuccess=Aufgabe-Scheduler-Operation erfolgreich beendet, MissfireHandler-Fehler erwartet.
scheduler.suspendingError=Beim Versuch, den Scheduler anzuhalten, ist ein Fehler aufgetreten.
scheduler.triggerIsConcreteDate=Ein Zeitplan mit einem bestimmten Datum.
scheduler.unknownTypeOfTrigger=Unbekannter Zeitplantyp.
scheduler.periodicTriggerError=Der Zeitplan mit der ID "{0}" ist nicht periodisch. Versuchen Sie, das Datum zu ändern.
scheduler.resumeSuccess=Der Aufgabe-Scheduler wurde erfolgreich wiederhergestellt.
scheduler.resumingError=Beim Versuch, den Scheduler wiederherzustellen, ist ein Fehler aufgetreten.
scheduler.successInterrupt=Die Scheduler-Aufgabe wurde erfolgreich abgebrochen. Es werden Thread Death-Fehler erwartet.
schemaOptimizationProcessAlreadyRun=Der Optimierungsprozess ist bereits im Gange.
schemaOptimizationProcessNotFound=Optimierungsprozess nicht gefunden. Bitte kontaktieren Sie den technischen Support.
serviceCall.wfprofile.2=Verknüpftes Workflow-Profil für die Verknüpfung von Massenanfrage und untergeordneten Anfragen
serviceCallValidationOperation.agsSettingYouShouldSelectService=Im Feld "Vereinbarung/Service" muss "Service" ausgewählt werden
serviceCallValidationOperation.serviceAggrementError=Service ''{1}'' ist nicht mit der Vereinbarung ''{0}'' verbunden
serviceCallValidationOperation.serviceMassProblemError=Die Bindung der Anfrage kann nicht geändert werden. Es sind {0} mit der Anfrage verbunden: {1}
serviceCallValidationOperation.serviceWithoutAggrementError=Die Anfrage hat einen Service ''{0}'', aber keine Vereinbarung abgeschlossen
serviceCallValidationOperation.slaveRemovedDifferFromMasterFromRemove=Die Anfrage ist untergeordnet und kann nicht getrennt von der Massenanfrage "{0}" aus dem Archiv wiederhergestellt werden.
serviceCallValidationOperation.serviceCallCaseError=Der Anfragetyp ''{1}'' entspricht nicht dem ausgewählten Service ''{0}''
validateAndFixChangeStateMarkers.wasRemoved=Der Übergang ''{0}'' zu ''{1}'' wurde aus den Rechtekennzeichnungen der Gruppe ''Statusänderung'' entfernt
#ru.naumen.metainfo.server.spi.dispatch.wf.WorkflowActionHandlerUtils
validateWorkflowWithoutState.description=Status ''{0}'' wird von zugehörigen Workflow-Profilen referenziert: {1}
validateWorkflowWithoutState.disable=Der Status ''{0}'' kann nicht ausgeschaltet werden.
valueMapIncorrectLinkedClassAndClassesValues=kann nicht erstellt werden: Der Wert ''{0}'' des Attributs ''linkedClasses'' ist nicht elterlich für die im Attribut ''linkedClasses'' angegebenen Typen (''Objects''): ''{1}''.
websocket.canNotAddHandlers=Hinzufügen neuer Nachrichten-Handler fehlgeschlagen
utils.find.sp.offsetError=Die Methode offset() kann nicht getrennt von der Methode limit() verwendet werden.
scheduler.concreteDateError=Der Zeitplan mit der ID "{0}" ist kein Zeitplan mit einem bestimmten Datum. Versuchen Sie, den Zeitraum zu ändern.
scheduler.date=Nächstes geplantes Fälligkeitsdatum: {0}
scheduler.deleteSchedulerTaskError=Beim Löschen der Scheduler-Aufgabe ist ein Fehler aufgetreten.
scheduler.deleteSchedulerTaskSuccess=Die Scheduler-Aufgabe wurde erfolgreich gelöscht.
securityPolicy.props.suspensionTime=Kontosperrzeit zwischen fehlgeschlagenen Anmeldeversuchen (Sek.)
#serviceCall.wfprofile.xml
serviceCall.wfprofile.1=Abfrage im Zusammenhang mit Massenabfrage
serviceCallValidationOperation.slaveRemovedDifferFromMasterToRemove=Die Anfrage ist untergeordnet und kann nicht getrennt von der Massenanfrage "{0}" archiviert werden.
serviceTime.allExclusionsIgnored=<br>Keine neuen Ausnahmen in der Datei für diese Serviceklasse gefunden
websocket.configCreationSuccess=Die Konfiguration mit dem Code "{0}" wurde erfolgreich hinzugefügt.
websocket.connectionFailed=Verbindung zu einem Websocket mit Code "{0}" fehlgeschlagen
websocket.moduleOrMethodNotFound=Die Verbindung zur Konfiguration konnte mit Code "{0}" nicht hergestellt werden. Es wurde kein Modul oder keine Methode mit diesem Namen gefunden.
websocket.noWebsocketUrlByCode=Abruf einer Websocket-URL mit Code "{0}" fehlgeschlagen
websocket.notDeletedConnectionIsActive=Die Konfiguration der Websocket-Verbindung mit Code "{0}" wird nicht gelöscht, da die Verbindung aktiv ist.
workflowValidation.stateOutgoingTransitionsError=Für den Status ''{0}'' hat ''{1}'' Eingänge, aber keine Ausgänge
setEmployeeLoginOperation.superuser=Superbenutzer ''{0}''
scheduler.isEnabled=Zeitplan ist aktiv : {0}
serviceTime.dateYearValidationError=Das Ausschlussdatum muss zwischen dem 02.01.1900 und dem 01.01.3000 liegen
setEmployeeLoginOperation.user=Benutzer
systemFileCanNotBeCopy=Systemdateien dürfen nicht kopiert werden.
systemJMSQueues=System-Warteschlangen
validation.image.fileIsNotVectorImage=Der Inhalt der Datei "{0}" ist kein Vektorbild. Akzeptierte Dateiformate: {1}.
websocket.notSendedConnectionIsNotActive=Die Nachricht wurde nicht gesendet. Es wurde keine Verbindung mit dem Code "{0}" gefunden.
scheduler.weekly=Wöchentlich.
ru.naumen.sd.bobjects.nomenclature.designs.LengthHelp=Eine Konstruktion wie '{'?N'}', wobei anstelle von "?" eine natürliche Zahl angegeben wird, wird vom System durch eine eindeutige Zahl innerhalb des Zählers von Zahlen dieser Klasse ersetzt, in der "?" - die Länge, bis zu der führende Nullen hinzugefügt werden. Bei Verwendung dieser Konstruktion für Attribute vom Typ Integer werden führende Nullen ignoriert, es sei denn, ihnen geht eine Zahl ungleich Null voraus.
ru.naumen.sd.bobjects.nomenclature.designs.SourceNumberDesign.HelpString=Eine Konstruktion wie '{'SNum'}' wird vom System durch die Quellennummer ersetzt
ru.naumen.sd.bobjects.nomenclature.designs.YearDesign.HelpString=Eine Konstruktionen wie '{'YYYY'}' werden vom System für das laufende Jahr ersetzt
showLeftMenuSettings=Linkes Menü anzeigen
# SMS
sms.authorIsTooLarge=Das Feld "Autor" ist zu groß (mehr als 20 Bytes)!
root.window.Filter=Filterung
administrationExportImport=Entladen/Beladen
homePage=Home-Seite
showHomePage=Taste "Home-Seite erstellen" anzeigen
gatewayRedirectionDisabled=Umleitung zum Gateway ist deaktiviert
bool=Boolean
boLink=Link zu BO
boLinks=Satz von Links zu Geschäftsobjekten
date=Datum
dateTime=Datum/Zeit
dtInterval=Zeitintervall
integer=Ganze Zahl
show=Anzeige
string=String
#ru.naumen.core.server.catalog.valuemap.SetValueMapItemRowSetOperation.validate()
double=Reelle Zahl
editing=Bearbeitung
eventService.eventCategory.pushPortalSendFailedPartially=Fehler bei der Push-Benachrichtigung auf dem Portal (Benutzer ist nicht eingeloggt)
eventService.pushPortalSendFailed=Die Portalbenachrichtigung wurde nicht gesendet ({0} - {1}{3}), weil {2}.{4}
eventService.pushPortalSendFailedPartially=Die Portalbenachrichtigung ({0} - {1}) wurde nicht an die folgenden Mitarbeiter gesendet: {2}, da die Benutzer derzeit nicht angemeldet sind.{3}
eventService.pushPortalSendSuccessful=An das Portal gesendete Benachrichtigung ({0} - {1}{3}).\nInformiert: {2}.{4}
favicon.convert.error=Die Quelldatei konnte nicht konvertiert werden. Wählen Sie ein anderes Bild.
richtext=Text im RTF-Format
eventActionCard=Karte Aktionen nach Ereignis
eventActionSettings=Ereignisaktionen
mobile.metainfo.error.securitySettings.loginType=Seite "Mobile Anwendung", Tab "Andere", Block "Sicherheit". Der Parameter "Authentifizierungstyp" (Login-Typ-Tag) enthält einen Wert, der in den aktuellen Systemeinstellungen nicht verfügbar ist - "{0}" ({1}).
maxOpenedBrowserTabsPerUser=Maximale Anzahl von gleichzeitig geöffneten Registerkarten für einen Benutzer
mobile.error.couldNotDeleteEditFormOnContents=Auf den Karten im Inhalt "Objektparameter [{0}].
mobile.error.couldNotDeleteEditForm=Das Element "{0}" kann nicht gelöscht werden, da es in der Aktion "Bearbeiten" verwendet wird:\n
mobile.error.couldNotDeleteEditFormOnCards=Auf Karten im Objektmenü [{0}]
userEvent.executionErrors.cannotApplicableForThisObjectType=Aktion ist auf Objekte dieses Typs nicht anwendbar
eventService.embeddedApplicationEventInitiated=Die Ausführung des benutzerdefinierten Ereignisses ''{0}'' ({1}) von der eingebetteten Anwendung ''{2}'' wird eingeleitet. Ort: {3}.
userEvent.executionErrors.noSpecifiedActionOrDisabled=es gibt keine angegebene Aktion im System oder die Aktion ist deaktiviert.
userEvent.executionErrors.cannotExecuteByReason=Die Aktion kann nicht ausgeführt werden, weil: {0}
importMetainfo.loadingImportConfigurationError=Einstellungen aus einer Datei können nicht geladen werden. Der Code der Synchronisationskonfiguration {0}({1}) stimmt mit dem Code der zu ladenden Synchronisationskonfiguration überein.
NavigationSettingsValidate.homePage.absentTabs=Das Home-Seite-Element ''{0}'' verweist auf die Tabs ''{1}'', die im System fehlen.
NavigationSettingsValidate.homePage.absentTitle=Fehlender "title"-Tag auf der Home-Seite mit UUID ''{0}''.
NavigationSettingsValidate.homePage.incorrectAttrCode=Home-Seite ''{0}'' wird nicht geladen: Das in den Home-Seite-Einstellungen angegebene Attribut mit dem Code ''{1}'' wurde nicht gefunden.
metainfo.DelAttributeActionHandler.attrUsedInHomePage=Das Attribut ''{0}'' kann nicht entfernt werden. Das Attribut wird in den Home-Seite-Einstellungen verwendet: {1}
Catalog.icons.title=Icons für Steuerelemente
metainfoValidation.homePage.useContentInHomePageError=Sie oder ihre Untertabs werden in den Home-Seite-Einstellungen verwendet: {0}
NavigationSettingsValidate.homePage.incorrectProfileCode=Home-Seite ''{0}'' wird nicht geladen: Das in den Home-Seite-Einstellungen angegebene Profil mit Code ''{1}'' wurde nicht gefunden.
NavigationSettingsValidate.homePage.absentCustomLink=Der "customLink"-Tag fehlt auf der Home-Seite mit der UUID ''{0}''.
NavigationSettingsValidate.homePage.absentProfiles=Auf der Home-Seite mit der UUID ''{0}'' fehlt der Tag "profiles".
NavigationSettingsValidate.homePage.absentReference=Fehlendes "reference"-Tag auf der Home-Seite ''{0}''.
NavigationSettingsValidate.homePage.absentReferenceClassFqn=Die Eigenschaft "fqn" im "reference"-Tag der Home-Seite ''{0}'' fehlt.
mobile.store.appstore=App Store
mobile.store.googleplay=Google Play
mobile.store.appgallery=AppGallery
mobile.store.rustore=RuStore
userEvent.executionErrors.permissionForEvent=Sie haben nicht die Berechtigung, die Aktion durchzuführen.
userEvent.executionErrors.log.warningForApplications={0}: Aktion zum Ereignis {1} wird für Objekte nicht ausgeführt: {2} Benutzer hat keine Rechte, um die Aktion auszuführen.
commonMentionSettings.fastLinkRightsEnabled=Verwendung eines Mechanismus zur Kontrolle der Benutzerrechte für Objekte, die zur Erwähnung verfügbar sind
NavigationSettingsValidate.homePage.incorrectUiTemplateCode=Home-Seite''{0}'' wird nicht geladen: Vorlage mit dem in den Home-Seite-Einstellungen angegebenen Code ''{1}'' wurde nicht gefunden.
NavigationSettingsValidate.homePage.incorrectTitle=Der "title"-Tag der Home-Seite mit der UUID ''{0}'' enthält einen falschen Wert ''{1}''. Der Tag-Wert sollte eine Zeichenkette mit einer Länge zwischen 1 und 64 Zeichen sein.
NavigationSettingsValidate.homePage.incorrectType=Das "type"-Tag der Home-Seite mit der UUID ''{0}'' enthält einen ungültigen Wert oder fehlt.
NavigationSettingsValidate.homePage.incorrectReferenceClassFqn=Die Eigenschaft "fqn" im "reference"-Tag der Home-Seite ''{0}'' enthält einen ungültigen Wert ''{1}''. Die Eigenschaft "fqn" muss den Code der Metaklasse enthalten.
NavigationSettingsValidate.homePage.nonUniqueHomePageItemCode=Die Eigenschaft "UUID" der Home-Seite enthält den falschen Wert "{0}". Der Wert der Eigenschaft muss innerhalb aller Home-Seite-Elemente eindeutig sein.
importMetainfo.type.fullReloadMetainfo=vollständig substituiert
importMetainfo.type.withoutFullReloadMetainfo=ohne vollständige Substitution
ScriptUtils.attributeTypeMustBeLinkBO=Der Typ des Link-Attributs sollte sein: "Link zu Geschäftsobjekt".
ScriptUtils.wrongLinkedClass=Es ist nicht möglich, einen vollständigen Identifikatorbaum der Geschäftsobjekthierarchie aufzubauen: Der übergebene FQN einer Klasse oder eine Zeichenkette mit ihrem FQN - ''{0}'' stimmt nicht mit dem Objekttyp ''{1}'' überein, auf den das Referenzattribut ''{2}'' in der Klasse ''{0}'' verweist.
eventService.eventCategory.loginSuccessfulFromMobile=Einloggen über die mobile Anwendung
eventService.eventCategory.logoutFromMobile=Abmelden aus dem System über die mobile Anwendung
eventService.loginSuccessfulFromMobile=Benutzer ''{0}'' hat sich über die mobile App angemeldet
eventService.logoutFromMobile=Benutzer ''{0}'' hat sich über die mobile App abgemeldet
fileStorage.activeStorageAbsence=Keine aktive Dateiablage
external.exception.emptyPostLogoutRedirectUri=Warnung! Die Standard-Benutzerumleitungslogik wird beim Abmelden ausgelöst, weil der Parameter postLogoutRedirectUri einen ungültigen Wert (leere Zeichenfolge) enthält.
importMetainfo.error.eventStorageRuleEventsMustBeFilled=Ereignisse müssen ausgefüllt werden
importMetainfo.DeletingSet=['{}'/'{}'] Satz löschen '{}'
importMetainfo.DeletingSetsDone=Sätze entfernt: '{}'
importMetainfo.loadingEventStorageRule=['{}'/'{}'] Laden der Speicherregel für das Ereignisprotokoll: '{}'
importMetainfo.loadingEventStorageRulesDone=Ereignisprotokoll-Speicherregeln geladen: '{}'
importMetainfo.loadingEventStorageRulesStart=Start des Ladens von Ereignisprotokoll-Speicherregeln
importMetainfo.loadingSet=['{}'/'{}'] Laden der Satzeinstellungen: '{}'
NavigationSettingsValidate.homePage.absentTab=Das Home-Seite-Element ''{0}'' verweist auf den Tab ''{1}'', der im System nicht vorhanden ist.
importMetainfo.DeletingEventStorageRule=['{}'/'{}'] Ereignisprotokoll-Speicherregel '{}' löschen
importMetainfo.loadingEventCleanerJobSettingsDone=Die Einstellungen für die Ereignisprotokollbereinigungsaufgabe werden geladen
importMetainfo.loadingEventCleanerJobSettingsStart=Start des Ladens der Einstellungen für die Ereignisprotokoll-Clearing-Aufgabe
importMetainfo.loadingSetsStart=Start der Beladung von Sätzen
importMetainfo.loadingSetsDone=Satzkonfigurationen geladen: '{}'
ImportMetainfoAction.uploadError=Die Metainformationen konnten aus technischen Gründen nicht geladen werden (siehe Anwendungsprotokoll).
ErrorDetails.relatedToWithAttribute=Der Wert des Attributs "{2}" kann nicht geändert werden, da {0} mit den folgenden Objekten verbunden ist: {1}.
ErrorDetails.relatedToWithAttribute_f=Der Wert des Attributs "{2}" kann nicht geändert werden, da {0} mit den folgenden Objekten verbunden ist: {1}.
DeleteMetaClassNavigationSettingsListener.usedClassInHomePage=Die Klasse/der Typ ''{0}'' kann nicht gelöscht werden. Sie oder ihre verschachtelten Typen werden in den Home-Seite-Einstellungen verwendet: {1}
Catalog.icons.description=Enthält Symbole, die für in der Anzeigeoberfläche platzierte Steuerelemente verwendet werden können (Tasten, Attribute wie "Verzeichniselementsatz" und "Verzeichniselement").
NavigationSettingsUIEventListener.profileIsOneOfInHomePageElements=Für das Profil wird eine Home-Seite definiert. Wenn das Profil gelöscht wird, wird es von den Home-Seite-Einstellungen ausgeschlossen. Die Home-Seite für Benutzer, die dieses Profil haben, ändert sich.
NavigationSettingsUIEventListener.profileUsedInHomePage=Das Profil muss mindestens eine absolute Rolle enthalten, wie sie in den Home-Seite-Einstellungen verwendet wird: {0}
Catalog.icons.vector.description=Enthält Symbole, die für Steuerelemente in der Anzeigeoberfläche verwendet werden können (Linkes Menü, Benutzerdefinierte Tasten, die sich an Attributwerten befinden).
CustomForm.changeState=Übergangsformular
DelAttributeGroupActionHandler.commentAttributeGroupDelete=Die Gruppe ist an der Einrichtung von Übergangselementen beteiligt: {0}
content.PropertyLists.visible=Der Inhalt "{0}" wird ausgeblendet, weil in seinen Einstellungen im Parameter "Attribut" der Parameter "{1}" verwendet wird, der nicht über den Link "Anlage"/"Link zu Geschäftsobjekt" mit diesem Objekt verbunden ist
NavigationSettingsUIEventListener.listTemplateUsedInHomePageElements=Die Vorlage ''{0}'' kann nicht gelöscht werden. Die Vorlage wird in den Home-Seite-Einstellungen verwendet: {1}
ImportMetainfoAction.uploadError.attributeValidation=Die Metainformation kann nicht geladen werden. Fehler bei der Validierung des Referenzattributs {0} ({1}): Die Objektklasse wurde in dem zu ladenden Attribut geändert.
exportLicense=Hochladen der Lizenz
metainfoValidation.commentShowAttributeAttachedInvalidCode=Das Tag "showAttributeAttachedFiles" im Content mit dem Code ''{2}'' in der Karte des Objekttyps/der Klasse ''{0}'' enthält den falschen Wert ''{3}''. Der Tag-Wert sollte der Code eines vorhandenen Attributs vom Typ ''File'' sein.
mobile.error.couldNotDeleteAddFormOnCards=Das Element "[''{0}'']" kann nicht gelöscht werden, da es auf Karten im Inhalt [{1}]
mobile.contents.actions.sort=Sortieren
customLoginPage.failedToApplyCustomLoginPage=Die Anwendung einer benutzerdefinierten Anmeldeseite ist fehlgeschlagen. Es wurde die System-Anmeldeseite verwendet.
PushMobile.authError=Autorisierungsfehler im FCM
leftMenu.validation.incorrectReferenceFqn=In den Einstellungen des Elements ist ein falscher Link zur Karte angegeben. Um den Artikel zu aktivieren, ändern Sie die Einstellungen.
PushMobile.failedToReceiveAccessToken=Firebase-Token konnte nicht abgerufen werden
PushMobile.serviceAccountFileReadingError=Fehler beim Lesen der JSON-Datei des Dienstkontos
leftMenu.validation.emptyTabs=Karten-Tab ist in den Elementeinstellungen nicht gefunden worden. Um das Element zu aktivieren, ändern Sie die Einstellungen.
