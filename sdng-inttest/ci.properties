#java version
smp.java.version=21

#ndap
ndap.version=2.3.9
ndap.security.version=1

#omnichannel
omnichannel.ncc-gateway.version=1.0
omnichannel.omnigate-gateway.version=1.1

#stages configuration
stages.layout.enabled=true
stages.layout.backupUrl=https://at-files.s3.sd.nau.run/layout/layout_test_41806_NSDAT-12703.backup
stages.cluster.enabled=true
stages.cluster.omnichannel.enabled=true
stages.cluster.smia.enabled=false
stages.cluster.frontend1.enabled=false
stages.cluster.frontend2.enabled=false
stages.cluster.backend1.enabled=false
stages.cluster.universal1.enabled=true
stages.cluster.universal2.enabled=true
stages.omnichannel.enabled=true
stages.omnichannel.gateway.config.version=23.1.3.3
stages.ndap.influx.enabled=true
stages.readonly.enabled=true
stages.spotbugs.enabled=true
stages.ui2.enabled=true

#docker tags
# v2/v3 - custom-built images with selenium/hub 4.8.0
docker.firefox.tag=141.0.3
docker.chrome.tag=129.0.6668.100
docker.node-firefox.tag=123.0-v3
docker.node-chrome.tag=122.0.6261.69-v2
docker.selenium-hub.tag=4.8.0-20230123

#dbaccess configuration
dbaccess.full.properties=true

#tomcat
tomcat.version=10.1.42
